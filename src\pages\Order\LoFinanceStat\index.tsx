import React, { useState, useRef, useEffect, useCallback } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import styles from './index.less';
import Util, { nf2 } from '@/util';

import { Button, Card, Col, Modal, Row, Space, Spin, Typography, message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSwitch } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import LoInvoiceCheckList from './components/LoInvoiceCheckList';
import LoPaymentList from './components/LoPaymentList';
import type { LoFinanceStatType } from '@/services/app/LexOffice/lo-finance';
import { exportLoFinanceStat } from '@/services/app/LexOffice/lo-finance';
import { getCustomerOrSupplierAccountNoACList } from '@/services/app/LexOffice/lo-finance';
import { getNextCustomerOrSupplier } from '@/services/app/LexOffice/lo-finance';
import { getLoFinanceStat } from '@/services/app/LexOffice/lo-finance';
import LoSusaMonthlyDiffList from './components/LoSusaMonthlyDiffList';
import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  FileExcelOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import useCustomerOrSupplier from './components/useCustomerOrSupplier';
import SProFormSelect from '@/components/SProFormSelect';
import SDatePicker from '@/components/SDatePicker';
import DoubleCheckCard from './components/DoubleCheckCard';

export type FilterFormType = { csId?: number; hideBalance?: boolean };

const LoFinanceStatPage: React.FC = () => {
  const searchFormRef = useRef<ProFormInstance>();

  const [customerOrSupplier, setCustomerOrSupplier] = useState<
    Partial<(API.Customer | API.Supplier) & { uid?: string }>
  >(Util.getSfValues('s_finance_stat'));

  const [loading, setLoading] = useState<boolean>(false);
  const [statData, setStatData] = useState<LoFinanceStatType>();
  const [refreshInvoiceList, setRefreshInvoiceList] = useState<number>(0);
  const [refreshPaymentList, setRefreshPaymentList] = useState<number>(0);

  const { getSCParam } = useCustomerOrSupplier(customerOrSupplier);

  /**
   * Load the financial stats values.
   */
  const loadFinanceStat = useCallback(() => {
    const scParams = getSCParam();
    Util.setSfValues('s_finance_stat_all', searchFormRef.current?.getFieldsValue());

    if (!scParams.customerId && !scParams.supplierId) {
      setStatData({} as any);
    } else {
      setLoading(true);

      getLoFinanceStat({ ...searchFormRef.current?.getFieldsValue(), ...scParams })
        .then((res) => {
          setStatData(res);
          if (res.book_max_ym) {
            // searchFormRef.current?.setFieldValue('book_max_ym', res.book_max_ym);
          }
        })
        .catch((err) => Util.error(err))
        .finally(() => setLoading(false));
    }
  }, [getSCParam]);

  const handleExportFinanceStat = useCallback(() => {
    const scParams = getSCParam();

    if (!scParams.customerId && !scParams.supplierId) {
    } else {
      const hide = message.loading('Exporting as Xls...', 0);
      exportLoFinanceStat({ ...searchFormRef.current?.getFieldsValue(), ...scParams })
        .then((res) => {
          console.log(res);
          setStatData((prev) => ({ ...prev, sqls: res.sqls } as any));
          message.success('Exported successfully.');
          window.open(`${API_URL}${res.file}`, '_blank');
        })
        .catch((err) => Util.error(err))
        .finally(() => hide());
    }
  }, [getSCParam]);

  useEffect(() => {
    loadFinanceStat();
  }, [loadFinanceStat]);

  /**
   * Next or previous customer/supplier
   *
   * @param dir -1 or 1
   */
  const hanldeNavigation = (dir: number) => {
    setLoading(true);
    getNextCustomerOrSupplier(dir, customerOrSupplier?.uid)
      .then((res) => {
        if (res) {
          setCustomerOrSupplier(res);
        }
      })
      .catch((err) => Util.error(err))
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    Util.setSfValues('s_finance_stat', customerOrSupplier);
  }, [customerOrSupplier]);

  /* const doubleCheckSum = statData
    ? statData.inv_sum - statData.creditnote_sum - statData.payment_sum - statData.balance_sum
    : 0; */
  const newSum = statData
    ? statData.book_last_balance +
      statData.inv_new_sum -
      statData.creditnote_new_sum -
      statData.payment_new_sum -
      statData.allocation_new_sum
    : 0;

  console.log('main', statData?.book_max_ym);

  return (
    <PageContainer className={styles.financeWrap}>
      <Row gutter={24}>
        <Col span={24}>
          <Card bordered={false}>
            <ProForm<FilterFormType>
              layout="inline"
              formRef={searchFormRef}
              isKeyPressSubmit
              className="search-form"
              initialValues={Util.getSfValues('s_finance_stat_all')}
              submitter={{
                render(props, dom) {
                  return (
                    <Space>
                      <Button
                        type="primary"
                        onClick={() => {
                          loadFinanceStat();
                          setRefreshInvoiceList((prev) => prev + 1);
                          setRefreshPaymentList((prev) => prev + 1);
                        }}
                      >
                        Search
                      </Button>
                      <Button
                        type="primary"
                        onClick={() => {
                          handleExportFinanceStat();
                        }}
                        icon={<FileExcelOutlined />}
                      >
                        Export
                      </Button>
                      <Button
                        type="dashed"
                        onClick={() => {
                          const sqls = statData?.sqls?.map((x) => x.query).join('\n\n');
                          Modal.info({
                            title: (
                              <Typography.Text copyable={{ text: sqls }}>
                                SQL result
                              </Typography.Text>
                            ),
                            content: statData?.sqls?.map((x, ind) => (
                              // eslint-disable-next-line react/no-array-index-key
                              <div key={ind} style={{ marginBottom: 16 }}>
                                {x.query} <br />
                                <Typography.Text copyable={{ text: x.query }}>
                                  {x.time} ms
                                </Typography.Text>
                              </div>
                            )),
                            width: '70%',
                            maskClosable: true,
                          });
                        }}
                        style={{ float: 'right' }}
                      >
                        SQL
                      </Button>
                    </Space>
                  );
                },
              }}
            >
              <ProFormSwitch
                label={'Prorofma'}
                name="orderconfirmationOnly"
                fieldProps={{
                  onChange: (e) => {
                    setRefreshInvoiceList((prev) => prev + 1);
                  },
                }}
              />

              <SProFormSelect
                name={'csId'}
                label="Customer / Supplier"
                showSearch={true}
                debounceTime={200}
                isNumericValue={false}
                onTabKeyCallback={(value) => {
                  setCustomerOrSupplier({ ...value, uid: value.value } as any);
                }}
                fieldProps={{
                  value: customerOrSupplier,
                  style: { width: 300 },
                  filterOption: false,
                  dropdownMatchSelectWidth: false,
                  labelInValue: true,
                  onChange: (value, option) => {
                    setCustomerOrSupplier(value);
                  },
                }}
                formItemProps={{ style: { marginBottom: 0 } }}
                request={async (params) => {
                  return getCustomerOrSupplierAccountNoACList(params) as any;
                }}
              />
              <div>
                <Button onClick={() => hanldeNavigation(-1)} icon={<ArrowLeftOutlined />} />
                <Button onClick={() => hanldeNavigation(1)} icon={<ArrowRightOutlined />} />
              </div>
              {/* <SProFormDateRange
                label="Date"
                style={{ marginLeft: 32 }}
                startDateName={'start_date'}
                endDateName="end_date"
                fieldItemProps2={{ fieldProps: { style: { margin: 0 } } }}
              />
               */}
              <SDatePicker
                label="Bookkeeping until"
                name="book_max_ym"
                placeholder="Month"
                width={120}
                fieldProps={{ picker: 'month' }}
                formItemProps={{ style: { marginLeft: 32 } }}
              />
              <ProFormSwitch
                name="book_max_ym_enabled"
                initialValue={true}
                fieldProps={{
                  title: 'Enable "bookkeeping until" filter?',
                  onChange: (e) => {
                    loadFinanceStat();
                    setRefreshInvoiceList((prev) => prev + 1);
                    setRefreshPaymentList((prev) => prev + 1);
                  },
                }}
              />
              <SProFormSelect
                name={'is_checkeds'}
                showSearch
                mode="multiple"
                tooltip="Marked?"
                fieldProps={{
                  style: { width: 150 },
                  dropdownMatchSelectWidth: false,
                }}
                options={[
                  { value: 0, label: 'Unmarked' },
                  { value: 1, label: 'Marked' },
                ]}
                initialValue={[0]}
              />
            </ProForm>
            <Typography />
            <Spin spinning={loading}>
              <div style={{ marginTop: 16 }}>
                {statData && (
                  <Space size={16} wrap align="start">
                    <Card
                      size="small"
                      title={
                        <label>
                          Bookkeeping until{' '}
                          <b>
                            {statData.book_max_ym_last_month
                              ? Util.dtToMY(statData.book_max_ym_last_month)
                              : 'N/A'}
                          </b>
                        </label>
                      }
                      className="card-desc"
                    >
                      <span>€ {nf2(statData.book_last_balance, true)}</span>
                    </Card>
                    <Card size="small" title={<label>New Invoice</label>} className="card-desc">
                      <span>€ {nf2(statData.inv_new_sum, true)}</span>
                    </Card>
                    <Card size="small" title={<label>New Creditnote</label>} className="card-desc">
                      <span>€ {nf2(statData.creditnote_new_sum, true)}</span>
                    </Card>
                    <Card size="small" title={<label>New Payment</label>} className="card-desc">
                      <span>€ {nf2(statData.payment_new_sum, true)}</span>
                    </Card>
                    <Card size="small" title={<label>New Allocation</label>} className="card-desc">
                      <span>€ {nf2(statData.allocation_new_sum, true)}</span>
                    </Card>
                    <Card size="small" title={<label>New Total</label>} className="card-desc">
                      <span>€ {nf2(newSum, true)}</span>
                    </Card>
                    {/* <Card
                      size="small"
                      title={
                        <label>
                          Double check{' '}
                          <InfoCircleOutlined
                            style={{ fontSize: 12 }}
                            title="Inv. Sum - Creditnote Sum - Payment Sum - Blance Sum"
                          />
                        </label>
                      }
                      className="card-desc"
                      style={{ marginLeft: 120 }}
                    >
                      <span className={doubleCheckSum != newSum ? 'c-orange' : ''}>
                        € {nf2(doubleCheckSum, true)}
                      </span>
                      <span style={{ color: 'grey', fontSize: 14, fontWeight: 'normal' }}>
                        {` = ${nf2(statData.inv_sum, true)} - ${nf2(
                          statData.creditnote_sum,
                          true,
                        )} - ${nf2(statData.payment_sum, true)} - ${nf2(
                          statData.balance_sum,
                          true,
                        )}`}
                      </span>
                    </Card> */}
                    <Card
                      size="small"
                      title={
                        <label>
                          Double check{' '}
                          <InfoCircleOutlined
                            style={{ fontSize: 12 }}
                            title="Inv. Sum - Creditnote Sum - Payment Sum - Allocation Sum"
                          />
                        </label>
                      }
                      style={{ marginLeft: 40 }}
                    >
                      <DoubleCheckCard data={statData} />
                    </Card>
                  </Space>
                )}
              </div>
            </Spin>
          </Card>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col xl={12} lg={12} md={12} sm={24} xs={24} style={{ marginTop: 24 }}>
          <Card bodyStyle={{ paddingTop: 0, paddingBottom: 0 }}>
            <LoInvoiceCheckList
              customerOrSupplier={customerOrSupplier}
              searchFormRef={searchFormRef}
              refreshTick={refreshInvoiceList}
              loadFinanceStat={loadFinanceStat}
            />
          </Card>
        </Col>
        <Col xl={12} lg={12} md={12} sm={24} xs={24} style={{ marginTop: 24 }}>
          <Card bodyStyle={{ paddingTop: 0, paddingBottom: 0 }}>
            {/* {statData && (
              <Space size={36}>
                <div>{statData.sum_payment}</div>
              </Space>
            )} */}
            <LoPaymentList
              customerOrSupplier={customerOrSupplier}
              loadFinanceStat={loadFinanceStat}
              searchFormRef={searchFormRef}
              refreshTick={refreshPaymentList}
              extraParams={{ includeCreditNote: true }}
            />
          </Card>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={24} style={{ marginTop: 24 }}>
          <Card>
            {customerOrSupplier && statData?.book_max_ym && (
              <LoSusaMonthlyDiffList
                customerOrSupplier={customerOrSupplier}
                searchFormRef={searchFormRef}
                book_max_ym={statData?.book_max_ym}
              />
            )}
          </Card>
        </Col>
      </Row>
    </PageContainer>
  );
};

export default LoFinanceStatPage;
