import { DictCode, DT_FORMAT_YMD } from '@/constants';
import { getUnassignedCalendarTaskList, updateTaskPartial } from '@/services/app/Task/task';
import type { DateRangeType } from '@/util';
import { sn } from '@/util';
import { ArrowLeftOutlined, ArrowRightOutlined, SwitcherOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Col, message, Modal, Row, Spin } from 'antd';
import _ from 'lodash';
import { isNumber } from 'lodash';
import moment from 'moment';
import { SetStateAction, useCallback, useEffect, useMemo, useState } from 'react';
import { useModel } from 'umi';
import type { onTaskClickType } from '.';
import TaskBoard, { TaskClickAction } from '.';
import Task from './components/Calendar/Task';
import UnassignedCalendarTasksList from './components/Calendar/UnassignedCalendarTasksListModal';
import WeekGrid from './components/Calendar/WeekGrid';
import UpdateForm from './components/UpdateForm';
import style from './index.less';

const TaskCalendarPage: React.FC = () => {
  const { loading, setLoading, data, setData, loadData } = useModel('task-calendar');

  const [weekCount, setWeekCount] = useState<number>(2);
  const [baseDate, setBaseDate] = useState<string>(
    moment().startOf('isoWeek').subtract(0, 'w').format(DT_FORMAT_YMD),
  );
  const [datesList, setDatesList] = useState<string[][]>([]);
  const [groupDateRange, setGroupRange] = useState<DateRangeType>();

  useEffect(() => {
    const tmp: string[][] = [];
    for (let w = 0; w < weekCount; w++) {
      const baseM = moment(baseDate).add(w, 'w');
      tmp[w] = [];
      for (let i = 0; i < 7; i++) {
        tmp[w].push(baseM.clone().add(i, 'd').format(DT_FORMAT_YMD));
      }
    }
    if (weekCount) {
      const gDt: DateRangeType = {};
      gDt.from = tmp[0][0];
      gDt.to = tmp[weekCount - 1][6];
      setGroupRange(gDt);
    }
    setDatesList(tmp);
  }, [weekCount, baseDate]);

  const loadTasksList = useCallback(() => {
    loadData(groupDateRange);
  }, [groupDateRange, loadData]);

  useEffect(() => {
    loadTasksList();
  }, [loadTasksList]);

  /**
   * Next or previous customer/supplier
   *
   * @param dir -1 or 1
   */
  const hanldeNavigation = useCallback(
    (dir: number) => {
      setBaseDate(moment(baseDate).add(dir, 'w').format(DT_FORMAT_YMD));
    },
    [baseDate],
  );

  // Create/Update form
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [createHeadModalVisible, handleCreateHeadModalVisible] = useState<boolean>(false);
  const [selectedDropInfo, setSelectedDropInfo] = useState<{
    droppableId?: string;
    index?: number;
  }>({});

  // email view
  const [openMailModal, setOpenMailModal] = useState<boolean>(false);
  const [selectedTask, setSelectedTask] = useState<API.Task>();

  // sub task group
  const [visibleSubGroup, setVisibleSubGroup] = useState<boolean>(false);
  const [subGroup, setSubGroup] = useState<API.TaskGroup>();

  // unassigned task modal
  const [visibleUnassignedOrderOutModal, setVisibleUnassignedOrderOutModal] =
    useState<boolean>(false);

  // unassigned tasks list
  const [unassignedTaskList, setUnassignedTaskList] = useState<API.Task[]>([]);

  const onTaskClick = useCallback<onTaskClickType>(
    async (
      task: API.Task,
      dropPanelId?: string,
      index?: number,
      e?: any,
      actionMode?: TaskClickAction,
      options?: any,
    ) => {
      console.log(task, index, 'block:', dropPanelId, actionMode, e, options);
      if (actionMode == TaskClickAction.delete) {
        /* if (!task.id || !(isNumber(index) && index >= 0)) return Promise.resolve();
        return deleteTask({ id: task.id })
          .then(() => {
            setTasks((prev) => {
              const newTasks = { ...prev };
              (newTasks[`${dropPanelId}`] || []).splice(index, 1);
              return newTasks;
            });
          })
          .catch((reason) => Util.error(reason)); */
      } else if (actionMode == TaskClickAction.createSubCallback) {
        /* return getTask(task.id, { with: 'taskBlocks', block_id: dropPanelId }).then((resTask) => {
          updateTask(resTask, dropPanelId, index);
        }); */
      } else if (actionMode == TaskClickAction.updatePartial) {
        const hide = message.loading('Updating task...', 0);
        return updateTaskPartial({ id: task.id, ...(options || {}) })
          .then(() => {
            // updateTask({ ...task, ...(options || {}) }, dropPanelId, index);
            loadTasksList();
          })
          .finally(() => hide());
      } else if (actionMode == TaskClickAction.openSubGroup) {
        setSubGroup(task.sub_task_groups?.[0]);
        setVisibleSubGroup(true);
        return Promise.resolve();
      } else if (actionMode == TaskClickAction.openEmailModal) {
        setSelectedTask(task);
        setSelectedDropInfo({ droppableId: dropPanelId, index });
        setOpenMailModal(true);
      } else {
        setSelectedDropInfo({ droppableId: dropPanelId, index });
        setSelectedTask(task);
        handleUpdateModalVisible(true);
        return Promise.resolve();
      }
    },
    [loadTasksList],
  );

  /** Memoized component */
  const navigationComp = useMemo(() => {
    console.log('Memoized component');
    return (
      <>
        <div style={{ marginTop: 12 }}>
          <Button onClick={() => hanldeNavigation(-1)} icon={<ArrowLeftOutlined />} />
          <Button onClick={() => hanldeNavigation(1)} icon={<ArrowRightOutlined />} />
        </div>
        <Button
          onClick={() => setVisibleUnassignedOrderOutModal(true)}
          type="primary"
          size="small"
          style={{ marginTop: 16 }}
        >
          Not assigned orders
        </Button>
      </>
    );
  }, [hanldeNavigation]);

  useEffect(() => {
    getUnassignedCalendarTaskList({ perPage: 10000 }, { order_no: 'descend' }).then((res) => {
      setUnassignedTaskList(res.data);
    });
  }, [baseDate]);

  return (
    <PageContainer title="Task Calendar" className={`${style.container}`}>
      <Spin spinning={loading}>
        {datesList.map((x, index) => (
          <WeekGrid
            key={x[0]}
            dates={x}
            baseDate={x[0]}
            navigationComp={index == 0 ? navigationComp : undefined}
            onTaskClick={onTaskClick}
          />
        ))}

        <Row style={{ marginBottom: 32 }} className="calendar" gutter={16}>
          {unassignedTaskList
            .filter(
              (x) =>
                x.ref_type == 'OrderOut' && (x.ref as API.OrderOut).type_code == 'ORD_OUT_LAGER',
            )
            .map((x) => (
              <Col key={`${x.id}`} span={5} className="calendar-date">
                <Task
                  index={0}
                  droppableId={''}
                  onTaskClick={onTaskClick}
                  task={{ ...x, ui_type: 'exact' }}
                />
              </Col>
            ))}
        </Row>
      </Spin>

      <UnassignedCalendarTasksList
        visible={visibleUnassignedOrderOutModal}
        setVisible={setVisibleUnassignedOrderOutModal}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={selectedTask}
        onSubmit={async (value) => {
          loadTasksList();
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
      />
      {/* <Modal
        title={
          <>
            <SwitcherOutlined className="c-blue4" /> Sub Task Board in #{subGroup?.task_id}
          </>
        }
        style={{ top: 20 }}
        bodyStyle={{ minHeight: 400, paddingTop: 0 }}
        width={'85%'}
        open={visibleSubGroup}
        footer={false}
        onCancel={() => {
          setVisibleSubGroup(false);
          setSubGroup(undefined);
        }}
      >
        {visibleSubGroup && subGroup?.id && <TaskBoard group_id={subGroup?.id} />}
      </Modal> */}
    </PageContainer>
  );
};

export default TaskCalendarPage;
