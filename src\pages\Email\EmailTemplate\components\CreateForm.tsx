import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ModalForm, ProFormText } from '@ant-design/pro-form';
import { addEmailTemplate } from '@/services/app/Email/email-template';
import { message } from 'antd';
import Util from '@/util';
import HtmlEditor from '@/components/HtmlEditor';
import MappingDefines from './MappingDefines';

const handleAdd = async (fields: API.EmailTemplate) => {
  const hide = message.loading('Adding...', 0);
  const data = { ...fields };
  try {
    await addEmailTemplate(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error('Adding failed, please try again!', error);
    return false;
  } finally {
    hide();
  }
};

export type CreateFormProps = {
  values?: Partial<API.EmailTemplate>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.EmailTemplate) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={
        <>
          <span>New Email template</span>
          <span>
            &nbsp;
            <MappingDefines />
          </span>
        </>
      }
      width="800px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      labelAlign="left"
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.EmailTemplate);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormText name="subject" label="Subject" />
      <ProForm.Item name={'text_html'} label="Body" style={{ width: '100%' }}>
        <HtmlEditor initialFocus enableTextModule height={400} />
      </ProForm.Item>
    </ModalForm>
  );
};

export default CreateForm;
