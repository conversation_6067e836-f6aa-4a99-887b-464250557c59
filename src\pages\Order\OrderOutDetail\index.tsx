import { Row, Col, Card, Spin, Tabs } from 'antd';
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { GridContent } from '@ant-design/pro-layout';

import { useModel, useParams } from 'umi';
import Util, { sn } from '@/util';
import useUrlParams from '@/hooks/useUrlParams';
import { getOrderOut } from '@/services/app/Order/order-out';
import { getTask } from '@/services/app/Task/task';
import TaskCommentList from '@/pages/TaskBoard/components/TaskCommentList';
import OrderOutInfoCard from './components/OrderOutInfoCard';
import OrderOutInvoice from './Invoice/OrderOutInvoice';
import { BarsOutlined, ProfileOutlined, UserOutlined, UserSwitchOutlined } from '@ant-design/icons';
import { DictCode } from '@/constants';
import FileDetail from './FileDetail/FileDetail';
import _ from 'lodash';
import SecFiles from './components/SecFiles';
import styles from './style.less';
import CrmData from './Crm/CrmData';
import Files from './Files/Files';
import WorkflowSection from './Workflow/WorkflowSection';
import WorkflowForSupplier from './Workflow/WorkflowForSupplier';
import { getSysWorkflow } from '@/services/app/Sys/sys-workflow';
import CustomerInfo from './CustomerInfo/CustomerInfo';

export const saveLastOrderTypeBySupplier = (supplier_id?: number, order_type?: string) => {
  Util.lsUpdate(`[orderOutDetail][sid${supplier_id}]`, order_type);
};

export const loadLastOrderTypeBySupplier = (supplier_id?: number) => {
  return Util.lsGet(`[orderOutDetail][sid${supplier_id}]`);
};

export const isEqualAddresses = (a?: API.Address, b?: API.Address) => {
  if (!a || !b) return false;

  for (const key in a) {
    if (
      key == 'full' ||
      key == 'firstname' ||
      key == 'lastname' ||
      key == 'email' ||
      key == 'telephone' ||
      key == 'fax' ||
      key == 'cust_no' ||
      key == 'company'
    ) {
    } else {
      continue;
    }

    if (a[key] != b[key]) return false;
  }
  return true;
};

const OrderOutDetail: React.FC = () => {
  // models
  const { setTaskContext } = useModel('task');
  const { getCode } = useModel('app-settings');

  // hooks
  const { order_no, lotus_notes_id } = useParams<any>();
  const { query } = useUrlParams();
  const task_id_query = query.get('task_id');

  // states
  const [orderOut, setOrderOut] = useState<API.OrderOut>();
  const [task, setTask] = useState<API.Task>();
  const [loadings, setLoadings] = useState<Record<string, boolean>>({
    orderOut: false,
    task: false,
  });

  const [___, setRefreshInvoiceList] = useState<number>(0);
  const [refreshTaskCommentsList, setRefreshTaskCommentsList] = useState<number>(0);

  // notes section
  // const [showNotes, setShowNotes] = useState<boolean>(true);

  // Get task by query param or orderOut loading
  const task_id = task_id_query ?? orderOut?.task?.id;

  const loadOrderOut = useCallback(async (no?: number, params?: any) => {
    const id = no ?? params?.lotus_notes_id;
    if (!id) return;
    return getOrderOut(id, {
      with: 'supplier,customer,categories,addresses,task,orderOutItems,orderOutHead,loOrderOutInvoicesFinalInvoices',
      ...params,
    })
      .then((res) => {
        setOrderOut(res);
      })
      .finally(() => setLoadings((prev) => ({ ...prev, orderOut: false })));
  }, []);

  const loadTask = useCallback((id?: number | string, params?: any) => {
    getTask(sn(id), params)
      .then((res) => {
        setTask(res);
      })
      .finally(() => setLoadings((prev) => ({ ...prev, task: true })));
  }, []);

  useEffect(() => {
    setTaskContext((prev) => ({
      ...prev,
      reloadInvoice: () => setRefreshInvoiceList((tick) => tick + 1),
    }));
  }, [setTaskContext]);

  useEffect(() => {
    if (!order_no && !lotus_notes_id) return;
    setLoadings((prev) => ({ ...prev, orderOut: true }));
    loadOrderOut(order_no, { lotus_notes_id, order_no });
  }, [order_no, loadOrderOut, lotus_notes_id]);

  useEffect(() => {
    if (!task_id) return;
    setLoadings((prev) => ({ ...prev, task: true }));
    loadTask(task_id);
  }, [task_id, loadTask]);

  const orderOutWorkflowId = getCode(DictCode.CODE_ORDER_OUT_WORKFLOW_ID)?.value;
  const orderOutSupplierWorkflowId = getCode(DictCode.CODE_ORDER_OUT_SUP_WORKFLOW_ID)?.value;

  // New Change v1.1
  /* const [supplierPallet, setSupplierPallet] = useState<string>('');

  useEffect(() => {
    setSupplierPallet(orderOut?.supplier_obj?.pallet || '');
  }, [orderOut?.supplier_obj?.pallet]); */

  const [orderOutWorkflow, setOrderOutWorkflow] = useState<API.SysWorkflow>();
  useEffect(() => {
    if (orderOutWorkflowId) {
      getSysWorkflow(orderOutWorkflowId, { with: 'sections' })
        .then((res) => {
          setOrderOutWorkflow(res);
        })
        .catch((err) => Util.error(err));
    } else {
      setOrderOutWorkflow(undefined);
    }
  }, [orderOutWorkflowId]);

  const sectionPerPositionMap = useMemo(() => {
    const map: Record<number, API.SysWorkflowSection> = {};
    if (orderOutWorkflow?.sections?.length) {
      orderOutWorkflow?.sections.reduce((prev, cur) => {
        prev[sn(cur.position)] = cur;
        return prev;
      }, map);
    }
    return map;
  }, [orderOutWorkflow?.sections]);

  return (
    <>
      <GridContent className={styles.orderOut}>
        <Row gutter={24} style={{ marginBottom: 24 }}>
          <Col xl={8} lg={8} md={12} sm={24} xs={24}>
            <Card title="" bordered={false} style={{ marginBottom: 24 }}>
              <Spin spinning={loadings.orderOut}>
                <OrderOutInfoCard
                  orderOut={orderOut}
                  setOrderOut={setOrderOut}
                  loadOrderOut={loadOrderOut}
                />
              </Spin>
            </Card>
            <Card title="" bordered={false}>
              <TaskCommentList
                initialValues={task}
                tableHeight={300}
                refreshTick={refreshTaskCommentsList}
              />
            </Card>
          </Col>
          <Col xl={16} lg={16} md={12} sm={24} xs={24}>
            <Tabs
              className="page-tabs"
              defaultActiveKey={Util.getSfValues('sf_order_out_tabs', 'workflow')}
              // defaultActiveKey="crm"
              type="card"
              size={'small'}
              onChange={(activeKey) => {
                Util.setSfValues('sf_order_out_tabs', activeKey);
              }}
              items={[
                {
                  label: (
                    <>
                      <BarsOutlined /> Details
                    </>
                  ),
                  key: 'details',
                  children: (
                    <Card title="" bordered={false} style={{ height: '100%' }}>
                      {orderOut?.order_no && <FileDetail orderNo={orderOut.order_no} />}
                    </Card>
                  ),
                },
                {
                  label: (
                    <>
                      <UserOutlined />
                      Supplier
                    </>
                  ),
                  key: 'supplierInfo',
                  children: (
                    <Row gutter={24} style={{ marginBottom: 24 }}>
                      <Col xl={24} lg={24} md={24} sm={24} xs={24}>
                        <Card
                          title=""
                          bordered={false}
                          style={{ minHeight: 350 }}
                          className="workflows"
                        >
                          {orderOut?.order_no &&
                            orderOutSupplierWorkflowId &&
                            orderOut?.supplier_obj && (
                              <WorkflowForSupplier
                                order_no={orderOut?.order_no}
                                orderOut={{
                                  type_code: orderOut?.type_code,
                                  categories: orderOut?.categories,
                                  supplier_id: orderOut?.supplier_id,
                                  supplier_obj: orderOut?.supplier_obj,
                                }}
                                workflow_id={orderOutSupplierWorkflowId}
                                setRefreshTaskCommentsList={setRefreshTaskCommentsList}
                              />
                            )}
                        </Card>
                      </Col>
                      {/* <Col
                        xl={8}
                        lg={8}
                        md={8}
                        sm={24}
                        xs={24}
                        style={{ padding: '24px 36px 0 0' }}
                      >
                        {
                          <>
                            <div
                              style={{
                                display: 'flex',
                                marginBottom: 12,
                                gap: 16,
                                height: 40,
                                alignItems: 'center',
                              }}
                            >
                              <span>Pallet: </span>
                              <Input
                                value={supplierPallet}
                                size="small"
                                onChange={(e) => setSupplierPallet(e.target.value)}
                              />
                              <Button
                                type="primary"
                                size="small"
                                onClick={() => {
                                  if (!orderOut?.supplier_id) return;
                                  updateSupplier({
                                    id: orderOut?.supplier_id,
                                    pallet: supplierPallet,
                                  })
                                    .then((res) => {
                                      message.success('Saved pallet successfully.');
                                      setOrderOut((prev) => ({
                                        ...prev,
                                        supplier_obj: {
                                          ...prev?.supplier_obj,
                                          pallet: supplierPallet,
                                        },
                                      }));
                                    })
                                    .catch((e) => Util.error(e));
                                }}
                              >
                                Save
                              </Button>
                            </div>

                            <Card
                              title="Notes of Supplier"
                              bordered={true}
                              size="small"
                              style={{ marginTop: 24 }}
                            >
                              <div
                                dangerouslySetInnerHTML={{
                                  __html: orderOut?.supplier_obj?.notes || '',
                                }}
                              />
                            </Card>
                            <Card
                              title="Notes of selected loading"
                              bordered={false}
                              size="small"
                              style={{ marginTop: 24 }}
                            >
                              <div
                                dangerouslySetInnerHTML={{
                                  __html: _.get(
                                    _.find(orderOut?.addresses, { address_type: 'loading' }),
                                    'detail',
                                    '',
                                  ),
                                }}
                              />
                            </Card>
                          </>
                        }
                      </Col> */}
                    </Row>
                  ),
                },
                {
                  label: (
                    <>
                      <UserOutlined />
                      Customer
                    </>
                  ),
                  key: 'customerInfo',
                  children: (
                    <>
                      <CustomerInfo
                        orderOut={{
                          order_no: orderOut?.order_no,
                          customer_id: orderOut?.customer_id,
                          customer_obj: orderOut?.customer_obj,
                        }}
                      />
                    </>
                  ),
                },
                /* {
                  label: (
                    <>
                      <SettingOutlined />
                      Workflow
                    </>
                  ),
                  key: 'workflow',
                  children: (
                    <Row gutter={24} style={{ marginBottom: 24 }}>
                      <Col xl={16} lg={16} md={16} sm={24} xs={24}>
                        <Card
                          title=""
                          bordered={false}
                          style={{ minHeight: 350 }}
                          className="workflows"
                        >
                          {orderOut?.order_no && orderOutWorkflowId && (
                            <WorkflowStepData
                              order_no={orderOut?.order_no}
                              orderOut={{
                                type_code: orderOut?.type_code,
                                categories: orderOut?.categories,
                              }}
                              workflow_id={orderOutWorkflowId}
                              setShowNotes={setShowNotes}
                              setRefreshTaskCommentsList={setRefreshTaskCommentsList}
                            />
                          )}
                        </Card>
                      </Col>
                      <Col
                        xl={8}
                        lg={8}
                        md={8}
                        sm={24}
                        xs={24}
                        style={{ padding: '24px 36px 0 0' }}
                      >
                        {orderOut?.order_no && (
                          <Card title="Extra files" bordered={false} size="small">
                            <SecFiles orderNo={orderOut?.order_no} />
                          </Card>
                        )}
                        {showNotes && (
                          <>
                            <Card
                              title="Notes of Supplier"
                              bordered={true}
                              size="small"
                              style={{ marginTop: 24 }}
                            >
                              <div
                                dangerouslySetInnerHTML={{
                                  __html: orderOut?.supplier_obj?.notes || '',
                                }}
                              />
                            </Card>
                            <Card
                              title="Notes of selected loading"
                              bordered={false}
                              size="small"
                              style={{ marginTop: 24 }}
                            >
                              <div
                                dangerouslySetInnerHTML={{
                                  __html: _.get(
                                    _.find(orderOut?.addresses, { address_type: 'loading' }),
                                    'detail',
                                    '',
                                  ),
                                }}
                              />
                            </Card>
                            <Divider />
                            <Card title="Notes of customer" bordered={true} size="small">
                              <div
                                dangerouslySetInnerHTML={{
                                  __html: orderOut?.customer_obj?.notes || '',
                                }}
                              />
                            </Card>
                          </>
                        )}
                      </Col>
                    </Row>
                  ),
                }, */
                {
                  label: sectionPerPositionMap[1]?.name || 'N/A',
                  key: 'workflow 1',
                  children: (
                    <Row gutter={24} style={{ marginBottom: 24 }}>
                      <Col xl={16} lg={16} md={16} sm={24} xs={24}>
                        <Card
                          title=""
                          bordered={false}
                          style={{ minHeight: 350 }}
                          className="workflows"
                        >
                          {orderOut?.order_no && orderOutWorkflowId && (
                            <WorkflowSection
                              workflow_id={orderOutWorkflowId}
                              position={1}
                              order_no={orderOut?.order_no}
                              orderOut={{
                                type_code: orderOut?.type_code,
                                categories: orderOut?.categories,
                              }}
                              setRefreshTaskCommentsList={setRefreshTaskCommentsList}
                            />
                          )}
                        </Card>
                      </Col>
                      <Col
                        xl={8}
                        lg={8}
                        md={8}
                        sm={24}
                        xs={24}
                        style={{ padding: '24px 36px 0 0' }}
                      >
                        {orderOut?.order_no && (
                          <Card title="Extra files" bordered={false} size="small">
                            <SecFiles orderNo={orderOut?.order_no} />
                          </Card>
                        )}
                      </Col>
                    </Row>
                  ),
                },
                {
                  label: (
                    <>
                      <UserSwitchOutlined />
                      CMR
                    </>
                  ),
                  key: 'cmr',
                  children: (
                    <Card title="" bordered={false} style={{ height: '100%' }}>
                      {orderOut?.order_no && (
                        <CrmData
                          order_no={orderOut?.order_no}
                          detail_file_id={orderOut?.detail_file_id}
                        >
                          <Row gutter={24} style={{ marginBottom: 24 }}>
                            <Col xl={16} lg={16} md={16} sm={24} xs={24}>
                              <Card
                                title={sectionPerPositionMap[2]?.name || 'N/A'}
                                bordered={false}
                                size="small"
                                style={{ minHeight: 250 }}
                                className="workflows"
                              >
                                {orderOut?.order_no && orderOutWorkflowId && (
                                  <WorkflowSection
                                    workflow_id={orderOutWorkflowId}
                                    position={2}
                                    order_no={orderOut?.order_no}
                                    orderOut={{
                                      type_code: orderOut?.type_code,
                                      categories: orderOut?.categories,
                                    }}
                                    setRefreshTaskCommentsList={setRefreshTaskCommentsList}
                                  />
                                )}
                              </Card>
                            </Col>
                          </Row>
                        </CrmData>
                      )}
                    </Card>
                  ),
                },
                {
                  label: sectionPerPositionMap[3]?.name || 'N/A',
                  key: 'workflow 3',
                  children: (
                    <Row gutter={24} style={{ marginBottom: 24 }}>
                      <Col xl={16} lg={16} md={16} sm={24} xs={24}>
                        <Card
                          title=""
                          bordered={false}
                          style={{ minHeight: 350 }}
                          className="workflows"
                        >
                          {orderOut?.order_no && orderOutWorkflowId && (
                            <WorkflowSection
                              workflow_id={orderOutWorkflowId}
                              position={3}
                              order_no={orderOut?.order_no}
                              orderOut={{
                                type_code: orderOut?.type_code,
                                categories: orderOut?.categories,
                              }}
                              setRefreshTaskCommentsList={setRefreshTaskCommentsList}
                            />
                          )}
                        </Card>
                      </Col>
                    </Row>
                  ),
                },
                {
                  label: (
                    <>
                      <ProfileOutlined />
                      Invoice
                    </>
                  ),
                  key: 'invoice',
                  children: (
                    <Card title="" bordered={false} style={{ height: '100%' }}>
                      <OrderOutInvoice
                        orderOut={orderOut}
                        loadOrderOut={loadOrderOut}
                        setOrderOut={setOrderOut}
                      />
                    </Card>
                  ),
                },
                {
                  label: sectionPerPositionMap[4]?.name || 'N/A',
                  key: 'workflow 4',
                  children: (
                    <Row gutter={24} style={{ marginBottom: 24 }}>
                      <Col xl={16} lg={16} md={16} sm={24} xs={24}>
                        <Card
                          title=""
                          bordered={false}
                          style={{ minHeight: 350 }}
                          className="workflows"
                        >
                          {orderOut?.order_no && orderOutWorkflowId && (
                            <WorkflowSection
                              workflow_id={orderOutWorkflowId}
                              position={4}
                              order_no={orderOut?.order_no}
                              orderOut={{
                                type_code: orderOut?.type_code,
                                categories: orderOut?.categories,
                              }}
                              setRefreshTaskCommentsList={setRefreshTaskCommentsList}
                            />
                          )}
                        </Card>
                      </Col>
                    </Row>
                  ),
                },
                {
                  label: <>Files</>,
                  key: 'files',
                  children: (
                    <Card
                      title=""
                      bordered={false}
                      bodyStyle={{ padding: '24px 0 0 0' }}
                      style={{ background: '#f0f2f5' }}
                      className="files-tab-wrap"
                    >
                      {orderOut?.order_no && <Files orderNo={orderOut?.order_no} />}
                    </Card>
                  ),
                },
                /* {
                  label: `Quotations`,
                  key: 'quotations',
                  children: (
                    <Card title="" bordered={false} style={{ height: '100%', display: 'flex' }}>
                      <Card title="" bordered={true} style={{ minHeight: 350, marginBottom: 24 }}>
                        <LoInvoiceList
                          hide_date
                          lo_type="quotation"
                          lo_status="open"
                          order_no={orderOut?.order_no}
                          title="Recent Quotation List"
                          refreshTick={refreshInvoiceList}
                        />
                      </Card>
                      <Card title="" bordered={true} style={{ minHeight: 350 }}>
                        <LoInvoiceList
                          hide_date
                          lo_type="quotation"
                          lo_status="draft"
                          order_no={orderOut?.order_no}
                          title="Recent Draft Quotation List"
                          refreshTick={refreshInvoiceList}
                        />
                        {/* {task && (
                            <SubTaskList
                              initialValues={task}
                              groupId={task?.group_id}
                              droppableId={`${task?.block_id}`}
                              tableTitle={
                                <>
                                  <SwitcherOutlined className="c-blue4" /> &nbsp;{`Sub tasks in #${task_id}`}
                                </>
                              }
                            />
                          )} * /}
                      </Card>
                      <Card title="" bordered={true} style={{ minHeight: 350, marginBottom: 24 }}>
                        <LoInvoiceList
                          hide_date
                          lo_type="order_confirmation"
                          lo_status="open"
                          order_no={orderOut?.order_no}
                          title="Recent Order Confirmation List"
                          refreshTick={refreshInvoiceList}
                        />
                      </Card>
                      <Card title="" bordered={true} style={{ minHeight: 350 }}>
                        <LoInvoiceList
                          hide_date
                          lo_type="order_confirmation"
                          lo_status="draft"
                          order_no={orderOut?.order_no}
                          title="Recent Draft Order Confirmation List"
                          refreshTick={refreshInvoiceList}
                        />
                      </Card>
                      <Card title="" bordered={true} style={{ minHeight: 350, marginBottom: 24 }}>
                        <LoInvoiceList
                          hide_date
                          lo_type="invoice"
                          lo_status="open"
                          order_no={orderOut?.order_no}
                          title="Recent Invoice List"
                          refreshTick={refreshInvoiceList}
                        />
                      </Card>
                      <Card title="" bordered={true} style={{ minHeight: 350 }}>
                        <LoInvoiceList
                          hide_date
                          lo_type="invoice"
                          lo_status="draft"
                          order_no={orderOut?.order_no}
                          title="Recent Draft Invoice List"
                          refreshTick={refreshInvoiceList}
                        />
                      </Card>
                    </Card>
                  ),
                }, */
              ]}
            />
          </Col>
        </Row>
      </GridContent>
    </>
  );
};

export default OrderOutDetail;
