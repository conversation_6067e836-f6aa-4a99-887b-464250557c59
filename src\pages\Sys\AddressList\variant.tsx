import { EditOutlined, PlusOutlined } from '@ant-design/icons';
import { But<PERSON>, message, Space } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React, { useState, useRef, useEffect } from 'react';
import { FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import UpdateForm from './components/UpdateForm';

import Util, { sn } from '@/util';
import CreateForm from './components/CreateForm';
import { getAddressList, deleteAddress, updateOrderAddress } from '@/services/app/Sys/address';
import SFooterToolbarExtra from '@/components/Table/SFooterToolbarExtra';
import BatchDeleteAction from '@/components/Table/BatchDeleteAction';

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.Address[]) => {
  const hide = message.loading('Deleting');
  if (!selectedRows) return true;

  try {
    await deleteAddress({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error('Delete failed, please try again!');
    return false;
  }
};

export const handleOrderOutAddressByCustomerAddress = async (
  address_type: API.AdressType,
  order_no?: number,
  by_address_id?: number,
) => {
  const hide = message.loading(`Updating ${address_type} address...`, 0);
  return updateOrderAddress(order_no, {
    address_type,
    by_address_id,
    type: 'OrderOut',
  })
    .catch((e) => Util.error(e))
    .finally(() => hide());
};

/**
 * Don't pass both of customer_id and supplier_id
 */
type AddressListVariantProps = {
  order_no?: number;
  customer_id?: number;
  supplier_id?: number;
  tableTitle?: string;
  refreshTick?: number;
  addressList?: API.Address[];
  isSupplier?: boolean;
  setAddressList?: Dispatch<SetStateAction<API.Address[]>>;
  loadOrderOut?: (no?: number) => void;
};

const AddressListVariant: React.FC<AddressListVariantProps> = ({
  order_no,
  customer_id,
  supplier_id,
  addressList,
  setAddressList,
  loadOrderOut,
  isSupplier,
  ...rest
}) => {
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.Address>();
  const [selectedRowsState, setSelectedRows] = useState<API.Address[]>([]);

  const columns: ProColumns<API.Address>[] = [
    {
      title: '',
      dataIndex: 'link',
      sorter: true,
      width: 60,
      valueType: 'option',
      render: (doms, record) => (
        <Space size={0}>
          {customer_id && (
            <Button
              size="small"
              type="link"
              title="Use as invoice address"
              onClick={() => {
                handleOrderOutAddressByCustomerAddress('invoice', order_no ?? 0, record.id).then(
                  (res) => loadOrderOut?.(order_no),
                );
              }}
            >
              Inv
            </Button>
          )}
          {customer_id && (
            <Button
              size="small"
              type="link"
              onClick={() => {
                handleOrderOutAddressByCustomerAddress('shipping', order_no ?? 0, record.id).then(
                  (res) => loadOrderOut?.(order_no),
                );
              }}
              title="Use as shipping address"
            >
              S
            </Button>
          )}
          {supplier_id && (
            <Button
              size="small"
              type="link"
              title="Use as loading address"
              onClick={() => {
                handleOrderOutAddressByCustomerAddress('loading', order_no ?? 0, record.id).then(
                  (res) => loadOrderOut?.(order_no),
                );
              }}
            >
              L
            </Button>
          )}
        </Space>
      ),
    },
    {
      title: 'Company',
      dataIndex: 'company',
      sorter: true,
      width: 180,
      render: (doms, record) => <>{record.company}</>,
    },
    {
      title: 'Cust No',
      dataIndex: 'cust_no',
      sorter: true,
      hideInTable: isSupplier && !!supplier_id,
      width: 90,
    },
    {
      title: 'Address',
      dataIndex: 'full',
      sorter: true,
      render: (doms, record) => record.full,
    },
    {
      title: '',
      dataIndex: 'option',
      valueType: 'option',
      width: 50,
      render: (_, record) => [
        <Button
          key="edit"
          type="link"
          size="small"
          icon={<EditOutlined />}
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        />,
      ],
    },
  ];

  useEffect(() => {
    if (sn(rest.refreshTick) > 0) {
      actionRef.current?.reload();
    }
  }, [rest.refreshTick]);

  // Default loading address
  const [defaultLoadingAddressList, setDefaultLoadingAddressList] = useState<API.Address[]>([]);

  useEffect(() => {
    if (isSupplier) {
      getAddressList({ isDefaultLoading: true }, {}, {})
        .then((res) => setDefaultLoadingAddressList(res.data))
        .catch((err) => Util.error(err));
    }
  }, [isSupplier]);

  return (
    <>
      <ProTable<API.Address, API.PageParams>
        headerTitle={rest.tableTitle ?? 'Address list'}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        sticky
        revalidateOnFocus={false}
        options={{ fullScreen: true, reload: true, density: false, setting: false }}
        search={false}
        cardProps={{ style: { padding: 0 }, bodyStyle: { padding: 0 } }}
        toolBarRender={() =>
          (isSupplier && supplier_id) || customer_id
            ? [
                <Button
                  type="primary"
                  key="primary"
                  size="small"
                  onClick={() => {
                    handleModalVisible(true);
                  }}
                >
                  <PlusOutlined /> New
                </Button>,
              ]
            : []
        }
        locale={{ emptyText: <></> }}
        pagination={{
          showSizeChanger: true,
          pageSize: 10,
          hideOnSinglePage: true,
        }}
        dataSource={addressList}
        onDataSourceChange={setAddressList}
        request={(params, sort, filter) =>
          getAddressList(
            { ...params, customer_id, supplier_id, with: 'includeSupplierAddress' },
            sort,
            filter,
          )
        }
        columns={columns}
        rowSelection={false}
        summary={(data) => {
          return (
            <ProTable.Summary fixed="top">
              {defaultLoadingAddressList.map((row) => (
                <ProTable.Summary.Row
                  key={row.id}
                  style={{ fontWeight: 'bold', borderBottom: '2px solid #666' }}
                >
                  {columns
                    .filter((c) => {
                      if (c.hideInTable) return false;
                      return true;
                    })
                    .map((c, index) => {
                      const tmpKey =
                        typeof c.dataIndex === 'string'
                          ? c.dataIndex
                          : ((c.dataIndex || []) as any[]).join('.');

                      let value: any = null;
                      if (tmpKey == 'link') {
                        if (isSupplier) {
                          value = (
                            <Button
                              size="small"
                              type="link"
                              title="Use as loading address"
                              onClick={() => {
                                handleOrderOutAddressByCustomerAddress(
                                  'loading',
                                  order_no ?? 0,
                                  row.id,
                                ).then((res) => loadOrderOut?.(order_no));
                              }}
                            >
                              L
                            </Button>
                          );
                        }
                      } else if (tmpKey == 'option') {
                      } else {
                        value = (row as any)[tmpKey] ?? null;
                      }

                      return (
                        <ProTable.Summary.Cell key={tmpKey} index={index}>
                          {value}
                        </ProTable.Summary.Cell>
                      );
                    })}
                </ProTable.Summary.Row>
              ))}
            </ProTable.Summary>
          );
        }}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <SFooterToolbarExtra
              title="address"
              actionRef={actionRef}
              selectedRowsState={selectedRowsState}
            />
          }
        >
          <BatchDeleteAction
            title="address"
            onConfirm={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          />
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        customer_id={customer_id}
        supplier_id={supplier_id}
        isSupplier={isSupplier}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        order_type={'OrderOut'}
        customer_id={customer_id}
        supplier_id={supplier_id}
        isSupplier={isSupplier}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
          loadOrderOut?.(order_no);
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
      />
    </>
  );
};

export default AddressListVariant;
