import { PlusOutlined } from '@ant-design/icons';
import { Button, message, Drawer } from 'antd';
import React, { useState, useRef } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import UpdateForm from './components/UpdateForm';

import Util from '@/util';
import CreateForm from './components/CreateForm';
import { getSysNotificationList, deleteSysNotification } from '@/services/app/Sys/notification';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.SysNotification[]) => {
  const hide = message.loading('Deleting');
  if (!selectedRows) return true;

  try {
    await deleteSysNotification({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error('Delete failed, please try again!', error);
    return false;
  }
};

const SysNotificationList: React.FC = () => {
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.SysNotification>();
  const [selectedRowsState, setSelectedRows] = useState<API.SysNotification[]>([]);

  const columns: ProColumns<API.SysNotification>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 40,
      align: 'center',
      fixed: 'left',
      render: (item, record, index, action) => {
        return (
          ((action?.pageInfo?.current ?? 1) - 1) *
            (action?.pageInfo?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION) +
          index +
          1
        );
      },
    },
    {
      title: 'Type',
      dataIndex: 'type',
      sorter: true,
      width: 100,
    },
    {
      title: 'Title',
      dataIndex: 'title',
      sorter: true,
      width: 200,
      ellipsis: true,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      sorter: true,
      align: 'center',
      width: 80,
      valueEnum: {
        1: { text: 'Enabled' },
        0: { text: '' },
      },
    },
    {
      title: 'Ref.',
      dataIndex: 'ref_id',
      sorter: true,
      align: 'center',
      width: 80,
    },
    {
      title: 'Description',
      dataIndex: 'desc',
      sorter: true,
      hideInForm: true,
      ellipsis: true,
      width: 250,
    },
    {
      title: 'Start date',
      sorter: true,
      dataIndex: 'start_date',
      valueType: 'date',
      search: false,
      align: 'center',
      width: 120,
      className: 'c-grey text-sm',
      render: (dom, record) => Util.dtToDMY(record.start_date),
    },
    {
      title: 'End date',
      sorter: true,
      dataIndex: 'end_date',
      valueType: 'dateTime',
      search: false,
      align: 'center',
      width: 120,
      render: (dom, record) => Util.dtToDMY(record.end_date),
    },
    {
      title: 'Created on',
      sorter: true,
      dataIndex: 'created_on',
      valueType: 'dateTime',
      defaultSortOrder: 'descend',
      search: false,
      width: 150,
      render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
    },
    {
      title: 'Updated on',
      sorter: true,
      dataIndex: 'updated_on',
      valueType: 'dateTime',
      search: false,
      width: 150,
      render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
    },
    {
      title: 'ID',
      dataIndex: 'id',
      colSize: 1,
      search: false,
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 120,
      fixed: 'right',
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          Edit
        </a>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.SysNotification, API.PageParams>
        headerTitle={'System notification list'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        scroll={{ x: 800 }}
        search={{
          labelWidth: 'auto',
          searchText: 'Search',
          span: 6,
          filterType: 'query',
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        pagination={{
          showSizeChanger: true,
          pageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        request={getSysNotificationList}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              chosen{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              SysNotification &nbsp;&nbsp;
            </div>
          }
        >
          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            Bacth deletion
          </Button>
          {/* <Button type="primary">batch approval</Button> */}
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions<API.SysNotification>
            column={2}
            title={currentRow?.id}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<API.SysNotification>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default SysNotificationList;
