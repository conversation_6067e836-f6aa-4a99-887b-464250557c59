/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/lo-voucherlist';


/** rule GET /api/lo-voucherlist */
export async function getLoInvoiceCheckList(
  params: API.PageParams,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.LoInvoiceCheck>> {
  return request<API.Result<API.LoInvoiceCheck>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** post POST /api/lo-voucherlist/ds-latest */
export async function dsLatestLoVoucherList(data?: any, options?: { [key: string]: any }) {
  return request<API.BaseResult>(`${urlPrefix}/ds-latest`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  }).then(res => res.message);
}

/** post POST /api/lo-voucherlist/ds */
export async function dsLoVoucherList(data?: any, options?: { [key: string]: any }) {
  return request<API.BaseResult>(`${urlPrefix}/ds`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  }).then(res => res.message);
}

/** put PUT /api/lo-voucherlist/update/{id} */
export async function updateLoInvoiceCheck(
  id?: string,
  data?: Partial<API.LoInvoiceCheck>,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject<API.LoInvoiceCheck>>(`${urlPrefix}/update/${id}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then(res => res.message);
}

/** 
 * Add order outs into Invoice Check entry
 * 
 * PUT /api/lo-voucherlist/addOrderOut/{id} 
 */
export async function addOrderOutToLoInvoiceCheck(
  id?: string,
  data?: { orderOutNos: number[] },
  options?: { [key: string]: any },
) {
  return request<API.ResultObject<API.LoInvoiceCheck>>(`${urlPrefix}/addOrderOut/${id}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then(res => res.message);
}

/** 
 * remove order outs into Invoice Check entry
 * PUT /api/lo-voucherlist/addOrderOut/{id} 
 */
export async function deleteOrderOutToLoInvoiceCheck(
  id?: string,
  data?: { orderOutNos: number[] },
  options?: { [key: string]: any },
) {
  return request<API.ResultObject<API.LoInvoiceCheck>>(`${urlPrefix}/deleteOrderOut/${id}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then(res => res.message);
}