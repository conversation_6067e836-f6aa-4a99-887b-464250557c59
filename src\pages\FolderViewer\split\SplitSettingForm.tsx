import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { Col, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import Util from '@/util';
import { splitFile } from '@/services/app/FolderViewer/folder-viewer';

export type SplitOptionType = {
  split_pages?: string;
  delete_pages?: string;
};

export type SplitSettingFormProps = {
  fmFile?: API.FmFile;
  onSubmit?: (res: any) => void;
};

const SplitSettingForm: React.FC<SplitSettingFormProps> = (props) => {
  const formRef = useRef<ProFormInstance<SplitOptionType>>();

  const { fmFile, onSubmit } = props;

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    formRef.current?.resetFields();
  }, [fmFile?.id]);

  return (
    <ProForm<SplitOptionType>
      layout="vertical"
      labelAlign="left"
      formRef={formRef}
      colon={false}
      grid
      disabled={!fmFile?.id || fmFile.isDir}
      onFinish={async (values) => {
        setLoading(true);

        const hide = message.loading('Splitting a PDF...', 0);
        setLoading(true);
        return splitFile(fmFile?.id, {
          ...values,
        })
          .then((res) => {
            message.success('Splitted successfully.');
            onSubmit?.(res);
          })
          .catch(Util.error)
          .finally(() => {
            hide();
            setLoading(false);
          });
      }}
      submitter={{
        searchConfig: { resetText: 'Cancel', submitText: 'Split' },
        resetButtonProps: { disabled: loading, loading: loading, style: { display: 'none' } },
        submitButtonProps: { disabled: loading || !fmFile?.id || fmFile.isDir, loading: loading },
      }}
    >
      <Col span={12}>
        <ProFormTextArea
          name="split_pages"
          label="Split Pages"
          fieldProps={{ rows: 5 }}
          help="You can separate documents by ';' or new line. e.g.1-4;6,8\n\r2-4,12\n9,10,11"
        />
      </Col>
      <Col span={12}>
        <ProFormText
          name="delete_pages"
          label="Delete Pages"
          help="Specify page numbers to be excluded by ',' and '-'. e.g.3,4-6"
        />
      </Col>
    </ProForm>
  );
};

export default SplitSettingForm;
