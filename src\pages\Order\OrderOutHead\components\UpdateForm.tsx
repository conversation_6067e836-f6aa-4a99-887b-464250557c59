import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { Button, message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText, ModalForm } from '@ant-design/pro-form';
import { updateOrderOutHead } from '@/services/app/Order/order-out-head';
import Util from '@/util';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateOrderOutHead(fields);
    hide();
    message.success('Update is successful');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

type FormValueType = Partial<API.OrderOutHead>;

export type UpdateFormProps = {
  initialValues?: Partial<API.OrderOutHead>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.OrderOutHead) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef && formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, props.initialValues]);

  return (
    <ModalForm
      title={'Update Order Out Head'}
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      initialValues={props.initialValues || {}}
      formRef={formRef}
      submitter={{
        render(__, dom) {
          return [
            // eslint-disable-next-line react/jsx-no-undef
            <Button key="cancel" onClick={() => props.handleModalVisible(false)}>
              {' '}
              Cancel
            </Button>,
            <Button key="submit" type="primary" onClick={() => formRef.current?.submit()}>
              Update
            </Button>,
          ];
        },
      }}
      onFinish={async (value) => {
        const success = await handleUpdate({
          ...value,
          id: props.initialValues?.id,
        });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormText
        required
        rules={[
          {
            required: true,
            message: 'Name is required',
          },
        ]}
        width="md"
        name="name"
        label="Name"
      />
    </ModalForm>
  );
};

export default UpdateForm;
