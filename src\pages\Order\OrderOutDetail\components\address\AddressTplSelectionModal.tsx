import { SUPPLIER_ID_WHC } from '@/constants';
import { getAddressACList } from '@/services/app/Sys/address';
import { getSysAddressXlsTplACList } from '@/services/app/Sys/sys-address-xls-tpl';
import { ExportOutlined } from '@ant-design/icons';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormSelect } from '@ant-design/pro-form';
import { Button } from 'antd';
import type { DefaultOptionType } from 'antd/lib/select';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useState } from 'react';
import { useRef } from 'react';

export type AddressTplSelectionModalProps = {
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit: (formData: API.Address) => Promise<boolean | void>;
  supplierId: number;
  addressId: number;
};

const AddressTplSelectionModal: React.FC<AddressTplSelectionModalProps> = (props) => {
  const { supplierId, addressId, modalVisible } = props;
  const formRef = useRef<ProFormInstance>();

  const [addresses, setAddresses] = useState<DefaultOptionType[]>([]);

  useEffect(() => {
    if (!modalVisible || !supplierId) return;
    getAddressACList({
      with: 'withSuppliers',
      supplier_ids: [supplierId, SUPPLIER_ID_WHC],
    }).then((res) =>
      setAddresses(
        res.map((address) => ({
          value: `${address.suppliers?.[0].id}_${address.id}`,
          label: `${address?.suppliers?.[0]?.name} - ${address.company ?? '-'} - ${address.full}`,
        })),
      ),
    );
  }, [modalVisible, supplierId]);

  return (
    <ModalForm
      title="Select XLS template & Export address"
      width="500px"
      visible={modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ flex: '0 0 120px' }}
      formRef={formRef}
      onFinish={async (value) => {
        props.handleModalVisible(false);
        if (props.onSubmit) props.onSubmit(value);
      }}
      submitter={{
        render(__, doms) {
          return (
            <>
              <Button
                type="primary"
                icon={<ExportOutlined />}
                onClick={() => formRef.current?.submit()}
              >
                Export
              </Button>
            </>
          );
        },
      }}
    >
      <ProFormSelect
        required
        rules={[
          {
            required: true,
            message: 'Address is required',
          },
        ]}
        name="supplierIdAndAddressId"
        showSearch
        label="Address"
        mode="single"
        initialValue={`${supplierId}_${addressId}`}
        fieldProps={{ dropdownMatchSelectWidth: false }}
        options={addresses}
      />
      <ProFormSelect
        required
        rules={[
          {
            required: true,
            message: 'XLS Template is required',
          },
        ]}
        name="tpl_id"
        showSearch
        label="XLS Template"
        mode="single"
        request={(params) => getSysAddressXlsTplACList(params)}
      />
    </ModalForm>
  );
};
export default AddressTplSelectionModal;
