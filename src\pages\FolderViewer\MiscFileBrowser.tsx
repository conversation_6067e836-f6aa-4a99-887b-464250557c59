import { useEffect, useMemo, useRef, useState } from 'react';
import Util, { isImageFile, isPdfFile } from '@/util';
import { PageContainer } from '@ant-design/pro-layout';

import { Col, Row, Image, Card, Space, Button, message } from 'antd';

import { Viewer, Worker } from '@react-pdf-viewer/core';
import '@react-pdf-viewer/core/lib/styles/index.css';

import { toolbarPlugin } from '@react-pdf-viewer/toolbar';
import '@react-pdf-viewer/toolbar/lib/styles/index.css';

import type { ToolbarSlot, TransformToolbarSlot } from '@react-pdf-viewer/toolbar';

import { SelectionMode } from '@react-pdf-viewer/selection-mode';
import { getFilesInMiscPath } from '@/services/app/FolderViewer/misc-file-assign';
import { getFileUrlInMiscFolder } from '@/services/app/FolderViewer/folder-viewer';
import { ArrowLeftOutlined, ArrowRightOutlined, ReloadOutlined } from '@ant-design/icons';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormDependency, ProFormRadio, ProFormSelect } from '@ant-design/pro-form';
import { getOrderOutACList } from '@/services/app/Order/order-out';
import { getOrderInACList } from '@/services/app/Order/order-in';
import { moveToAssignFile } from '@/services/app/file';
import MiscFilesList from './components/MiscFilesList';
import SFileIcon from '@/components/SFileIcon';
import WorkflowScanFile from './components/WorkflowScanFile';
import { DictCode, LS_TOKEN_NAME } from '@/constants';
import { useModel } from 'umi';

type NavigationActionType = 'prev' | 'next';

export const FileCategorySuffixOptions = [
  { value: 'ab', label: 'AB' },
  { value: 'cmr', label: 'CMR' },
  { value: 'invoice', label: 'Invoice' },
  { value: 'freight', label: 'Freight' },
  { value: 'other2', label: 'Other' },
];

export const FileCategorySuffixOptionsKv: Record<string, any> = FileCategorySuffixOptions.reduce(
  (prev: any, current) => {
    prev[current.value] = current.label;
    return prev;
  },
  {},
);

export type FormValueType = {
  cat1?: 'OrderIn' | 'OrderOut' | 'Misc';
  order_out_order_no?: number;
  order_in_order_no?: number;
  cat2?: string;
};

const MiscFileBrowser: React.FC = () => {
  const { getCodeValue } = useModel('app-settings');
  const [resourceLoading, setResourceLoading] = useState(false);

  const formRef = useRef<ProFormInstance<FormValueType>>();

  const [files, setFiles] = useState<API.FmFile[]>([]);
  const [selectedFmFile, setSelectedFmFile] = useState<API.FmFile | null>(null);
  const [formValues, setFormValues] = useState<FormValueType>();

  const [childrenRefreshTick, setChildrenRefreshTick] = useState<number>(0);

  const handleGetFiles = async (newSelectIndex?: number) => {
    setResourceLoading(true);
    return getFilesInMiscPath({})
      .then((fileList) => {
        setFiles(fileList);
        if (typeof newSelectIndex == 'number' && newSelectIndex >= 0) {
          setSelectedFmFile(
            fileList[newSelectIndex] ?? fileList[newSelectIndex - 1] ?? fileList[0] ?? null,
          );
        } else {
          setSelectedFmFile((prev) => (prev ? prev : fileList[0] ?? null));
        }
        return fileList;
      })
      .catch((error) => {
        Util.error(error);
      })
      .finally(() => setResourceLoading(false));
  };

  useEffect(() => {
    handleGetFiles();
  }, []);

  // const defaultLayoutPluginInstance = defaultLayoutPlugin();
  const defaultLayoutPluginInstance = toolbarPlugin({
    searchPlugin: {
      keyword: 'PDF',
    },
    selectionModePlugin: {
      selectionMode: SelectionMode.Hand,
    },
    fullScreenPlugin: {},
    openPlugin: {},
  });

  const { Toolbar, renderDefaultToolbar } = defaultLayoutPluginInstance;

  const transform: TransformToolbarSlot = (slot: ToolbarSlot) => ({
    ...slot,
    // These slots will be empty
    Download: () => <></>,
    Open: () => <></>,
    // EnterFullScreen: () => <></>,
    SwitchTheme: () => <></>,
  });

  const fileIcon = useMemo(() => {
    if (selectedFmFile) {
      return <SFileIcon fileName={selectedFmFile.name} />;
    }
    return null;
  }, [selectedFmFile]);

  const selectedIndex = useMemo(() => {
    if (!selectedFmFile?.id) return -1;
    const ind = files.findIndex((x) => x.id == selectedFmFile?.id);
    return ind;
  }, [files, selectedFmFile?.id]);

  // console.log('selected file: ', selectedFmFile?.id, selectedFmFile?.name, selectedIndex);

  const handleNavigation = (action: NavigationActionType) => {
    const ind = selectedIndex;
    if (ind >= 0) {
      const dir = action == 'next' ? 1 : -1;
      const nextInd = (ind + dir + files.length) % files.length;

      if (action == 'next' && nextInd < ind) {
        message.info('No next file!', 2);
      } else if (action == 'prev' && nextInd > ind) {
        message.info('No prev file!', 2);
      } else {
        setSelectedFmFile(files[nextInd] ?? null);
      }
    }
  };

  /* // todo
  useEffect(() => {
    const tmp = {cat1: 'OrderOut', order_out_order_no: 1000};
    setFormValues(tmp);
    formRef.current?.setFieldsValue(tmp);
  }, []); */

  const ccCopy = getCodeValue(DictCode.CODE_FM_MISC_FILE_COPY_MODE);

  return (
    <PageContainer>
      <Row gutter={24} wrap={false}>
        <Col span={12} style={{ paddingBottom: 32 }}>
          <Worker workerUrl={`${(window as any).routerBase}pdf.worker.min.js`}>
            {selectedFmFile?.id &&
              !selectedFmFile?.isDir &&
              isPdfFile(selectedFmFile?.name || '') && (
                <div
                  style={{
                    height: '750px',
                    width: '100%',
                    marginLeft: 'auto',
                    marginRight: 'auto',
                  }}
                >
                  <Toolbar>{renderDefaultToolbar(transform)}</Toolbar>
                  <Viewer
                    fileUrl={getFileUrlInMiscFolder(selectedFmFile?.id)}
                    plugins={[defaultLayoutPluginInstance]}
                    httpHeaders={{ Authorization: `Bearer ${localStorage.getItem(LS_TOKEN_NAME)}` }}
                  />
                </div>
              )}
          </Worker>
          {selectedFmFile?.id &&
            !selectedFmFile?.isDir &&
            isImageFile(selectedFmFile?.name || '') && (
              <Image src={getFileUrlInMiscFolder(selectedFmFile?.id)} />
            )}
        </Col>
        <Col span={12}>
          <Card
            title={
              <Space>
                <Space size={0}>
                  <Button
                    size="small"
                    title="Previous File"
                    onClick={() => handleNavigation('prev')}
                    icon={<ArrowLeftOutlined />}
                    disabled={selectedIndex == 0 || selectedIndex < 0}
                  />
                  <Button
                    size="small"
                    title="Next File"
                    onClick={() => handleNavigation('next')}
                    icon={<ArrowRightOutlined />}
                    disabled={selectedIndex == files.length - 1}
                  />
                </Space>
                {selectedFmFile && (
                  <div>
                    {fileIcon} {selectedFmFile.name}
                  </div>
                )}
              </Space>
            }
            extra={
              <Space>
                {selectedFmFile && selectedIndex >= 0 ? (
                  <div>
                    {selectedIndex + 1} / {files.length} files
                  </div>
                ) : null}
                <Button
                  icon={<ReloadOutlined />}
                  size="small"
                  type="link"
                  onClick={() => handleGetFiles(selectedIndex)}
                />
              </Space>
            }
            size="small"
            loading={resourceLoading}
          >
            <ProForm
              formRef={formRef}
              layout="horizontal"
              labelCol={{ style: { width: 120 } }}
              labelAlign="left"
              size="small"
              submitter={{
                searchConfig: { submitText: ccCopy ? 'Move to Folders' : 'Move' },
                submitButtonProps: {
                  title: ccCopy ? 'Move to Folders and assign file' : 'Move and assign file',
                  disabled: !selectedFmFile?.id,
                },
              }}
              onValuesChange={(changedValues, values) => {
                // console.log(' Form:', changedValues, values);
                // console.log('    Form2:', formRef.current?.getFieldsValue());
                setFormValues((prev) => ({ ...prev, ...values }));
              }}
              onFinish={(values) => {
                console.log(values);
                if (!selectedFmFile) {
                  message.info('File is not selected. Please reload page and try again.');
                  return Promise.reject(false);
                }
                const hide = message.loading('Moving file to assign...', 0);
                return moveToAssignFile({ ...values, serverFileId: selectedFmFile?.id })
                  .then((res) => {
                    message.success('Assigned successfully. Reloading files now...', 3);

                    formRef.current?.resetFields();

                    setFormValues(undefined);

                    handleGetFiles(selectedIndex).then((res2) => {
                      // setChildrenRefreshTick((prev) => prev + 1);
                    });
                  })
                  .catch(Util.error)
                  .finally(hide);
              }}
            >
              <ProFormRadio.Group
                label="Category 1"
                name="cat1"
                options={[
                  { value: 'OrderOut', label: 'Order Out' },
                  { value: 'OrderIn', label: 'Order In' },
                  { value: 'Other', label: 'Other' },
                ]}
                required
                rules={[
                  {
                    required: true,
                    message: 'Category 1 is required',
                  },
                ]}
                fieldProps={{
                  buttonStyle: 'solid',
                  optionType: 'button',
                  size: 'small',
                  /* onChange(e) {
                    setFormValues((prev) => ({
                      ...prev,
                      cat1: e.target.value,
                    }));
                  }, */
                }}
              />
              <ProFormDependency name={['cat1']}>
                {(depValues) => {
                  if (depValues.cat1 == 'OrderOut') {
                    return (
                      <ProFormSelect
                        name="order_out_order_no"
                        label="Order No"
                        width="md"
                        showSearch
                        allowClear
                        request={(params) => {
                          return getOrderOutACList(params) as any;
                        }}
                        fieldProps={{
                          dropdownMatchSelectWidth: false,
                          /* onChange(value, option) {
                            setFormValues((prev) => ({
                              ...prev,
                              order_out_order_no: value,
                            }));
                          }, */
                        }}
                      />
                    );
                  } else if (depValues.cat1 == 'OrderIn') {
                    return (
                      <ProFormSelect
                        name="order_in_order_no"
                        label="Order No"
                        width="md"
                        showSearch
                        allowClear
                        request={(params) => {
                          return getOrderInACList(params) as any;
                        }}
                        fieldProps={{
                          dropdownMatchSelectWidth: false,
                          /* onChange(value, option) {
                            setFormValues((prev) => ({
                              ...prev,
                              order_in_order_no: value,                              
                            }));
                          }, */
                        }}
                      />
                    );
                  }
                  return null;
                }}
              </ProFormDependency>
              <ProFormRadio.Group
                label="Category 2"
                name="cat2"
                options={FileCategorySuffixOptions}
                required
                rules={[
                  {
                    required: true,
                    message: 'Category 2 is required',
                  },
                ]}
                fieldProps={{ buttonStyle: 'solid', optionType: 'button', size: 'small' }}
              />
            </ProForm>
          </Card>
          <Row style={{ marginTop: 24 }} gutter={24}>
            <Col span={12}>
              <Card
                title={'Workflow'}
                size="small"
                // loading={resourceLoading}
              >
                <WorkflowScanFile formValues={formValues} />
              </Card>
            </Col>
            <Col span={12}>
              <Card title={'Files'} size="small">
                {FileCategorySuffixOptions.map((x) => {
                  const props: FormValueType & { category?: string } = { cat1: formValues?.cat1 };
                  if (props.cat1 == 'OrderOut') {
                    props.order_out_order_no = formValues?.order_out_order_no;
                    props.category = 'order-out-' + x.value;
                  } else if (props.cat1 == 'OrderIn') {
                    props.order_in_order_no = formValues?.order_in_order_no;
                    props.category = 'order-in-' + x.value;
                  } else {
                    props.category = 'misc-' + x.value;
                  }

                  return (
                    <MiscFilesList
                      key={x.value}
                      {...props}
                      tableTitle={x.label}
                      refreshTick={childrenRefreshTick}
                    />
                  );
                })}
              </Card>
            </Col>
          </Row>
        </Col>
      </Row>
    </PageContainer>
  );
};

export default MiscFileBrowser;
