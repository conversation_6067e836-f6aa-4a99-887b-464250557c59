import React, { useRef, useEffect, useMemo, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumnType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { sn } from '@/util';
import {
  getOrderOutDetailList,
  updateOrderOutXlsDetailLocal,
} from '@/services/app/Order/order-out-detail';
import _ from 'lodash';
import { Col, Row } from 'antd';
import EditableCell from '@/components/EditableCell';
import Button from 'antd/lib/button/button';
import HsCodeSelectionModal from './HsCodeSelectionModal';

type OrderOutFileDataListProps = {
  orderOut: Partial<API.OrderOut>;

  pageSize?: number;
  refreshTick?: number;
  hidePageContainer?: boolean;
};

const OrderOutFileDataList: React.FC<OrderOutFileDataListProps> = (props) => {
  const { orderOut } = props;
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.OrderOutDetail>();
  const [openHsCodeSelectionModal, setOpenHsCodeSelectionModal] = useState<boolean>(false);

  useEffect(() => {
    if (sn(props.refreshTick) > 0) {
      actionRef.current?.reload();
    }
  }, [props.refreshTick]);

  useEffect(() => {
    if (props.orderOut?.order_no) {
      actionRef.current?.reload();
    }
  }, [props.orderOut]);

  const fileSettings: API.FileSettingSettings = orderOut.detail_file?.file_setting?.settings || {};

  const columns: ProColumnType<API.OrderOutDetail>[] = useMemo(() => {
    const newColumns: ProColumnType<API.OrderOutDetail>[] = [
      {
        title: 'HS Code',
        sorter: true,
        dataIndex: ['hs_code'],
        valueType: 'text',
        search: false,
        tooltip: 'Click to edit.',
        width: 150,
        render: (dom, record: API.OrderOutDetail) => {
          return (
            <Row wrap={false}>
              <Col flex="auto">
                <EditableCell
                  dataType="text"
                  defaultValue={record.hs_code}
                  triggerUpdate={function (
                    value: any,
                    cancelEdit?: (() => void) | undefined,
                  ): Promise<void> {
                    cancelEdit?.();
                    return updateOrderOutXlsDetailLocal(sn(record.id), { hs_code: value }).then(
                      (res) => actionRef.current?.reload(),
                    );
                  }}
                >
                  {record.hs_code}
                </EditableCell>
              </Col>
              <Col flex="0 0 30px">
                <Button
                  type="link"
                  size="small"
                  onClick={() => {
                    setCurrentRow(record);
                    setOpenHsCodeSelectionModal(true);
                  }}
                >
                  S
                </Button>
              </Col>
            </Row>
          );
        },
      },
    ];
    if (!fileSettings?.col2field2) return newColumns;

    for (const col of Object.keys(fileSettings?.col2field2)) {
      const dbField = fileSettings?.col2field2[col];
      if (dbField) {
        let align = 'left';
        let width = 150;

        switch (`${dbField.db_field_type}`) {
          case 'date':
            align = 'center';
            break;
          case 'int':
          case 'bigint':
          case 'double':
          case 'numeric':
            align = 'right';
            break;
          case 'text':
          case 'varchar':
          case 'longtext':
            break;
        }
        const dataIndex = `${dbField.db_field}`;

        const meta = fileSettings?.col2meta?.[col];
        if (meta) {
          width = (meta.width ?? width) + 16;
        }

        newColumns.push({
          title: meta?.header ?? dbField.db_field_comment ?? dbField.db_field,
          sorter: true,
          dataIndex: [dataIndex],
          valueType: 'text',
          align: align as any,
          search: false,
          ellipsis: true,
          width: width,
          render(dom, record: any) {
            switch (dbField.db_field_type) {
              case 'date':
                return Util.dtToDMY(record?.[dataIndex]);
              case 'int':
              case 'bigint':
                return Util.numberFormat(record?.[dataIndex]);
              case 'double':
              case 'numeric':
                return Util.numberFormat(record?.[dataIndex], false, 3, true);
              default:
                return dom;
            }
          },
          onCell: (record: any) => {
            const style: any = {};
            if (meta) {
              // eslint-disable-next-line @typescript-eslint/dot-notation
              style['color'] = meta.color;
              // eslint-disable-next-line @typescript-eslint/dot-notation
              style['backgroundColor'] = meta.fillColor;
            }

            return {
              style: {
                ...style,
              },
            };
          },
        });
      }
    }
    return newColumns;
  }, [fileSettings?.col2field2, fileSettings?.col2meta]);

  const renderBody = () => (
    <>
      <ProTable<API.OrderOutDetail, API.PageParams>
        headerTitle={'Imported data list'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true, density: false, search: false }}
        size="small"
        search={false}
        toolBarRender={() => []}
        pagination={{
          hideOnSinglePage: true,
          showSizeChanger: false,
        }}
        scroll={{ x: 800 }}
        cardProps={{
          bodyStyle: { padding: 0 },
        }}
        locale={{ emptyText: <></> }}
        request={(params, sort, filter) => {
          return getOrderOutDetailList(
            { ...params, pageSize: props?.pageSize ?? 20, order_no: props.orderOut.order_no },
            { ...sort },
            filter,
          ) as any;
        }}
        columns={columns as any}
        columnEmptyText=""
      />
      <HsCodeSelectionModal
        order_no={orderOut.order_no}
        supplier_id={orderOut.supplier_id}
        record={currentRow as any}
        modalVisible={openHsCodeSelectionModal}
        handleModalVisible={setOpenHsCodeSelectionModal}
      />
    </>
  );

  return props.hidePageContainer ? renderBody() : <PageContainer>{renderBody()}</PageContainer>;
};

export default OrderOutFileDataList;
