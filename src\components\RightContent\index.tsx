import { Space } from 'antd';
import React, { useEffect } from 'react';
// @ts-ignore
import { useModel } from 'umi';
import Avatar from './AvatarDropdown';
import HeaderSearch from '../HeaderSearch';
import styles from './index.less';
import { getAppSettings } from '@/services/app/api';
import Util from '@/util';
export type SiderTheme = 'light' | 'dark';

const GlobalHeaderRight: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const { setAppSettings } = useModel('app-settings');

  useEffect(() => {
    if (initialState?.currentUser) {
      getAppSettings()
        .then((res) => setAppSettings(res))
        .catch((e) => Util.error('Failed to fetch app settings. Please try to reload a page!'));
    }
  }, [initialState?.currentUser, setAppSettings]);

  if (!initialState || !initialState.settings) {
    return null;
  }

  const { navTheme, layout } = initialState.settings;
  let className = styles.right;

  if ((navTheme === 'dark' && layout === 'top') || layout === 'mix') {
    className = `${styles.right}  ${styles.dark}`;
  }

  return (
    <Space className={className}>
      <HeaderSearch
        className={`${styles.action} ${styles.search}`}
        placeholder="Search"
        defaultValue=""
        options={
          [
            /* {
            label: <a href="https://umijs.org/zh/guide/umi-ui.html">umi ui</a>,
            value: 'umi ui',
          },
          {
            label: <a href="next.ant.design">Ant Design</a>,
            value: 'Ant Design',
          },
          {
            label: <a href="https://protable.ant.design/">Pro Table</a>,
            value: 'Pro Table',
          },
          {
            label: <a href="https://prolayout.ant.design/">Pro Layout</a>,
            value: 'Pro Layout',
          }, */
          ]
        } // onSearch={value => {
        //   console.log('input', value);
        // }}
      />
      {/* <span
        className={styles.action}
        onClick={() => {
          window.open('https://pro.ant.design/docs/getting-started');
        }}
      >
        <QuestionCircleOutlined />
      </span> */}
      {/* <DownSyncDropdown /> */}
      <Avatar />
    </Space>
  );
};

export default GlobalHeaderRight;
