import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import { Button, message, Modal } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import { linkEmailToTask } from '@/services/app/Task/task';
import Util, { sn } from '@/util';
import _ from 'lodash';
import { LinkOutlined, SelectOutlined } from '@ant-design/icons';
import { getEmailList } from '@/services/app/Email/email';

export type FormValueType = Partial<API.Task>;

export type EmailLinkingFormProps = {
  task?: Partial<API.Task>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  reloadParent?: (newEmail?: API.Email) => void;
};

const EmailLinkingForm: React.FC<EmailLinkingFormProps> = (props) => {
  const { task } = props;

  // form
  const formRef = useRef<ProFormInstance>();

  return (
    <Modal
      title={
        <>
          <SelectOutlined className="c-blue" /> Link an Email into task
          {`#${task?.id} - ${task?.title}`}
        </>
      }
      width="500px"
      open={props.modalVisible}
      onCancel={() => props.handleModalVisible(false)}
      footer={
        <Button
          type="primary"
          onClick={() => {
            formRef.current?.validateFields([['email_id']]).then((values) => {
              const hide = message.loading('Linking the Email to selected tasks...', 0);
              linkEmailToTask(values?.email_id, { task_ids: [sn(task?.id)] })
                .then((newEmail) => {
                  message.success('Linked successfully.');
                  props.handleModalVisible(false);
                  props.reloadParent?.(newEmail);
                })
                .catch((e) => Util.error(e))
                .finally(() => hide());
            });
          }}
          icon={<LinkOutlined />}
        >
          Link
        </Button>
      }
    >
      <ProForm formRef={formRef} layout="vertical" submitter={false}>
        <ProFormSelect
          name="email_id"
          label="Exsiting Email"
          required
          showSearch
          mode="single"
          debounceTime={200}
          rules={[
            {
              required: true,
              message: 'Email is required',
            },
          ]}
          request={(params) => {
            const newParams = {
              ...params,
              pageSize: 200,
            };
            return getEmailList(newParams).then((res) =>
              res.data.map((email) => ({
                value: email.id,
                label: `${email.subject} - ${email.text_plain}`,
              })),
            );
          }}
        />
      </ProForm>
    </Modal>
  );
};

export default EmailLinkingForm;
