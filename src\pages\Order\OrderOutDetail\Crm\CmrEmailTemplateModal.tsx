import HtmlEditor from '@/components/HtmlEditor';
import { DictCode } from '@/constants';
import { getEmailTplACList } from '@/services/app/Email/email-template';
import { getRenderedOrderOutCrmEmailTemplate } from '@/services/app/Order/order-out-detail-crm';
import Util, { sn } from '@/util';
import { ReloadOutlined, StarOutlined } from '@ant-design/icons';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ModalForm, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { Button, Col, Row, message } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useModel } from 'umi';

export type CmrEmailTemplateModalProps = {
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  crmId: number;
  orderNo: number;
  onSubmit?: (formData: API.Address) => Promise<boolean | void>;
};

const CmrEmailTemplateModal: React.FC<CmrEmailTemplateModalProps> = (props) => {
  const { modalVisible, handleModalVisible, crmId, orderNo } = props;
  const formRef = useRef<ProFormInstance>();
  const { getCodeValue } = useModel('app-settings');

  const [loading, setLoading] = useState<boolean>(false);
  const [tplId, setTplId] = useState<number>(
    sn(getCodeValue(DictCode.ORDER_OUT_CMR_EMAIL_TEMPLATE)),
  );
  const [emailTemplate, setEmailTemplate] = useState<API.EmailTemplate>();

  /**
   * Load email template data rendered.
   */
  const loadRenderedEmailTemplateData = useCallback(() => {
    setLoading(true);
    getRenderedOrderOutCrmEmailTemplate({
      tplId: tplId,
      crmId: crmId,
      orderNo: orderNo,
    })
      .then((res) => {
        setEmailTemplate(res);
        formRef.current?.setFieldsValue(res);
      })
      .catch((err) => Util.error(err))
      .finally(() => setLoading(false));
  }, [crmId, orderNo, tplId]);

  useEffect(() => {
    if (tplId && modalVisible) {
      loadRenderedEmailTemplateData();
    }
  }, [tplId, crmId, orderNo, modalVisible, loadRenderedEmailTemplateData]);

  return (
    <ModalForm
      title={
        <>
          CMR Email&nbsp;
          <Button
            type="link"
            size="small"
            icon={<ReloadOutlined />}
            title="Reload"
            onClick={() => loadRenderedEmailTemplateData()}
          />
        </>
      }
      width="900px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      initialValues={emailTemplate}
      labelAlign="left"
      formRef={formRef}
      onFinish={async (value) => {
        handleModalVisible(false);
        props.onSubmit?.(value);
      }}
      disabled={loading}
      submitter={false}
      /* submitter={{
        render(__, doms) {
          return (
            <>
              <Button
                type="primary"
                icon={<ExportOutlined />}
                onClick={() => formRef.current?.submit()}
              >
                Export
              </Button>
            </>
          );
        },
      }} */
    >
      <ProFormSelect
        required
        rules={[
          {
            required: true,
            message: 'Email Template is required',
          },
        ]}
        name="tplId"
        showSearch
        label="Email Template"
        mode="single"
        initialValue={sn(getCodeValue(DictCode.ORDER_OUT_CMR_EMAIL_TEMPLATE))}
        request={(params) => getEmailTplACList({ ...params, status: 1 })}
        fieldProps={{
          onChange(value) {
            setTplId(value);
          },
        }}
      />
      <Row>
        <Col flex="auto">
          <ProFormText name="subject" label="Subject" />
        </Col>
        <Col flex="20px" style={{ paddingTop: 36 }}>
          <Button
            type="link"
            size="small"
            icon={<StarOutlined />}
            title="Copy subject"
            onClick={() => {
              const value = formRef.current?.getFieldValue('subject');
              navigator.clipboard.writeText(value);
              message.success('Copied!', 2);
            }}
          />
        </Col>
      </Row>
      <Row>
        <Col flex="auto">
          <ProForm.Item name={'text_html'} label="Body" style={{ width: '100%' }}>
            <HtmlEditor initialFocus enableTextModule height={400} />
          </ProForm.Item>
        </Col>
        <Col flex="20px" style={{ paddingTop: 36 }}>
          <Button
            type="link"
            size="small"
            icon={<StarOutlined />}
            title="Copy body"
            onClick={() => {
              const value = formRef.current?.getFieldValue('text_html');
              navigator.clipboard.writeText(value);
              message.success('Copied!', 2);
            }}
          />
        </Col>
      </Row>
    </ModalForm>
  );
};
export default CmrEmailTemplateModal;
