import { getCustomerACList } from '@/services/app/BasicData/customer';
import Util from '@/util';
import type { DefaultOptionType } from 'antd/lib/select';
import { useCallback, useState } from 'react';

/**
 * Auto completion list of customer
 */
export const useCustomerOptions = (defaultParams?: Record<string, any>) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [customerOptions, setCustomerOptions] = useState<DefaultOptionType[]>([]);

  const searchCustomerOptions = useCallback(
    async (params?: Record<string, any>, sort?: any) => {
      setLoading(true);
      return getCustomerACList({ ...defaultParams, ...params }, sort)
        .then((res) => {
          setCustomerOptions(res);
          return res;
        })
        .catch(Util.error)
        .finally(() => {
          setLoading(false);
        });
    },
    [defaultParams],
  );

  return { customerOptions, searchCustomerOptions, loading };
};
