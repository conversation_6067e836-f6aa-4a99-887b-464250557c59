import { useState } from 'react';
import { useMemo } from 'react';
import React, { useEffect, useRef } from 'react';
import { Button, Col, message, Popconfirm, Row, Space } from 'antd';
import { ProFormTextArea } from '@ant-design/pro-form';
import Util from '@/util';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import {
  addTaskComment,
  deleteTaskComment,
  getTaskCommentList,
} from '@/services/app/Task/task-comment';
import moment from 'moment';
import SAvatar from '@/components/SAvatar';
import { useModel } from 'umi';
import _ from 'lodash';

export type TaskCommentListProps = {
  initialValues?: Partial<API.Task & { group_id?: number; block_id?: number; index?: number }>;
  tableHeight?: any;
  refreshTick?: number;
};

const TaskCommentList: React.FC<TaskCommentListProps> = (props) => {
  const { initialState } = useModel('@@initialState');

  const [comment, setComment] = useState<string>();

  const actionRef = useRef<ActionType>();

  const columns: ProColumns<API.TaskComment>[] = useMemo(() => {
    return [
      {
        title: 'Comment',
        sorter: true,
        dataIndex: ['comment'],
        valueType: 'text',
        search: false,
        width: '100%',
        render: (dom, record) => {
          return (
            <Row gutter={16} wrap={false}>
              <Col flex={'0 0 50px'}>
                <SAvatar text={(record.user?.username || '').substring(0, 2).toUpperCase()} />
              </Col>
              <Col>
                {record.comment && (
                  <div
                    dangerouslySetInnerHTML={{ __html: record.comment.replaceAll('\n', '<br />') }}
                  />
                )}
                <div className="text-sm c-grey">
                  <Space>
                    <span>{moment(record.created_on).fromNow()}</span>
                    {initialState?.currentUser?.user_id == record.user?.user_id && (
                      <span>
                        <Popconfirm
                          className="c-red"
                          title={<>Are you sure you want to delete?</>}
                          okText="Yes"
                          cancelText="No"
                          overlayStyle={{ width: 300 }}
                          onConfirm={async () => {
                            deleteTaskComment({ id: record.id })
                              .then(() => actionRef.current?.reload())
                              .catch((reason) => Util.error(reason));
                          }}
                        >
                          <Button className="text-sm" type="link">
                            Delete
                          </Button>
                        </Popconfirm>
                      </span>
                    )}
                  </Space>
                </div>
              </Col>
            </Row>
          );
        },
      },
    ];
  }, [initialState?.currentUser?.user_id]);

  useEffect(() => {
    if (props.initialValues?.id) {
      actionRef.current?.reload();
    }
  }, [props.initialValues?.id]);

  useEffect(() => {
    if (props.refreshTick) {
      actionRef.current?.reload();
    }
  }, [props.refreshTick]);

  return (
    <>
      <ProFormTextArea
        width="xl"
        required
        name={['task_comment', 'comment']}
        label="Comment"
        fieldProps={{
          value: comment,
          onChange: (e) => setComment(e.target.value),
        }}
      />
      <Button
        type="primary"
        htmlType="button"
        size="small"
        onClick={() => {
          if (!comment) {
            message.error('Please fill a comment');
            return;
          }
          addTaskComment({
            task_id: props.initialValues?.id,
            comment,
          })
            .then(() => {
              setComment('');
              actionRef.current?.reload();
            })
            .catch((reason) => Util.error(reason));
        }}
      >
        Save
      </Button>
      <ProTable<API.TaskComment, API.PageParams>
        rowKey="id"
        headerTitle={'Comments'}
        showHeader={false}
        locale={{ emptyText: <></> }}
        sticky
        options={{ fullScreen: true, density: false, search: false, setting: false }}
        actionRef={actionRef}
        revalidateOnFocus={false}
        size="small"
        pagination={{
          showSizeChanger: false,
          hideOnSinglePage: true,
          pageSize: 10,
        }}
        search={false}
        cardProps={{
          bodyStyle: { padding: 0 },
        }}
        request={(params, sort, filter) => {
          if (!props.initialValues?.id)
            return new Promise((resolve) => resolve({ data: [], total: 0, success: true }));
          return getTaskCommentList(
            { ...params, task_id: props.initialValues?.id },
            sort,
            filter,
          ) as any;
        }}
        columns={columns}
      />
    </>
  );
};

export default TaskCommentList;
