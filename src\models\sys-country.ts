import { getCountryList } from '@/services/app/Sys/country';
import Util from '@/util';
import { useCallback, useEffect, useState } from 'react';

export default () => {
  const [countries, setCountries] = useState<API.Country[]>([]);

  const loadCountries = useCallback(
    async (force?: boolean) => {
      if (force || !countries.length) {
        return getCountryList({ pageSize: 500 })
          .then((res) => {
            setCountries(res.data);
            return res.data;
          })
          .catch((e) => Util.error(e));
      }
    },
    [countries],
  );

  useEffect(() => {
    if (!countries.length) {
      loadCountries();
    }
  }, [countries, loadCountries]);

  return { countries, setCountries, loadCountries };
};
