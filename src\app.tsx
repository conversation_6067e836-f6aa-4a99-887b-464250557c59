// @ts-nocheck
import type { Settings as LayoutSettings } from '@ant-design/pro-layout';
import { SettingDrawer } from '@ant-design/pro-layout';
import { PageLoading } from '@ant-design/pro-layout';
import type { RunTimeLayoutConfig } from 'umi';
import { history, Link } from 'umi';
import RightContent from '@/components/RightContent';
import Footer from '@/components/Footer';
import {
  currentUser as queryCurrentUser,
  getMagentoStoreConfig,
  getMagentoStoreWebsites,
} from './services/app/api';
import { BookOutlined, LinkOutlined, DragOutlined } from '@ant-design/icons';
import defaultSettings from '../config/defaultSettings';
import type { RequestConfig } from '@@/plugin-request/request';
import { LS_TOKEN_NAME } from '@/constants';
import { message, Space, notification } from 'antd';
import { getTaskGroupACList } from './services/app/Task/task-group';
import DynamicIcon from './components/DynamicIcon';

const isDev = process.env.NODE_ENV === 'development';
const loginPath = '/user/login';
import routes from '../config/routes';
// import Authorized from '@/utils/Authorized';

export const initialStateConfig = {
  loading: <PageLoading />,
};

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
  currentUser?: API.CurrentUser;
  loading?: boolean;
  fetchUserInfo?: () => Promise<API.CurrentUser | undefined>;
}> {
  const fetchUserInfo = async () => {
    try {
      const msg = await queryCurrentUser();
      // @ts-ignore
      return msg;
    } catch (error) {
      history.push(loginPath);
    }
    return undefined;
  };
  // If it is not the login page, execute
  if (history.location.pathname !== loginPath) {
    const currentUser = await fetchUserInfo();

    return {
      fetchUserInfo,
      currentUser,
      settings: {
        ...defaultSettings,
      },
    };
  }

  return {
    fetchUserInfo,
    settings: defaultSettings,
  };
}

/**
 * We build new menus from routes config.ts
 *
 * @param roleId
 * @param userId
 * @param parent
 * @param parentAccess
 * @returns
 */
const getMenusFromRouter = (roleId: number, userId: number, parent: any, parentAccess: any) => {
  const isAdmin = roleId === 1;

  const menuData = [];
  const children = parent ? parent.routes : routes;
  children.forEach((routeItem) => {
    const menuItem = { ...routeItem, routes: [], access: routeItem.access ?? parentAccess };

    const hasPermission = isAdmin ? true : menuItem.access != 'canAdmin';
    if (hasPermission) {
      if (routeItem.routes?.length) {
        menuItem.routes = getMenusFromRouter(roleId, userId, routeItem, menuItem.access);
      }

      menuData.push(menuItem);
    }
  });

  return menuData;
};

const menuDataRender = (menuList: MenuDataItem[]) => {
  return menuList.map((item) => {
    const localItem = {
      ...item,
      icon: typeof item.icon == 'string' ? <DynamicIcon type={item.icon} /> : item.icon,
      children: item.children ? menuDataRender(item.children) : [],
    };
    return localItem;
    // return Authorized.check(item.authority, localItem, null) as MenuDataItem;
  });
};

// ProLayout api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState, setInitialState }) => {
  return {
    rightContentRender: () => <RightContent />,
    disableContentMargin: false,
    /* waterMarkProps: {
      content: initialState?.currentUser?.name,
    }, */
    footerRender: () => <Footer />,
    onPageChange: () => {
      const { location } = history;
      // If not logged in, redirect to login
      if (!initialState?.currentUser && location.pathname !== loginPath) {
        history.push(loginPath);
      }
    },
    breadcrumbRender: () => undefined,
    links: isDev
      ? [
          <Link key="openapi" to="/umi/plugin/openapi" target="_blank">
            <LinkOutlined />
            <span>OpenAPI Docs</span>
          </Link>,
          <Link to="/~docs" key="docs">
            <BookOutlined />
            <span>Documentation</span>
          </Link>,
        ]
      : [],
    menuHeaderRender: undefined,

    menu: {
      locale: false,
      params: { userId: initialState?.currentUser?.user_id, tmpData: 1 },
      request: async (params, defaultMenuData) => {
        // initialState.currentUser contains all user information
        if (!initialState?.currentUser?.user_id) return defaultMenuData;

        const taskGroups = await getTaskGroupACList();
        const menuData = getMenusFromRouter(
          initialState?.currentUser?.role,
          initialState?.currentUser?.user_id,
        );
        const isAdmin = initialState?.currentUser?.role === 1;

        // update children of TaskBoard Menu
        const boardMenu = menuData.find((x) => x.path == '/task-board');

        const tmp = taskGroups
          ?.filter(
            (x) =>
              !x.task_id && x.code != 'OrderIn' && x.code != 'OrderOut' && x.code != 'OrderOutHead',
          )
          ?.map((x) => ({
            path: `/task-board/${x.id}`,
            name: x.name,
            icon: 'DragOutlined',
            component: './TaskBoard',
          }));
        boardMenu.children = tmp;

        // update children of Order Menu
        const orderMenu = menuData.find((x) => x.path == '/order');

        let row = taskGroups?.find((x) => x.code == 'OrderIn');

        if (row && orderMenu.routes.findIndex((m) => m.path == `/task-board/${row.id}`) < 0) {
          orderMenu.routes.push({
            type: 'divider',
          });
          orderMenu.routes.push({
            path: `/task-board/${row.id}`,
            name: row.name,
            icon: 'DragOutlined',
            component: './TaskBoard',
          });
        }

        row = taskGroups?.find((x) => x.code == 'OrderOut');
        if (row && orderMenu.routes.findIndex((m) => m.path == `/task-board/${row.id}`) < 0) {
          orderMenu.routes.push({
            path: `/task-board/${row.id}`,
            name: row.name,
            icon: 'DragOutlined',
            component: './TaskBoard',
          });
        }

        // new OrderIn Document
        row = orderMenu.routes.findIndex((m) => m.path == `/order/in-document`);
        if (row) {
          orderMenu.routes.push({
            path: '/order/in-document',
            name: 'Order In Document',
            icon: 'ShopOutlined',
            hideInMenu: false,
            component: './Order/OrderInDocument',
          });
        }

        // new OrderOut Document
        row = orderMenu.routes.findIndex((m) => m.path == `/order/out-document`);
        if (row) {
          orderMenu.routes.push({
            path: '/order/out-document',
            name: 'Order Out Document',
            icon: 'CarryOutOutlined',
            hideInMenu: false,
            component: './Order/OrderOutDocument',
          });
        }

        row = taskGroups?.find((x) => x.code == 'OrderOutHead');
        if (row && orderMenu.routes.findIndex((m) => m.path == `/task-board/${row.id}`) < 0) {
          orderMenu.routes.push({
            type: 'divider',
          });
          orderMenu.routes.push({
            path: `/task-board/${row.id}`,
            name: row.name,
            icon: 'DragOutlined',
            component: './TaskBoard',
          });
        }

        orderMenu.routes.push({
          path: `/order/task-board-calendar`,
          name: 'Task Calendar',
          icon: 'CalendarOutlined',
          component: './TaskBoard/TaskCalendar',
        });

        return menuData;
      },
    },
    menuItemRender(item, defaultDom, menuProps) {
      return item.children ? (
        <Link to={item.path}>
          <Space size={8}>
            {typeof item.icon == 'string' ? <DynamicIcon type={item.icon} /> : item.icon}
            {item.name}
          </Space>
        </Link>
      ) : (
        <Link to={item.path}>
          <Space size={8}>
            {typeof item.icon == 'string' ? <DynamicIcon type={item.icon} /> : item.icon}
            {item.name}
          </Space>
        </Link>
      );
    },
    menuDataRender,
    unAccessible: <div>Un accessible</div>,
    // Add one loading status
    childrenRender: (children: Element, props: BasicLayoutProps) => {
      if (initialState?.loading) return <PageLoading />;
      return (
        <>
          {children}
          {!props.location?.pathname?.includes('/login') && isDev && (
            <SettingDrawer
              disableUrlParams
              enableDarkTheme
              settings={initialState?.settings}
              onSettingChange={(settings) => {
                setInitialState((preInitialState) => ({
                  ...preInitialState,
                  settings,
                }));
              }}
            />
          )}
        </>
      );
    },
    ...initialState?.settings,
  };
};

const authHeaderInterceptor = (url: string, options: any) => {
  const token = localStorage.getItem(LS_TOKEN_NAME);
  const authHeader = {};
  if (token /*  && (options.method !== 'GET' || options.widthToken) */) {
    // @ts-ignore
    authHeader.Authorization = `Bearer ${token}`;
  }
  return {
    url: `${url}`,
    options: { ...options, interceptors: true, headers: authHeader },
  };
};

const responseInterceptor = async (response: any, options: any) => {
  if (options.url) {
  }

  return response;
};
console.log('ENV: ', isDev);
export const request: RequestConfig = {
  prefix: API_URL,
  errorConfig: {
    adaptor: (resData: any, ctx: any) => {
      if (401 === resData.code) {
        localStorage.setItem(LS_TOKEN_NAME, '');
        history.push(loginPath);
      }

      const flashMsg = resData?.messageFlash;
      if (flashMsg) {
        for (const key in flashMsg) {
          const msgStr = flashMsg[key].join('\n');
          if (key == 'w') {
            notification.warning({ description: msgStr, duration: 0, placement: 'top' });
          } else if (key == 'e') {
            if (resData.code == 403 || resData.code == 401) {
              message.error(msgStr, 5);
            } else notification.error({ description: msgStr, duration: 0, placement: 'top' });
          } else if (key == 's') {
            message.success(msgStr, 5);
          } else if (key == 'i') {
            notification.info({ description: msgStr, duration: 5, placement: 'top' });
          }
        }
      }

      return {
        ...resData,
        success: resData.status === 'success',
        errorCode: resData.code,
        errorMessage: resData.status === 'success' ? undefined : resData.message,
        showType: 0,
      };
    },
  },
  middlewares: [],
  requestInterceptors: [authHeaderInterceptor],
  responseInterceptors: [responseInterceptor],
};

export interface response {
  status: string; // if request is success
  code?: number; // code for errorType
  message?: any;
  // data?: any;
  showType?: number; // error display type: 0 silent; 1 message.warn; 2 message.error; 4 notification; 9 page
  traceId?: string; // Convenient for back-end Troubleshooting: unique request ID
  host?: string; // onvenient for backend Troubleshooting: host of current access server
}
