import type { Dispatch, MutableRefObject, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { Button, Card, Col, Modal, Row, Space } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import { ProForm, ProFormCheckbox } from '@ant-design/pro-form';
import Util from '@/util';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { FilePdfOutlined } from '@ant-design/icons';
import _ from 'lodash';
import { getLoInvoiceList } from '@/services/app/LexOffice/lo-invoice';

export const LoVoucherLink: React.FC<{
  record: API.LoInvoice & { voucherType?: string; voucherNumber?: string };
  showNumber?: boolean;
}> = ({ record, showNumber }) => {
  let partialUrl = 'quotations';
  if (record.lo_type == 'invoice' || record.voucherType == 'invoice') partialUrl = 'invoices';
  else if (record.lo_type == 'order_confirmation' || record.voucherType == 'orderconfirmation')
    partialUrl = 'order-confirmations';
  return (
    <a
      href={`https://app.lexoffice.de/permalink/${partialUrl}/view/${record.lo_id ?? record.id}`}
      target="_blank"
      rel="noreferrer"
    >
      {showNumber ? record.lo_no ?? record.voucherNumber : record.lo_id ?? record.id}
    </a>
  );
};

export type ConvertModalProps = {
  orderNo: number;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  parentFormRef?: MutableRefObject<ProFormInstance<any> | undefined>;
};

const ConvertModal: React.FC<ConvertModalProps> = (props) => {
  const { orderNo, parentFormRef } = props;

  // Search
  const searchFormRef = useRef<ProFormInstance>();

  const actionRef = useRef<ActionType>();

  useEffect(() => {
    if (props.modalVisible) {
      actionRef.current?.reload();
    }
  }, [props.modalVisible]);

  const columns: ProColumns<API.LoInvoice>[] = [
    {
      title: '',
      dataIndex: 'lo_document_file_id',
      sorter: false,
      hideInForm: true,
      ellipsis: true,
      width: 30,
      className: 'p0',
      render: (dom, record) => {
        return record.lo_document_file_id && record.file_url ? (
          <a href={`${API_URL}${record.file_url}`} target="_blank" rel="noreferrer">
            <FilePdfOutlined />
          </a>
        ) : undefined;
      },
    },
    {
      title: 'ID',
      dataIndex: 'lo_id',
      sorter: true,
      hideInForm: true,
      ellipsis: true,
      width: 150,
      copyable: true,
      render: (dom, record) => {
        return <LoVoucherLink record={record} />;
      },
    },
    {
      title: 'No',
      dataIndex: 'lo_no',
      sorter: true,
      hideInForm: true,
      ellipsis: true,
      width: 100,
      render: (dom, record) => {
        return <LoVoucherLink record={record} showNumber />;
      },
    },
    {
      title: 'Type',
      dataIndex: 'lo_type',
      sorter: true,
      hideInForm: true,
      ellipsis: true,
      width: 120,
    },
    {
      title: 'Status',
      dataIndex: 'lo_status',
      sorter: true,
      hideInForm: true,
      ellipsis: true,
      width: 100,
    },
    {
      title: 'Related',
      dataIndex: 'details',
      sorter: true,
      hideInForm: true,
      render: (dom, record) => {
        const rels = record.detail?.relatedVouchers ?? [];
        if (rels.length) {
          return (
            <Space wrap>
              {rels.map((x: any) => (
                <LoVoucherLink key={x.id} record={x} showNumber />
              ))}
            </Space>
          );
        }
        return undefined;
      },
    },
    {
      title: 'Created on',
      sorter: true,
      dataIndex: 'created_on',
      valueType: 'dateTime',
      search: false,
      width: 110,
      className: 'c-grey text-sm',
      render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
    },
    {
      title: 'Updated on',
      sorter: true,
      dataIndex: 'updated_on',
      valueType: 'dateTime',
      search: false,
      width: 110,
      className: 'c-grey text-sm',
      render: (dom, record) => Util.dtToDMYHHMM(record.updated_on),
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 120,
      render: (dom, record) => (
        <Row>
          <Col flex={'0 0 50px'}>
            {record.lo_type == 'quotation' && (
              <Button
                type="default"
                size="small"
                onClick={() => {
                  if (parentFormRef && parentFormRef.current) {
                    parentFormRef.current?.setFieldValue('action', 'order_confirmation');
                    parentFormRef.current?.setFieldValue('isFinal', false);
                    parentFormRef.current?.setFieldValue('precedingVoucherId', record.lo_id);
                    parentFormRef.current?.submit();
                  }
                }}
                title="Pursue to an Order Confirmation"
              >
                OC
              </Button>
            )}
          </Col>
          <Col flex={'0 0 30px'}>
            {(record.lo_type == 'quotation' || record.lo_type == 'order_confirmation') && (
              <Button
                type="default"
                size="small"
                onClick={() => {
                  if (parentFormRef && parentFormRef.current) {
                    parentFormRef.current?.setFieldValue('action', 'invoice');
                    parentFormRef.current?.setFieldValue('isFinal', false);
                    parentFormRef.current?.setFieldValue('precedingVoucherId', record.lo_id);
                    parentFormRef.current?.submit();
                  }
                }}
                title="Pursue to an Invoice"
              >
                Inv
              </Button>
            )}
          </Col>
        </Row>
      ),
    },
  ];

  return (
    <Modal
      title={'Convert Lex Office Voucher'}
      width="1100px"
      open={props.modalVisible}
      onCancel={() => props.handleModalVisible(false)}
      footer={false}
    >
      <Card style={{ marginBottom: 0 }} bordered={false} bodyStyle={{ padding: 0 }}>
        <ProForm<API.LoInvoice>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            submitButtonProps: {
              htmlType: 'submit',
            },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => actionRef.current?.reload(),
            render: (form, dom) => {
              return [...dom];
            },
          }}
        >
          <ProFormCheckbox.Group
            name="lo_types"
            label="Types"
            fieldProps={{ onChange: () => actionRef.current?.reload() }}
            initialValue={['quotation', 'order_confirmation']}
            options={[
              { value: 'quotation', label: 'Quotation' },
              { value: 'order_confirmation', label: 'Order confirmation' },
              { value: 'invoice', label: 'Invoice' },
            ]}
          />
          <ProFormText name="lo_no" label="Number" tooltip="e.g. AG0001 or RE0001" />
        </ProForm>
      </Card>
      <ProTable<API.LoInvoice, API.PageParams>
        headerTitle={'Lex Office Vouchers List'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: false, density: false, reload: true, search: false, setting: false }}
        search={false}
        size="small"
        scroll={{ x: 1200 }}
        cardProps={{ bodyStyle: { padding: 0 } }}
        pagination={{
          showSizeChanger: true,
          hideOnSinglePage: true,
        }}
        request={(params, sort, filter) => {
          if (!orderNo) return Promise.resolve({ data: [], success: true, total: 0 });

          const searchValues = searchFormRef.current?.getFieldsValue();

          return getLoInvoiceList(
            {
              ...params,
              orderNo: orderNo,
              ...searchValues,
              lo_types: searchValues?.lo_types ?? [],
            },
            sort,
            filter,
          );
        }}
        columnEmptyText=""
        columns={columns}
        rowSelection={false}
      />
    </Modal>
  );
};

export default ConvertModal;
