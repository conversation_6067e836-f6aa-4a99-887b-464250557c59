import { But<PERSON>, Card, message, Modal, Popover, Space, Tag } from 'antd';
import React, { useState, useRef } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { getLexOfficeLink, getOrderTaskNo, nf2, sn, urlFull } from '@/util';

import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import {
  dsLatestLoVoucherList,
  dsLoVoucherList,
  getLoInvoiceCheckList,
  updateLoInvoiceCheck,
} from '@/services/app/LexOffice/lo-voucher-list';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import ProForm, { ProFormText } from '@ant-design/pro-form';
import { SyncOutlined } from '@ant-design/icons';
import { getCustomerACList } from '@/services/app/BasicData/customer';
import SProFormSelect from '@/components/SProFormSelect';
import { ExportOutlined, LinkOutlined } from '@ant-design/icons/lib/icons';
import ExportToStatModal from './components/ExportToStatModal';

const LoInvoiceCheckListPage: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const rowRef = useRef<API.LoInvoiceCheck>({}); // used for note update

  const [dataSource, setDataSource] = useState<API.LoInvoiceCheck[]>(() => []);
  const [currentRow, setCurrentRow] = useState<Partial<API.LoInvoiceCheck>>();
  const [openExportToStatModal, setOpenExportToStatModal] = useState<boolean>(false);
  // const [selectedRowsState, setSelectedRows] = useState<API.LoInvoiceCheck[]>([]);

  const searchFormRef = useRef<ProFormInstance>();

  const columns: ProColumns<API.LoInvoiceCheck>[] = [
    /* 
    // Disabled since @2023-06-23
    {
      title: '',
      dataIndex: 'action-check',
      sorter: false,
      width: 20,
      render(__, record) {
        return (
          <Typography.Link
            onClick={() => {
              const hide = message.loading('Updating checked status', 0);
              const updatedData = { is_checked: record.is_checked == 1 ? 0 : 1 };
              updateLoInvoiceCheck(record.lo_id, updatedData)
                .then((res) => {
                  actionRef.current?.reload();
                })
                .catch((err) => Util.error(err))
                .finally(() => hide());
            }}
          >
            {!record.is_checked ? (
              <CheckSquareOutlined
                style={{ color: 'lightgrey', opacity: 0.4 }}
                title="Make checked."
              />
            ) : (
              <CheckSquareOutlined style={{ color: '#07c807' }} title="Make unchecked." />
            )}
          </Typography.Link>
        );
      },
    },*/
    {
      title: 'No',
      dataIndex: 'lo_no',
      sorter: true,
      hideInForm: true,
      width: 70,
      className: 'text-13',
    },
    {
      title: '',
      dataIndex: 'link',
      hideInForm: true,
      width: 20,
      className: 'p-0',
      render: (dom, record) => (
        <a href={getLexOfficeLink(record.lo_type, record.lo_id)} target="_blank" rel="noreferrer">
          <LinkOutlined />
        </a>
      ),
    },
    {
      title: 'Cust No',
      dataIndex: 'lo_cust_no',
      sorter: true,
      hideInForm: true,
      width: 70,
      className: 'text-13',
    },
    /* {
      title: 'Original Customer',
      dataIndex: 'org_customer_id',
      sorter: true,
      hideInForm: true,
      width: 130,
      ellipsis: true,
      className: 'text-13',
      render(dom, record, index, action, schema) {
        return record.org_customer?.id
          ? `${record.org_customer.name}${
              record.org_customer.description
                ? ` - ${record.org_customer.description.substring(0, 20)}`
                : ''
            }`
          : null;
      },
    }, */
    {
      title: 'Customer',
      dataIndex: ['order_out', 'customer_obj'],
      sorter: true,
      hideInForm: true,
      width: 130,
      tooltip: 'Customer in Order Out',
      ellipsis: true,
      className: 'text-13',
      render(dom, record, index, action, schema) {
        const cust = record.order_out?.customer_obj;
        return cust?.id
          ? `${cust.name}${cust.description ? ` - ${cust.description.substring(0, 20)}` : ''}`
          : null;
      },
    },
    {
      title: 'Type',
      dataIndex: 'lo_type',
      sorter: true,
      hideInForm: true,
      width: 90,
      ellipsis: true,
      className: 'text-13',
    },
    /* {
      title: 'Status',
      dataIndex: 'lo_status',
      sorter: true,
      hideInForm: true,
      width: 90,
      ellipsis: true,
      className: 'text-13',
    }, */
    {
      title: 'Voucher Date',
      dataIndex: 'lo_voucher_date',
      sorter: true,
      hideInForm: true,
      className: 'text-13',
      width: 100,
      render: (dom, record) => Util.dtToDMY(record.lo_voucher_date),
    },
    {
      title: 'Order No',
      dataIndex: ['order_out', 'order_no'],
      sorter: true,
      hideInForm: true,
      className: 'text-13',
      width: 90,
      render: (dom, record) => (
        <>
          <Popover
            placement="topLeft"
            overlayStyle={{ maxWidth: 400 }}
            content={
              <Space direction="vertical" size={8} style={{ maxWidth: 250 }} className="text-sm">
                <div>
                  <Tag color="lime">{getOrderTaskNo('OrderOut', record.order_out?.order_no)}</Tag>
                  <span>{record.order_out?.customer}</span>
                </div>
                <div
                  dangerouslySetInnerHTML={{
                    __html: record.order_out?.desc?.replaceAll?.('\n', '<br />') || '',
                  }}
                />
              </Space>
            }
          >
            <a
              href={urlFull('/order-out-detail/' + record.order_out?.order_no)}
              target="_blank"
              rel="noreferrer"
              style={{ marginLeft: 8 }}
            >
              {record.order_out?.order_no}
            </a>
          </Popover>
        </>
      ),
    },
    {
      title: 'Gross Amount',
      dataIndex: 'total_gross_amount',
      sorter: true,
      hideInForm: true,
      align: 'right',
      width: 100,
      className: 'text-13',
      render(dom, record, index, action, schema) {
        return <span>{nf2(record.total_gross_amount)}</span>;
      },
    },
    {
      title: 'Contact name',
      dataIndex: 'lo_contact_name',
      sorter: true,
      hideInForm: true,
      ellipsis: true,
      className: 'text-13',
      width: 120,
    },
    /* {
      title: 'Version',
      dataIndex: 'lo_version',
      sorter: true,
      hideInForm: true,
      width: 60,
    }, */
    /* {
      title: 'Archived?',
      dataIndex: 'lo_archived',
      sorter: true,
      hideInForm: true,
      width: 60,
      className: 'text-13',
      render(dom, record, index, action, schema) {
        return record.lo_archived ? 'Archived' : '';
      },
    }, */
    {
      title: 'Order Desc',
      dataIndex: 'order_desc_dummy',
      className: 'text-13',
      width: 300,
      render(dom, record, index, action, schema) {
        return record.order_out?.order_no ? (
          <Space direction="vertical" size={2} style={{ maxWidth: 250 }} className="text-sm">
            <div>
              <Tag color="lime">{getOrderTaskNo('OrderOut', record.order_out?.order_no)}</Tag>
              <span>{record.order_out?.customer}</span>
            </div>
            <div
              dangerouslySetInnerHTML={{
                __html: record.order_out?.desc?.replaceAll?.('\n', '<br />') || '',
              }}
            />
          </Space>
        ) : null;
      },
    },
    {
      title: 'Note',
      dataIndex: 'note',
      sorter: false,
      tooltip: 'Click to edit',
      className: 'text-13 c-grey',
      ellipsis: true,
      width: 200,
      onCell: (record) => {
        return {
          className: 'cursor-pointer',
          onClick: () => {
            const modal = Modal.confirm({
              title: 'Update note',
              icon: false,
              content: (
                <>
                  <ProFormTextArea
                    placeholder={'Enter your note'}
                    fieldProps={{
                      defaultValue: record.note,
                      onChange: (e: any) => {
                        rowRef.current = { ...rowRef.current, note: e.target.value };
                      },
                    }}
                  />
                </>
              ),
              onOk: (close) => {
                const hide = message.loading('Updating note...', 0);
                modal.update({ okButtonProps: { disabled: true } });
                updateLoInvoiceCheck(record.lo_id, { note: rowRef.current?.note })
                  .then((res) => {
                    actionRef.current?.reload();
                    close();
                  })
                  .catch((err) => {
                    Util.error(err);
                    modal.update({ okButtonProps: { disabled: false } });
                  })
                  .finally(() => hide());
              },
            });
          },
        };
      },
    },
    /* {
      title: 'ID',
      dataIndex: 'lo_id',
      sorter: true,
      copyable: true,
      ellipsis: true,
      width: 100,
      className: 'text-13',
    },

    {
      title: 'Updated on',
      sorter: true,
      dataIndex: 'lo_updated_on',
      valueType: 'dateTime',
      search: false,
      width: 90,
      ellipsis: true,
      className: 'c-grey text-sm',
      render: (dom, record) => Util.dtToDMYHHMM(record.lo_updated_on),
    }, */
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 120,
      render: (_, record) => [
        <Button
          key="export-to-stat"
          icon={<ExportOutlined />}
          size="small"
          type={record.is_exported ? 'default' : 'primary'}
          ghost={record.is_exported ? false : true}
          title="Export to Stat..."
          onClick={() => {
            setCurrentRow(record);
            setOpenExportToStatModal(true);
          }}
        >
          Export
        </Button>,
      ],
    },
  ];

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<API.LoInvoiceCheck>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_lo_invoice_check', {})}
          submitter={{
            submitButtonProps: {
              htmlType: 'submit',
            },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => actionRef.current?.reload(),
            render: (form, dom) => {
              return [...dom];
            },
          }}
        >
          <ProFormText name={'lo_no'} label="Number" width={120} placeholder={'No'} />
          <ProFormText name={'lo_cust_no'} label="Cust No" width={120} placeholder={'Cust No'} />
          <SProFormSelect
            name="org_customer_id"
            label="Customer"
            width={170}
            mode="single"
            showSearch
            placeholder={'Customer'}
            fieldProps={{ onChange: () => actionRef.current?.reload() }}
            request={(params) => getCustomerACList(params) as any}
            onTabKeyCallback={(value) => {
              searchFormRef.current?.setFieldValue('org_customer_id', sn(value));
              actionRef.current?.reload();
            }}
          />
          <SProFormSelect
            name="lo_types"
            label="Type"
            width={300}
            mode="multiple"
            placeholder={'Type'}
            fieldProps={{ onChange: () => actionRef.current?.reload() }}
            initialValue={['invoice', 'creditnote']}
            options={[
              { value: 'invoice', label: 'Invoice' },
              { value: 'creditnote', label: 'Credit Note' },
              { value: 'orderconfirmation', label: 'Order confirmation' },
            ]}
            onTabKeyCallback={(value) => {
              searchFormRef.current?.setFieldValue('lo_types', value);
              actionRef.current?.reload();
            }}
          />
          <ProFormSelect
            name="lo_statuses"
            label="Status"
            width={170}
            mode="multiple"
            placeholder={'Status'}
            fieldProps={{ onChange: () => actionRef.current?.reload() }}
            options={[
              { value: 'draft', label: 'Draft' },
              { value: 'open', label: 'Open' },
              { value: 'paid', label: 'Paid' },
              { value: 'paidoff', label: 'Paidoff' },
              { value: 'voided', label: 'Voided' },
              { value: 'overdue', label: 'Overdue' },
            ]}
          />
        </ProForm>
      </Card>
      <ProTable<API.LoInvoiceCheck, API.PageParams>
        headerTitle={'Invoice list'}
        actionRef={actionRef}
        rowKey="lo_id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        size="small"
        sticky
        search={false}
        pagination={{
          showSizeChanger: true,
          pageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        // params={{ nlo_statuses: ['draft'] } as any}
        params={{ with: 'orderOut,orderOut.customerObj,orderOut.supplierObj' }}
        dataSource={dataSource}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_lo_invoice_check', searchFormValues);
          return getLoInvoiceCheckList(
            { ...params, ...searchFormValues },
            Object.keys(sort).length < 1 ? { lo_voucher_date: 'descend', lo_no: 'descend' } : sort,
            filter,
          ).then((res) => {
            setDataSource(res.data);
            return res;
          });
        }}
        onRequestError={(err) => Util.error(err)}
        columns={columns}
        columnEmptyText=""
        toolBarRender={() => [
          <Button
            type="primary"
            key="ds-lo"
            className="btn-green"
            icon={<SyncOutlined />}
            title="Sync latest invoices & credit notes & order confirmation from Lex Office"
            onClick={() => {
              const hide = message.loading(
                'Down syncing invoices and credit notes from Lex office...',
                0,
              );
              dsLatestLoVoucherList()
                .then((res) => actionRef.current?.reload())
                .catch((res) => {
                  message.error('Failed to sync data.');
                })
                .finally(() => hide());
            }}
          >
            Sync LO Invoices
          </Button>,
          <Button
            type="default"
            key="ds-lo-full"
            icon={<SyncOutlined />}
            title="Sync invoices & credit notes & order confirmation from Lex Office"
            onClick={() => {
              const hide = message.loading(
                'Down syncing invoices from Lex office, it would take some time...',
                0,
              );
              dsLoVoucherList({ isFull: true })
                .then((res) => actionRef.current?.reload())
                .catch((res) => {
                  message.error('Failed to sync data.');
                })
                .finally(() => hide());
            }}
          >
            Sync LO Invoices (Full)
          </Button>,
        ]}
      />
      {currentRow && (
        <ExportToStatModal
          invoiceCheck={currentRow}
          modalVisible={openExportToStatModal}
          handleModalVisible={setOpenExportToStatModal}
        />
      )}
    </PageContainer>
  );
};

export default LoInvoiceCheckListPage;
