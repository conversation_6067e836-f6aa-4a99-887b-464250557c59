import type { Dispatch, SetStateAction } from 'react';
import { useMemo } from 'react';
import { useCallback } from 'react';
import { useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { Button, message, Modal, Popconfirm, Space } from 'antd';
import Util from '@/util';
import { DeleteFilled, DeleteOutlined, PlusOutlined, SaveOutlined } from '@ant-design/icons';
import type { ActionType, EditableFormInstance, ProColumns } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import type { DefaultOptionType } from 'antd/lib/select';
import {
  getSysWorkflowStepList,
  updateSysWorkflowStep,
} from '@/services/app/Sys/sys-workflow-step';
import _ from 'lodash';
import { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { useModel } from 'umi';
import { DictType } from '@/constants';

export const conditionTypeOptions: DefaultOptionType[] = [
  { value: 'eq', label: 'Equals' },
  { value: 'neq', label: 'Not equal' },
  { value: 'in', label: 'In' },
  { value: 'nin', label: 'Not In' },
  { value: 'like', label: 'Like' },
  { value: 'nlike', label: 'Note like' },
  { value: 'null', label: 'Null' },
  { value: 'notnull', label: 'Not null' },
];

const getNewRecord = (parent_id?: any): API.SysWorkflowStepVisibleRuleGroup => {
  const uid = Util.genNewKey();
  const uidChild = Util.genNewKey();

  const child: API.SysWorkflowStepVisibleRuleGroup = {
    parent_id,
    uid: uidChild,
    field: '',
    condition_type: 'eq',
    value: null,
  };

  if (parent_id) {
    return child;
  }
  child.parent_id = uid;

  const newRecord: API.SysWorkflowStepVisibleRuleGroup = {
    uid: uid,
    field: '',
    condition_type: '',
    value: null,
    filters: [child],
  };
  return newRecord;
};

const getEditableKeys = (rules?: API.SysWorkflowStepVisibleRuleGroup[]) => {
  const keys: React.Key[] = [];
  rules?.forEach((element) => {
    if (element.filters) {
      element.filters.forEach((x) => {
        keys.push(x.uid as React.Key);
      });
    }
  });
  return keys;
};

const addNewItem = (
  type: 'child' | 'parent',
  currentRow: API.SysWorkflowStepVisibleRuleGroup,
  dataSource: API.SysWorkflowStepVisibleRuleGroup[],
  cb?: (
    newItem: API.SysWorkflowStepVisibleRuleGroup,
    newDataSource: API.SysWorkflowStepVisibleRuleGroup[],
  ) => void,
) => {
  if (type == 'parent') {
    const newObj = getNewRecord();
    const newDataSource = [...dataSource, newObj];
    cb?.(newObj, newDataSource);
  } else {
    const newDataSource = [...dataSource];

    // child?
    console.log('creating new child: ', currentRow);
    console.log(newDataSource);
    const newChild = getNewRecord(currentRow.uid);
    const parentIndex = _.findIndex(newDataSource, {
      uid: currentRow.uid,
    });
    const parent: API.SysWorkflowStepVisibleRuleGroup = newDataSource[parentIndex];
    parent.filters = [...(parent?.filters || []), newChild];

    console.log(newDataSource);
    cb?.(newChild, newDataSource);

    console.log('==> exp', dataSource.length, newDataSource.length);
  }
};

export type WorkflowStepRuleGroupsFormProps = {
  initialValues: Partial<API.SysWorkflowStep>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onCancel?: (flag?: boolean, formVals?: API.SysWorkflowStepVisibleRuleGroup) => void;
  onSaveCallback?: (id?: number, data?: Partial<API.SysWorkflowStep>) => Promise<void>;
};

const WorkflowStepRuleGroupsForm: React.FC<WorkflowStepRuleGroupsFormProps> = (props) => {
  // props
  const { initialValues, modalVisible, onSaveCallback } = props;
  const { id, workflow_id, visible_rules } = initialValues;

  // App model
  const { getDictOptionsByType } = useModel('app-settings');

  const actionRef = useRef<ActionType>();
  const editableFormRef = useRef<EditableFormInstance<API.SysWorkflowStepVisibleRuleGroup>>();

  const [loading, setLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<API.SysWorkflowStepVisibleRuleGroup[]>([]);
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>(() => []);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [steps, setSteps] = useState<API.SysWorkflowStep[]>([]);

  const systemSteps = useMemo(() => {
    const arr: API.SysWorkflowStep[] = [];
    arr.push({
      field_type: 'select',
      id: DictType.OrderOutType as any, // fake typing
      desc: 'SYS | Order Out Type',
      options: getDictOptionsByType(DictType.OrderOutType),
    });
    arr.push({
      field_type: 'select',
      id: DictType.OrderOutCategory as any, // fake typing
      desc: 'SYS | Order Out Category',
      options: getDictOptionsByType(DictType.OrderOutCategory),
    });
    return arr;
  }, [getDictOptionsByType]);

  const stepsAll = useMemo(() => {
    return [...systemSteps, ...steps];
  }, [steps, systemSteps]);

  useEffect(() => {
    if (!modalVisible) return;

    const new_visible_rules = (visible_rules ?? [getNewRecord()]).map((x, index) => ({
      ...x,
    }));

    if (actionRef.current && id) {
      setDataSource(new_visible_rules);
      setExpandedRowKeys(new_visible_rules.map((x) => x.uid as React.Key));
      setEditableRowKeys(getEditableKeys(new_visible_rules));
    } else {
      setExpandedRowKeys([]);
      setEditableRowKeys([]);
    }
  }, [modalVisible, id, visible_rules]);

  const loadStepsList = useCallback(
    (params?: any) => {
      if (!workflow_id) return Promise.resolve({});

      return getSysWorkflowStepList({
        ...params,
        scopeLogicOnly: true,
        pageSize: 500,
        workflow_id,
      }).then((res) => {
        setSteps(res.data);
        return res;
      });
    },
    [workflow_id],
  );

  useEffect(() => {
    loadStepsList();
  }, [loadStepsList]);

  const columns: ProColumns<API.SysWorkflowStepVisibleRuleGroup>[] = [
    {
      title: '',
      dataIndex: 'parent_id',
      width: 120,
      tooltip: '',
      editable: (record) => false,
      fieldProps: { size: 'small' },
      render: (dom, record, recordKey) => {
        return record.filters ? 'AND Group' : 'OR';
      },
    },
    /* {
      title: 'UID',
      dataIndex: 'uid',
      width: 100,
      tooltip: '',
      editable: (record) => !record?.filters,
      fieldProps: { size: 'small' },
    }, */
    {
      title: 'Field',
      dataIndex: 'field',
      width: 350,
      tooltip: '',
      editable: (record) => !record?.filters,
      renderFormItem: (item, { defaultRender, ...rest }, form) => {
        return (
          <ProFormSelect
            placeholder="Field"
            options={Util.getOptionValues(
              stepsAll || [],
              'id',
              (x: API.SysWorkflowStep) => `${x.field_type} | ${x.desc}`,
            )}
            fieldProps={{ size: 'small' }}
            formItemProps={{ ...item.formItemProps, style: { marginBottom: 0 } }}
          />
        );
      },
    },
    {
      title: 'Condition',
      dataIndex: 'condition_type',
      width: 120,
      formItemProps: (form, { rowIndex, entity, filteredValue }) => {
        const rules = [{ required: false, message: 'Condition type is required!' }];
        return {
          rules,
          hasFeedback: false,
        };
      },
      renderFormItem: (item, { defaultRender, ...rest }, form) => {
        return (
          <ProFormSelect
            placeholder="condition type"
            options={conditionTypeOptions}
            fieldProps={{ size: 'small' }}
            formItemProps={{ ...item.formItemProps, style: { marginBottom: 0 } }}
          />
        );
      },
    },
    {
      title: 'Value',
      dataIndex: 'value',
      renderFormItem: (item, { defaultRender, record, recordKey, ...rest }, form) => {
        if (record?.condition_type == 'notnull' || record?.condition_type == 'null')
          return undefined;

        if (record?.field && record?.condition_type) {
          const step = _.find(stepsAll, { id: record.field }) as API.SysWorkflowStep;
          if (step && step?.field_type) {
            if (step.field_type == 'text' || step.field_type == 'textarea') {
              return (
                <ProFormText
                  placeholder="Value"
                  fieldProps={{ size: 'small' }}
                  formItemProps={{ ...item.formItemProps, style: { marginBottom: 0 } }}
                />
              );
            } else if (step.field_type == 'checkbox' || step.field_type == 'multiselect') {
              return (
                <ProFormSelect
                  placeholder="Value"
                  mode="multiple"
                  options={step.options ?? []}
                  fieldProps={{ size: 'small' }}
                  formItemProps={{ ...item.formItemProps, style: { marginBottom: 0 } }}
                />
              );
            } else if (step.field_type == 'radio' || step.field_type == 'select') {
              return (
                <ProFormSelect
                  placeholder="Value"
                  options={step.options ?? []}
                  fieldProps={{ size: 'small' }}
                  formItemProps={{ ...item.formItemProps, style: { marginBottom: 0 } }}
                />
              );
            } else if (step.field_type == 'switch') {
              return (
                <ProFormSelect
                  placeholder="Value"
                  options={step.options ?? []}
                  fieldProps={{ size: 'small' }}
                  formItemProps={{ ...item.formItemProps, style: { marginBottom: 0 } }}
                />
              );
            }
          }
        }
        return undefined;
      },
    },
    {
      title: '',
      dataIndex: 'tmpOption',
      editable: false,
      width: 80,
      render: (text, record, __, action) => (
        <Space>
          {!!record.filters && (
            <a
              key="add"
              type="link"
              title='Add new "OR" condition'
              onClick={() => {
                addNewItem('child', record, dataSource, (newOne, newDataSource) => {
                  setDataSource(newDataSource);
                  console.log('New child Edit Key:', newOne.uid);
                  setEditableRowKeys((prev) => [...(prev || []), newOne.uid as React.Key]);
                });
                action?.startEditable?.(`${record.uid}`);
              }}
            >
              <PlusOutlined />
            </a>
          )}
          <Popconfirm
            key="delete"
            className="c-red"
            title={<>Are you sure you want to delete?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ width: 300 }}
            onConfirm={async (e) => {
              console.log(' delete: ', record.uid);
              if (record.filters) {
                setDataSource((prev) => prev.filter((x) => x.uid != record.uid));
              } else {
                setDataSource((prev) =>
                  prev.map((x) => ({
                    ...x,
                    filters: x.filters?.filter((x2) => x2.uid != record.uid),
                  })),
                );
              }
            }}
          >
            <DeleteOutlined />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleSave = async (isClose?: boolean) => {
    if (!initialValues.id) {
      message.error('Step ID is invalid.');
      return;
    }

    // Build valid visible rules
    const formValuesAssoc: any = editableFormRef.current?.getFieldsValue();
    const visibleRules: API.SysWorkflowStepVisibleRuleGroup[] = [];
    dataSource.forEach((x) => {
      const newX: API.SysWorkflowStepVisibleRuleGroup = { ...x, filters: [] };
      (x.filters || []).forEach((f: any) => {
        const filter: any = { ...f, ...(formValuesAssoc[f.uid] ?? {}) };
        newX?.filters?.push(filter as any);
      });
      if (newX.filters?.length) {
        visibleRules.push(newX);
      }
    });

    setLoading(true);
    updateSysWorkflowStep(initialValues.id, { visible_rules: visibleRules })
      .then((res) => {
        message.success('Saved successfully.');
        onSaveCallback?.(initialValues.id, res);
        if (isClose) {
          props.handleModalVisible(false);
        }
      })
      .catch((e) => Util.error(e))
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <Modal
      title={'Update visibility rules'}
      width="900px"
      open={props.modalVisible}
      onCancel={() => props.handleModalVisible(false)}
      footer={
        <Space>
          <Button
            type="primary"
            onClick={() => {
              handleSave();
            }}
            icon={<SaveOutlined />}
          >
            Save
          </Button>
          <Button
            type="primary"
            onClick={() => {
              handleSave(true);
            }}
          >
            Save & Close
          </Button>
          <Button
            onClick={() => {
              props.handleModalVisible(false);
            }}
          >
            Cancel
          </Button>
        </Space>
      }
    >
      <EditableProTable<API.SysWorkflowStepVisibleRuleGroup>
        loading={loading}
        rowKey="uid"
        scroll={{
          x: 400,
        }}
        style={{ padding: 0 }}
        cardProps={{ bodyStyle: { padding: 0 } }}
        actionRef={actionRef}
        editableFormRef={editableFormRef}
        size="small"
        childrenColumnName="filters"
        recordCreatorProps={{
          newRecordType: 'dataSource',
          position: 'bottom',
          creatorButtonText: 'Add new "AND" condition',
          record: (index: number, dataSource2: API.SysWorkflowStepVisibleRuleGroup[]) => {
            const newRecord = getNewRecord();
            return newRecord;
          },
        }}
        columns={columns}
        value={dataSource}
        onChange={(values) => {
          setDataSource(values);
          setExpandedRowKeys(values?.map?.((x) => x.uid as React.Key));
          const keys: React.Key[] = [];
          (values as API.SysWorkflowStepVisibleRuleGroup[]).forEach((element) => {
            if (element.filters) {
              element.filters.forEach((x) => {
                keys.push(x.uid as React.Key);
              });
            }
          });
          console.log('keys update', keys);
          setEditableRowKeys(keys);
        }}
        editable={{
          type: 'multiple',
          editableKeys,
          actionRender: (row, config, defaultDoms) => {
            return [defaultDoms.delete];
          },
          onValuesChange: (record, recordList) => {
            console.log('Editable: Values changed: ', record, recordList);
          },
          deletePopconfirmMessage: 'Are you sure you want to delete?',
          onlyAddOneLineAlertMessage: 'You can only add one.',
          deleteText: <DeleteFilled />,
        }}
        expandable={{
          defaultExpandAllRows: true,
          expandedRowKeys,
          onExpandedRowsChange(expandedKeys) {
            console.log('expand change', expandedKeys);
            setExpandedRowKeys([...expandedKeys]);
          },
          indentSize: 50,
          rowExpandable: (record) => {
            console.log('expand check: ', record.uid, !!record?.filters);
            return !!record?.filters;
          },
        }}
        locale={{
          emptyText: <></>,
        }}
        columnEmptyText=""
      />
    </Modal>
  );
};

export default WorkflowStepRuleGroupsForm;
