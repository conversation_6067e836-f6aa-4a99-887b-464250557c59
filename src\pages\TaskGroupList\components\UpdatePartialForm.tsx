import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormDependency, ProFormSwitch } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { updateTaskGroup } from '@/services/app/Task/task-group';
import Util from '@/util';
import SProFormDigit from '@/components/SProFormDigit';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...');

  try {
    await updateTaskGroup(fields);
    hide();
    message.success('Update is successful');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

// eslint-disable-next-line @typescript-eslint/ban-types
export type FormValueType = {} & Partial<API.TaskGroup>;

export type UpdatePartialFormProps = {
  initialValues?: Partial<API.TaskGroup>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.TaskGroup) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdatePartialForm: React.FC<UpdatePartialFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  useEffect(() => {
    if (!props.modalVisible) return;
    if (formRef.current) {
      const newValues = {
        ...(props.initialValues || {}),
      };
      formRef.current.setFieldsValue(newValues);
    }
  }, [props.initialValues, props.modalVisible]);

  return (
    <ModalForm
      title={'Update Sort - ' + props.initialValues?.name}
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
      initialValues={props.initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate({
          ...value,
          id: props.initialValues?.id,
          sort: value.sort === undefined || value.sort === '' ? null : value.sort,
        });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <SProFormDigit name="sort" label="Sort" placeholder="Sort" />
      <ProFormDependency name={['sort']}>
        {(depsValues) => {
          return depsValues.sort ? <></> : <ProFormSwitch name="isSortNull" label="Set null?" />;
        }}
      </ProFormDependency>
    </ModalForm>
  );
};

export default UpdatePartialForm;
