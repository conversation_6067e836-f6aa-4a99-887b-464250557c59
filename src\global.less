@import (reference) '~antd/es/style/themes/index';
@import '~antd/es/style/variable.less';

html,
body,
#root {
  height: 100%;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}
.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

.ant-message-notice-content {
  max-width: 500px;
}

.ant-notification-notice-with-icon {
  max-height: 300px;
  overflow-y: auto;
}

.ant-menu-sub.ant-menu-inline > .ant-menu-item,
.ant-menu-sub.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title {
  height: 30px;
  margin-top: 2px;
  margin-bottom: 2px;
  line-height: 30px;
}

@media (max-width: @screen-xs) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}

// Compatible with IE11
@media screen and(-ms-high-contrast: active), (-ms-high-contrast: none) {
  body .ant-design-pro > .ant-layout {
    min-height: 100vh;
  }
}

.search-form {
  .ant-row.ant-form-item {
    margin-bottom: 12px;
  }
  .ant-space.ant-space-horizontal {
    margin-bottom: 12px;
    margin-left: auto;
  }
}

.hideHeader.ant-pro-page-container .ant-pro-page-container-warp {
  display: none;
}

.hidden {
  display: none;
}

.ant-table-sticky-scroll-bar {
  height: 12px !important;
}

.text-right {
  text-align: right;
}
.text-center {
  text-align: center;
}
.pink {
  color: #9c5fe3;
}
.green {
  color: #026c02;
}
.red {
  color: #e00;
}
.gross-price {
  color: #9c5fe3;
  font-size: 80%;
}
.dark-blue {
  color: #4b81c7;
}

.c-blue {
  color: #1890ff;
}

.c-blue4 {
  color: var(--ant-primary-4);
}

.c-yellow {
  color: #baba09;
}

.c-orange {
  color: #f9a409;
}
.c-lightorange {
  color: #f4c877;
}

.c-darkorange {
  color: #c17c08 !important;
}

.c-lightgrey {
  color: lightgrey;
}

.c-lightgrey2 {
  color: #e3e3e3;
}

.c-grey {
  color: grey;
}

.c-green {
  color: #07c807;
}
.c-green-dark {
  color: #026c02;
}

.c-lightred {
  color: lightcoral;
}

.c-red {
  color: #ee2201;
}

.c-dark-purple {
  color: #9882dc !important;
}

.bg-green {
  background: green;
}

.bg-green2 {
  background: rgb(141, 190, 141) !important;
}

.bg-green3 {
  background: rgb(198, 232, 198) !important;
}

.bg-light-green {
  background: #f8fcf8;
}

.bg-light-red {
  background: #ff8686 !important;
}

.bg-light-red1 {
  background: #f8d7d7 !important;
}

.bg-light-red2 {
  background: #f8e3e3 !important;
}

.bg-light-pink2 {
  background: #f3c5cc !important;
}

.bg-light-orange {
  background: #fdc763 !important;
}

.bg-light-orange1 {
  background: #e4d3b6 !important;
}

.bg-light-orange2 {
  background: #ffeed0 !important;
}

.bg-yellow {
  background: #f3f189 !important;
}

.bg-light-yellow {
  background: #fbfcee !important;
}

.btn-gray {
  color: gray;
  &:hover {
    color: #1890ff;
  }
}

.np {
  padding: 0 !important;
}
.bl2 {
  border-left-width: 2px !important;
  border-left-style: solid !important;
  &.b-pink {
    border-left-color: #9c5fe3;
  }
  &.b-gray {
    border-left-color: #eee;
  }
}

.h-full {
  height: auto !important;
  height: 100%;
  min-height: 100%;
}

.w-full {
  width: 100%;
}

/*
Table customization
-------------------------------------------------------------------
*/
.ant-table.ant-table-small .ant-table-title,
.ant-table.ant-table-small .ant-table-footer,
.ant-table.ant-table-small .ant-table-thead > tr > th,
.ant-table.ant-table-small .ant-table-tbody > tr.ant-table-row > td,
.ant-table.ant-table-small tfoot > tr > th,
.ant-table.ant-table-small tfoot > tr > td {
  padding: 3px 5px !important;
}
.textForm .ant-pro-form-list-container {
  display: flex;
  flex-wrap: wrap;
}

.ant-pro-form-query-filter .ant-col {
  flex: 0 0 auto !important;
  max-width: 100%;
}

.ant-pro-form-query-filter > .ant-row > .ant-col:last-child {
  margin-left: auto;
}
.ant-pro-form-query-filter .ant-row.ant-form-item {
  margin-bottom: 12px;
}
.ant-table .ant-table-tbody > tr.row-multi {
  & > td:first-child {
    border-left: 2px solid #9c5fe3;
  }
}

.ant-table tr.stroke-all {
  td {
    text-decoration: line-through;

    .ant-typography {
      text-decoration: line-through;
    }
  }
}

.ant-table tr.italic-all {
  td {
    font-style: italic;
    .ant-typography {
      font-style: italic;
    }
  }
}

.ant-table .ant-table-tbody > tr.total-row {
  td {
    font-weight: bold;
    background-color: #e0f4e0;
  }
}

.ant-table .ant-table-tbody > tr.total-row2 {
  td {
    font-weight: bold;
    background-color: #e7f4e7;
  }
}

/*
Modal customization
-------------------------------------------------------------------
*/
.ant-modal.m-multi .ant-modal-header,
.ant-drawer-header {
  border-bottom-color: #b287e2 !important;
  border-bottom-width: 2px !important;
}

/*
General CSS classes
-------------------------------------------------------------------
*/
.cursor-pointer {
  cursor: pointer;
}

.margin-0,
.m-0 {
  margin: 0 !important;
}
.padding-0,
.p-0 {
  margin: 0 !important;
}

.text-13 {
  font-size: 13px;
}

.text-11 {
  font-size: 11px;
}

.text-sm2 {
  font-size: 95%;
}

.text-small,
.text-sm {
  font-size: 80%;
}
.text-xs {
  font-size: 70%;
}
.text-xxs {
  font-size: 8px;
  line-height: 1.3;
}
.text-md {
  font-size: 120%;
}
.bold {
  font-weight: bold;
}
.font-normal {
  font-weight: normal;
}

.italic {
  font-style: italic;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.rounded-full {
  border-radius: 100%;
}

.opacity-80 {
  opacity: 0.8;
}

.d-none {
  display: none;
}

/*
Popup
-------------------------------------------------------------------
*/
.popup {
  position: absolute;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  overflow: hidden;
  text-align: left;
  list-style-type: none;
  background-color: #fff;
  background-clip: padding-box;
  border-radius: 4px;
  outline: none;
  -webkit-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.popup li {
  clear: both;
  margin: 0;
  padding: 5px 12px;
  color: rgba(0, 0, 0, 0.65);
  font-weight: normal;
  font-size: 14px;
  line-height: 22px;
  white-space: nowrap;
  cursor: pointer;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.popup li:hover {
  background-color: #e6f7ff;
}

.popup li > i {
  margin-right: 8px;
}

.table-label {
  display: inline-block;
  padding-right: 8px;
}

/*
Extra Buttons
-------------------------------------------------------------------
*/
.ant-btn-primary {
  &.btn-green {
    background: #198754;
    border-color: #198754;

    &:hover {
      background: #1ea063;
      border-color: #1ea063;
    }

    &:active {
      background: #146c43;
      border-color: #13653f;
    }

    &:disabled {
      background: #22af6d;
      border-color: #22af6d;
    }
  }
}
.ant-btn-default {
  &.btn-green {
    color: #198754;
    border-color: #198754;

    &:hover {
      color: #1ea063;
      border-color: #1ea063;
    }

    &:active {
      color: #146c43;
      border-color: #13653f;
    }

    &:disabled {
      color: #22af6d;
      border-color: #22af6d;
    }
  }
}

.ant-pro-footer-bar {
  bottom: 18px !important;
}

.editable-table {
  .ant-table .ant-table-tbody > tr > td.ant-table-cell {
    padding: 8px 4px !important;
  }
}

/** TinyMCE editor
-------------------------------------------- */
.tox-tinymce {
  border: 1px solid rgba(0, 0, 0, 0.06) !important;
  border-radius: 2px !important;
}

/** Tabs
------------------------------------------- */
.page-tabs {
  & > .ant-tabs-nav {
    margin-bottom: 0;
    .ant-tabs-tab {
      &:first-child {
        border-left: none;
      }
      &.ant-tabs-tab-active {
        background-color: #1890ff;
        border-bottom-color: #1890ff;
        .ant-tabs-tab-btn {
          color: white;
        }
      }
    }
  }
  .ant-tabs-content-holder {
    background-color: white;
    .section-tabs {
      & > .ant-tabs-nav {
        .ant-tabs-tab {
          padding: 3px 6px;
          font-size: 13px;
        }
      }
    }
  }
}

/**
Inline editable cell
*/
.editable-cell-value-wrap {
  cursor: pointer;
}

.ant-table-cell .editable-cell-value-wrap {
  min-height: 32px;
  margin: -3px -5px;
  padding: 3px 5px;
}
.ant-table-cell:hover .editable-cell-value-wrap {
  /* border: 1px solid #d9d9d9;
  border-radius: 2px; */
  background: #eee;
}

[data-theme='dark'] .ant-table-cell:hover .editable-cell-value-wrap {
  border: 1px solid #434343;
}

/** Ant Tag
------------------------------------------------- */
.ant-tag.ant-tag-small {
  margin-right: 4px;
  padding: 0 3px;
  font-size: 10px;
  line-height: 14px;
}

/** Dragging
------------------------------------------------- */
.row-dragging {
  z-index: 1200 !important;
  background: #fafafa;
  border: 1px solid #ddd;
}

.row-dragging td {
  padding-top: 8px;
  padding-right: 16px;
  padding-left: 16px;
}

.row-dragging .drag-visible {
  visibility: visible;
}

/**
Resizable table
*/
.ant-table-cell.react-resizable .react-resizable-handle {
  position: absolute;
  right: -5px;
  bottom: 0;
  z-index: 100;
  width: 10px;
  height: 100%;
  cursor: col-resize;
  &:hover {
    background-color: #eee;
  }
}

/** Form inline card
------------------------------------------------- */

.sf-card.ant-card {
  .ant-card-body {
    padding-bottom: 12px;
  }
  .ant-form-item {
    margin-bottom: 12px;
  }
}

.label-h-auto .ant-form-item-label > label {
  height: auto;
}

blockquote {
  padding-left: 24px;
  border-left: 1px solid #ddd;
}

/** SQL printout
------------------------------------------------- */
pre.sql {
  overflow-x: auto;
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
}

@import './theme_small.less';
