/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/country';

/** rule GET /api/country */
export async function getCountryList(
  params: API.PageParams & Partial<API.Country>,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.Country>> {
  return request<API.Result<API.Country>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** put PUT /api/country */
export async function updateCountry(data: Partial<API.Country>, options?: { [key: string]: any }) {
  return request<API.Country>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/country */
export async function addCountry(data: API.Country, options?: { [key: string]: any }) {
  return request<API.Country>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/country/{id} */
export async function deleteCountry(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}
