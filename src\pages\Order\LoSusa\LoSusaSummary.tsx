import { <PERSON><PERSON>, <PERSON>, Col, Popover, Row, Space } from 'antd';
import type { CSSProperties } from 'react';
import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import type { DateRangeType } from '@/util';
import Util, { nf2, sn } from '@/util';

import {
  createOrUpdateLoAccountPlanning,
  getLoAccountsList,
  getLoSusaMonthlySummaryReport,
  getNextAccountCategory,
  getNextAccountNo,
} from '@/services/app/LexOffice/lo-susa';
import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  FileExcelOutlined,
  InfoCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import CreateForm from './components/CreateForm';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormDependency, ProFormRadio, ProFormSelect } from '@ant-design/pro-form';
import { ProFormCheckbox } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import UpdateAccountDataForm from './components/UpdateAccountDataForm';
import ImportModalForm from './components/ImportModalForm';
import EditableCell from '@/components/EditableCell';
import { useModel } from 'umi';
import { DictCode } from '@/constants';

const currentFy = Util.dtCurrentFy();
const FyListOptions = Util.dtBuildFyList(currentFy, 5);
const fyMonthsRange = Util.dtBuildRangesInFy(new Date(), false, true);

const isSumRow = (record: any): boolean => record.uid == 'Sum' || record.uid === 'Sum2';
const isCategoryRow = (record: any): boolean => `${record.uid}`.substring(0, 3) === '!c!';

const LoSusaSummaryPage: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();

  const { getCode } = useModel('app-settings');
  const category2Color = getCode(DictCode.SUSA_CATEGORY_COLOR)?.casted_value ?? {};

  const [loading, setLoading] = useState<boolean>(false);

  const [openPreviewModal, setOpenPreviewModal] = useState<boolean>(false);
  const [openUpdateAccountModal, setOpenUpdateAccountModal] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.LoSusa>();

  // XLS import
  const [openImportModal, setOpenImportModal] = useState<boolean>(false);

  const [months, setMonths] = useState<DateRangeType[]>(fyMonthsRange);
  const [accountCategories, setAccountCategories] = useState<API.LoAccount[]>([]);
  const [isSummarized, setIsSummarized] = useState<boolean>(false);
  const [isHideKum, setIsHideKum] = useState<boolean>(true);

  const loadAccountCategories = useCallback(() => {
    getLoAccountsList({ perPage: 5000 }).then((res) => {
      setAccountCategories(res.data);
    });
  }, []);

  useEffect(() => {
    const lastSf = Util.getSfValues('sf_lo_susa_summary', {});
    if (!lastSf.fy) {
      lastSf.fy = Util.dtCurrentFy();
    }
    setMonths(Util.dtBuildRangesInFy(+lastSf.fy, false, true));
    searchFormRef.current?.setFieldsValue(lastSf);
    setIsSummarized(lastSf.isSummarized || false);
    if (lastSf.isSummarized) {
      if (!lastSf.sortMode) {
        lastSf.sortMode = 'A';
      }
    }
    setIsHideKum(lastSf.isHideKum || false);

    Util.setSfValues(lastSf);
  }, []);

  useEffect(() => {
    loadAccountCategories();
  }, [loadAccountCategories]);

  const columns = useMemo<ProColumns<any>[]>(
    () => [
      {
        title: 'Konto',
        dataIndex: 'account_no',
        search: false,
        width: 110,
        copyable: true,
        align: 'right',
        fixed: 'left',
        tooltip: 'Total 2 is excluded total by system configuration',
        render(dom: any, record: any) {
          return isSumRow(record) ? record.uid == 'Sum2' ? <>Total 2</> : 'Total' : dom;
        },
      },
      {
        title: 'Kontobezeichung',
        dataIndex: 'account_name',
        search: false,
        width: 120,
        ellipsis: true,
        className: 'text-13 cursor-pointer',
        tooltip: 'Click to edit category.',
        onCell: (record: any) => {
          if (isSumRow(record)) return {};
          return {
            onClick() {
              setCurrentRow(record);
              setOpenUpdateAccountModal(true);
            },
          };
        },
        render(dom, record) {
          return isSumRow(record) || isCategoryRow(record) ? null : (
            <Row wrap={false} gutter={4}>
              <Col flex="auto">{dom}</Col>
              {record.lo_account?.desc && (
                <Col flex="0 0 20px">
                  <Popover
                    style={{ maxWidth: 400 }}
                    content={
                      <>
                        <div dangerouslySetInnerHTML={{ __html: record.lo_account?.desc ?? '' }} />
                      </>
                    }
                  >
                    <InfoCircleOutlined />
                  </Popover>
                </Col>
              )}
            </Row>
          );
        },
      },
      {
        title: 'Category',
        dataIndex: ['lo_account', 'category'],
        search: false,
        width: 60,
        ellipsis: true,
        align: 'center',
        tooltip: 'Click to edit category.',
        className: 'text-13 cursor-pointer',
        render(dom: any, record: any) {
          return isSumRow(record) ? '' : dom;
        },
        onCell: (record: any) => {
          if (isSumRow(record)) return {};
          return {
            onClick() {
              setCurrentRow(record);
              setOpenUpdateAccountModal(true);
            },
          };
        },
      },
      ...(months.map((month, mInd) => ({
        title: month.title,
        dataIndex: 'bal_' + mInd,
        search: false,
        width: 85,
        align: 'right',
        className: mInd == 0 ? 'bl2 b-gray' : '',
        render: (__: any, record: any, index: number) => {
          let cls = 'text-sm2';
          const diff =
            sn(record['bal_' + mInd]) -
            (mInd == 0 ? sn(record['bal_prev']) : sn(record['bal_' + (mInd - 1)]));

          if (!isHideKum) {
            if (diff > 0) cls += ' c-green';
            else if (diff < 0) cls += ' c-red';
          }
          return (
            <>
              {!isHideKum && (
                <div className="text-sm2 c-grey">{nf2(record['bal_' + mInd])}&nbsp;</div>
              )}
              <div
                className={cls}
                title={isHideKum ? `Balance: ${nf2(record['bal_' + mInd])}` : ''}
              >
                &nbsp;{nf2(diff)}
              </div>
            </>
          );
        },
      })) as any),
      /* {
        title: 'AVG',
        dataIndex: 'avg_bal_',
        search: false,
        width: 80,
        align: 'right',
        render: (dom: any, record: any) => {
          return nf2(sn(record[`bal_${months.length - 1}`]) / months.length);
        },
      }, */
      {
        title: 'AVG 1',
        dataIndex: 'avg_cur_bal_1',
        search: false,
        width: 90,
        align: 'right',
        className: 'bl2 b-gray',
        tooltip: 'Value of Last Month / qty.ofLastMonthsinceJuly',
        render: (dom: any, record: any) => {
          const bal = months.length > 1 ? sn(record[`bal_${months.length - 2}`]) : 0;
          const len = months.length > 1 ? months.length - 1 : 0;
          // return len ? nf2(bal / months.length) : 0;
          return len ? nf2(bal / len) : 0;
        },
      },
      {
        title: 'AVG 2',
        dataIndex: 'avg_cur_bal_2',
        search: false,
        width: 90,
        align: 'right',
        tooltip: 'Value of Cur Month / qty.ofMonthsinceJuly',
        render: (dom: any, record: any) => {
          return nf2(sn(record[`bal_${months.length - 1}`]) / months.length);
        },
      },
      {
        title: 'Plan',
        dataIndex: 'plannings',
        search: false,
        className: 'bl2 b-gray',
        width: 80,
        align: 'right',
        tooltip: 'Click to edit',
        render: (__: any, record: API.LoSusa & Record<string, any>) => {
          if (isSumRow(record) || isCategoryRow(record)) {
            return nf2(record.current_planning);
          }

          const selectedPlan = record.lo_account_plannings?.find(
            (x) => x.year == record.maxYear - 1,
          );
          const currentValue = selectedPlan?.value;
          return (
            <EditableCell
              dataType="number"
              defaultValue={currentValue}
              precision={2}
              triggerUpdate={function (
                value: any,
                cancelEdit?: (() => void) | undefined,
              ): Promise<any> {
                return createOrUpdateLoAccountPlanning({
                  year: record.maxYear - 1,
                  value: value,
                  account_no: record.account_no,
                  id: selectedPlan?.id,
                })
                  .catch((err) => Util.error(err))
                  .then((res) => {
                    cancelEdit?.();
                    if (res) {
                      actionRef.current?.reload();
                    }
                    return res;
                  });
              }}
            >
              {nf2(currentValue)}
            </EditableCell>
          );
        },
      },
    ],
    [months, isHideKum],
  );

  /**
   * Next or previous account no (Konto)
   *
   * @param dir -1 or 1
   */
  const hanldeAccountNoNavigation = (dir: number) => {
    const currentAccountNo = searchFormRef.current?.getFieldValue('account_no');
    setLoading(true);
    getNextAccountNo(dir, currentAccountNo)
      .then((res) => {
        if (res) {
          searchFormRef.current?.setFieldValue('account_no', res);
          actionRef.current?.reload();
        }
      })
      .catch((err) => Util.error(err))
      .finally(() => setLoading(false));
  };

  /**
   * Next or previous account category
   *
   * @param dir -1 or 1
   */
  const hanldeAccountCategoryNavigation = (dir: number) => {
    const category = searchFormRef.current?.getFieldValue(['lo_account', 'category']);
    setLoading(true);
    getNextAccountCategory(dir, category)
      .then((res) => {
        if (res) {
          searchFormRef.current?.setFieldValue(['lo_account', 'category'], res);
          actionRef.current?.reload();
        }
      })
      .catch((err) => Util.error(err))
      .finally(() => setLoading(false));
  };

  const categories: string[] = useMemo(() => {
    return accountCategories.map((x) => x.category ?? '');
  }, [accountCategories]);

  const valuesInUpdateForm = useMemo(() => {
    return { account_no: currentRow?.account_no, ...currentRow?.lo_account };
  }, [currentRow?.account_no, currentRow?.lo_account]);

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<API.LoSusa>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            submitButtonProps: {
              htmlType: 'submit',
            },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => actionRef.current?.reload(),
            render: (form, dom) => {
              return [...dom];
            },
          }}
        >
          <ProFormSelect
            key="year"
            name="fy"
            label="Year"
            width={100}
            placeholder="Fiscal year"
            formItemProps={{ style: { marginBottom: 0 } }}
            options={FyListOptions}
            fieldProps={{
              onChange(value, option) {
                const fy = value ?? Util.dtCurrentFy();
                const newMonths = Util.dtBuildRangesInFy(+fy, false, true);
                setMonths(newMonths);
                actionRef.current?.reload();
              },
            }}
          />
          <ProFormText
            name={'account_no'}
            label="Konto"
            width={120}
            placeholder={'Konto'}
            disabled={loading || isSummarized}
            addonAfter={
              <>
                <Button
                  onClick={() => hanldeAccountNoNavigation(-1)}
                  icon={<ArrowLeftOutlined />}
                  title="Previous Konto"
                  disabled={loading || isSummarized}
                />
                <Button
                  onClick={() => hanldeAccountNoNavigation(1)}
                  icon={<ArrowRightOutlined />}
                  disabled={loading || isSummarized}
                  title="Next Konto"
                />
              </>
            }
          />
          <ProFormText
            name={'account_name'}
            label="Kontobezeichung"
            width={'sm'}
            placeholder={'Kontobezeichung'}
            disabled={isSummarized}
          />
          <ProFormSelect
            name={['lo_account', 'category']}
            label="Category"
            width={'xs'}
            placeholder={'Category'}
            allowClear
            disabled={isSummarized}
            options={categories}
            addonAfter={
              <>
                <Button
                  onClick={() => hanldeAccountCategoryNavigation(-1)}
                  icon={<ArrowLeftOutlined />}
                  title="Previous category"
                  disabled={loading || isSummarized}
                />
                <Button
                  onClick={() => hanldeAccountCategoryNavigation(1)}
                  icon={<ArrowRightOutlined />}
                  disabled={loading || isSummarized}
                  title="Next conto"
                />
              </>
            }
          />
          <ProFormCheckbox
            name="noCategory"
            label="No Category"
            tooltip="No Category."
            disabled={isSummarized}
          />
          <ProFormCheckbox
            name="isSummarized"
            label="Summarized View"
            tooltip="Summarized View. Filters will be ignored."
            fieldProps={{
              onChange(e) {
                setIsSummarized(e.target.checked);
                actionRef.current?.reload();
              },
            }}
          />
          <ProFormCheckbox
            name="isHideKum"
            label="Hide Kum."
            fieldProps={{
              onChange(e) {
                setIsHideKum(e.target.checked);
              },
            }}
          />
          <ProFormDependency name={[`isSummarized`]}>
            {(depValues) => {
              console.log(depValues);
              const isSummarizedLocal = depValues.isSummarized;
              return (
                isSummarizedLocal && (
                  <ProFormRadio.Group
                    label="Sort mode"
                    name="sortMode"
                    options={[
                      {
                        value: 'A',
                        label: 'Sort Order A',
                      },
                      {
                        value: 'B',
                        label: 'Sort Order B',
                      },
                    ]}
                    initialValue={'A'}
                    fieldProps={{
                      onChange(e) {
                        actionRef.current?.reload();
                      },
                    }}
                  />
                )
              );
            }}
          </ProFormDependency>
        </ProForm>
      </Card>
      <ProTable<API.LoSusa & Record<string, any>, API.PageParams>
        headerTitle={'SuSa Summary in selected fiscal year'}
        actionRef={actionRef}
        rowKey="uid"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        size="small"
        toolBarRender={() => [
          <Space key="new">
            <Button
              type="primary"
              icon={<FileExcelOutlined />}
              onClick={() => setOpenImportModal(true)}
              title="Import by XLS or CSV"
            >
              CSV Import
            </Button>
            <div>or</div>
            <Button
              type="primary"
              ghost
              icon={<PlusOutlined />}
              onClick={() => {
                setOpenPreviewModal(true);
              }}
            >
              Paste & Import
            </Button>
          </Space>,
        ]}
        scroll={{ x: 800 }}
        pagination={false}
        request={async (params, sort, filter) => {
          const sfValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_lo_susa_summary', sfValues);

          let newSort = { ...sort };
          if (
            !sfValues.account_no &&
            !sfValues.date_to &&
            !sfValues.account_name &&
            Object.keys(sort).length < 1
          ) {
            newSort = {
              account_no: 'ascend',
              account_name: 'ascend',
            };
          }

          return getLoSusaMonthlySummaryReport({
            ...sfValues,
            ...params,
            months: months.map((x) => ({ from: x.from?.substring(0, 7) })),
          });
        }}
        onRequestError={(err) => Util.error(err)}
        columns={columns as any}
        tableAlertRender={false}
        columnEmptyText=""
        rowClassName={(row: any) => {
          if (row.uid == 'Sum') return 'total-row';
          if (row.uid == 'Sum2') return 'total-row2';
          return '';
        }}
        onRow={(record) => {
          const style: CSSProperties = {};
          // if ((record as any)?.children?.length) {
          const category = record.lo_account?.category || '';
          if (category2Color?.[category]?.color ?? null) {
            style.background = category2Color[category].color;
          }
          // }
          return {
            style,
          };
        }}
      />
      <CreateForm
        modalVisible={openPreviewModal}
        handleModalVisible={setOpenPreviewModal}
        onSubmit={async (value) => {
          actionRef.current?.reload();
        }}
      />
      <ImportModalForm
        modalVisible={openImportModal}
        handleModalVisible={setOpenImportModal}
        onSubmit={async (value) => {
          actionRef.current?.reload();
          setOpenImportModal(false);
        }}
      />
      <UpdateAccountDataForm
        initialValues={valuesInUpdateForm}
        modalVisible={openUpdateAccountModal}
        handleModalVisible={setOpenUpdateAccountModal}
        onSubmit={async (value) => {
          if (currentRow && value) {
            setCurrentRow((prev) => ({ ...prev, lo_account: { ...prev?.lo_account, ...value } }));
          }
          actionRef.current?.reload();
          loadAccountCategories();
          setOpenUpdateAccountModal(false);
        }}
        parentActionRef={actionRef}
        categories={categories}
        loadAccountCategories={loadAccountCategories}
      />
    </PageContainer>
  );
};

export default LoSusaSummaryPage;
