import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { addOrderIn } from '@/services/app/Order/order-in';
import { message } from 'antd';
import Util from '@/util';

const handleAdd = async (fields: API.OrderIn) => {
  const hide = message.loading('Adding...');
  const data = { ...fields };
  try {
    await addOrderIn(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

// eslint-disable-next-line @typescript-eslint/ban-types
export type FormValueType = {};

export type CreateFormProps = {
  values?: Partial<API.OrderIn>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.OrderIn) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={'New Order In'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.OrderIn);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormText
        required
        rules={[
          {
            required: true,
            message: 'Order No is required',
          },
        ]}
        width="md"
        name="order_no"
        label="Order No"
      />
      <ProFormText
        required
        rules={[
          {
            required: true,
            message: 'Supplier is required',
          },
        ]}
        width="md"
        name="supplier"
        label="Supplier"
      />
      <ProFormText width="md" name="lotus_notes_id" label="Lotus Notes ID" />
      <ProFormTextArea width="md" name="desc" label="Description" />
    </ModalForm>
  );
};

export default CreateForm;
