import React, { useRef, useMemo, useEffect, useState } from 'react';
import type { ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import type { DateRangeType } from '@/util';
import Util, { nf2, sn } from '@/util';

import { getLoSusaMonthlyDiffList } from '@/services/app/LexOffice/lo-susa';
import type { ProFormInstance } from '@ant-design/pro-form';
import useCustomerOrSupplier from './useCustomerOrSupplier';
import { Button, Modal, Typography } from 'antd';

type LoSusaMonthlyDiffListPropsType = {
  customerOrSupplier?: Partial<(API.Customer | API.Supplier) & { uid?: string }>;
  searchFormRef?: React.MutableRefObject<ProFormInstance<any> | undefined>;
  book_max_ym?: string | null;
};

const LoSusaMonthlyDiffList: React.FC<LoSusaMonthlyDiffListPropsType> = ({
  customerOrSupplier,
  searchFormRef,
  book_max_ym,
}) => {
  const actionRef = useRef<ActionType>();

  const [months, setMonths] = useState<DateRangeType[]>([]);
  const [sqls, setSqls] = useState<any[]>([]);

  const { isSupplier, selectedId, accountNo } = useCustomerOrSupplier(customerOrSupplier);

  useEffect(() => {
    setMonths(
      Util.dtBuildRangesFromLastMonth('m', 6, book_max_ym ?? new Date().toISOString(), true),
    );
  }, [book_max_ym]);

  const columns = useMemo(() => {
    return [
      {
        dataIndex: 'title',
        search: false,
        width: 150,
        render: (dom: any, record: any, index: number) => (
          <>
            <div>Balance {record.uid == 'Sum' && index == 0 ? record.account_no : record.uid}</div>
            {record.uid == 'Sum' && <div>Diff to month before</div>}
          </>
        ),
      },
      ...months.map((month, mInd) => ({
        title: month.title,
        key: `${month.from}_${mInd}`,
        dataIndex: `${month.from}_${mInd}`,
        // dataIndex: 'bal_' + mInd,
        search: false,
        width: 150,
        align: 'right',
        render: (__: any, record: any) => {
          let cls = 'text-sm2';
          const diff = mInd == 0 ? 0 : sn(record['bal_' + mInd]) - sn(record['bal_' + (mInd - 1)]);
          if (diff > 0) cls += ' c-green';
          else if (diff < 0) cls += ' c-red';
          return (
            <>
              <div className="text-sm2">{nf2(record['bal_' + mInd])}&nbsp;</div>
              {record.uid == 'Sum' && (
                <div className={cls}>
                  {mInd == 0 ? '' : nf2(diff)}
                  &nbsp;
                </div>
              )}
            </>
          );
        },
      })),
    ];
  }, [months]);

  useEffect(() => {
    actionRef.current?.reload();
  }, [months]);

  useEffect(() => {
    actionRef.current?.reload();
  }, [customerOrSupplier?.uid]);

  return (
    <ProTable<API.LoPayment, API.PageParams>
      headerTitle={'SuSa summary'}
      actionRef={actionRef}
      rowKey="uid"
      options={{ fullScreen: false, density: false, search: false, setting: false }}
      size="small"
      search={false}
      scroll={{ x: 1000 }}
      cardProps={{ bodyStyle: { padding: 0 } }}
      pagination={false}
      params={
        {
          customerId: isSupplier ? null : selectedId,
          supplierId: isSupplier ? selectedId : null,
          accountNo: accountNo,
        } as any
      }
      request={async (params) => {
        const searchFilters = searchFormRef?.current?.getFieldsValue();
        return getLoSusaMonthlyDiffList({
          ...{ ...searchFilters, book_max_ym: book_max_ym },
          ...params,
          months: months.map((x) => ({ from: x.from?.substring(0, 7) })),
        }).then((res) => {
          setSqls(res.sqls || []);
          return res;
        });
      }}
      toolBarRender={(action, rows) => {
        return [
          <Button
            key="sqls"
            type="dashed"
            onClick={() => {
              const list = sqls?.map((x) => x.query).join('\n\n');
              Modal.info({
                title: <Typography.Text copyable={{ text: list }}>SQL result</Typography.Text>,
                content: sqls?.map((x, ind) => (
                  // eslint-disable-next-line react/no-array-index-key
                  <div key={ind} style={{ marginBottom: 16 }}>
                    {x.query} <br />
                    <Typography.Text copyable={{ text: x.query }}>{x.time} ms</Typography.Text>
                  </div>
                )),
                width: '70%',
                maskClosable: true,
              });
            }}
            style={{ float: 'right' }}
          >
            SQL
          </Button>,
        ];
      }}
      columns={columns as any}
      columnEmptyText=""
    />
  );
};

export default LoSusaMonthlyDiffList;
