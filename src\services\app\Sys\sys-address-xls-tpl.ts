/* eslint-disable */
import { AC_PER_PAGE_PAGINATION } from '@/constants';
import { request, RequestConfig } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/sys/sys-address-xls-tpl';

/** rule GET /api/sys/sys-address-xls-tpl */
export async function getSysAddressXlsTplList(
  params: API.PageParams & Partial<API.SysAddressXlsTpl> & { is_date_valid?: number },
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.SysAddressXlsTpl>> {
  return request<API.Result<API.SysAddressXlsTpl>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** POST /api/sys/sys-address-xls-tpl */
export async function updateSysAddressXlsTpl(
  id?: number,
  data?: Partial<API.SysAddressXlsTpl> | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'POST',
    ...(options || {}),
  };

  if (data instanceof FormData) {
    config.body = data;
  } else {
    config.data = data;
  }

  return request<API.SysAddressXlsTpl>(`${urlPrefix}/` + id, config);
}

/** post POST /api/sys/sys-address-xls-tpl */
export async function addSysAddressXlsTpl(
  data: API.SysAddressXlsTpl | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'POST',
    ...(options || {}),
  };
  if (data instanceof FormData) {
    config.body = data;
  } else {
    config.data = data;
  }
  return request<API.SysAddressXlsTpl>(`${urlPrefix}`, config);
}

/** PUT /api/sys/sys-address-xls-tpl/mapping-details/{id} */
export async function updateMappingDetails(
  id?: number,
  data?: Partial<API.SysAddressXlsTpl> | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };

  return request<API.ResultObject<API.SysAddressXlsTpl>>(
    `${urlPrefix}/mapping-details/${id}`,
    config,
  ).then((res) => res.message);
}

/** PUT /api/sys/sys-address-xls-tpl/export-address/{id} */
export async function exportAddressByTpl(
  id?: number, // tpl ID.
  data?: { addressId?: number; supplierId?: number; orderNo?: number },
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };

  return request<API.ResultObject<API.SysAddressXlsTpl>>(
    `${urlPrefix}/export-address/${id}`,
    config,
  ).then((res) => res.message);
}

/** GET /api/sys/sys-address-xls-tpl/{id} */
export async function getSysAddressXlsTpl(
  id?: number,
  params?: Partial<API.SysAddressXlsTpl> | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'GET',
    params,
    ...(options || {}),
  };

  return request<API.ResultObject<API.SysAddressXlsTpl>>(`${urlPrefix}/${id}`, config).then(
    (res) => res.message,
  );
}

/** delete DELETE /api/sys/sys-address-xls-tpl/{id} */
export async function deleteSysAddressXlsTpl(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}

export async function getSysAddressXlsTplACList(
  params: API.PageParams & { number?: number; numberExact?: number },
  sort?: any,
  filter?: any,
): Promise<any[]> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize ?? AC_PER_PAGE_PAGINATION,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) =>
    res.message.data.map((x: API.SysAddressXlsTpl) => ({
      value: x.id,
      text: x.name,
      label: x.name,
    })),
  );
}

export async function getAddressMappingFields(id?: number): Promise<any> {
  return request<API.BaseResult>(`${urlPrefix}/mapping-fields/${id}`, {
    method: 'GET',
    paramsSerializer,
    withToken: true,
  }).then((res) => res.message);
}
