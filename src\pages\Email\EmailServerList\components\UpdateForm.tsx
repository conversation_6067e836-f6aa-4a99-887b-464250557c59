import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSwitch } from '@ant-design/pro-form';
import { ProFormDigit } from '@ant-design/pro-form';
import { ProFormText, ModalForm } from '@ant-design/pro-form';
import { updateEmailServer } from '@/services/app/Email/email-server';
import Util from '@/util';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateEmailServer(fields);
    hide();
    message.success('Update is successful');
    return true;
  } catch (error) {
    hide();
    Util.error('Update failed, please try again!', error);
    return false;
  }
};

export type FormValueType = Partial<API.EmailServer>;

export type UpdateFormProps = {
  initialValues?: Partial<API.EmailServer>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.EmailServer) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef && formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, props.initialValues]);

  return (
    <ModalForm
      title={'Update supplier'}
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      initialValues={props.initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate({ ...value, id: props.initialValues?.id });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormText width="md" name="domain" label="Domain" />
      <ProFormText
        rules={[
          {
            required: true,
            message: 'IMAP Host is required',
          },
        ]}
        width="md"
        name="imap_host"
        label="IMAP Host"
      />
      <ProFormSwitch width="md" name="imap_ssl" label="IMAP SSL?" />
      <ProFormDigit width="md" name="imap_port" label="IMAP Port" />
      <ProFormDigit width="md" name="imap_port_ssl" label="IMAP Port (SSL)" />
    </ModalForm>
  );
};

export default UpdateForm;
