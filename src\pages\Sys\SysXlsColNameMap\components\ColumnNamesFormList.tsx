import React, { useRef } from 'react';
import type { FormListActionType } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import { ProFormList } from '@ant-design/pro-form';
import _ from 'lodash';
import Util from '@/util';

const ColumnNamesFormList: React.FC = () => {
  const listActionRef = useRef<FormListActionType>();

  return (
    <>
      <ProFormList
        actionRef={listActionRef}
        key={'id'}
        name="xls_column_list"
        creatorButtonProps={{
          position: 'bottom',
          creatorButtonText: 'Add Column Name',
        }}
        creatorRecord={(index: number) => {
          return {
            id: Util.genNewKey(),
          };
        }}
        deleteIconProps={{ tooltipText: 'Remove' }}
        copyIconProps={false}
        containerStyle={{ marginBottom: 0 }}
      >
        <ProFormText
          name="value"
          placeholder="XLS Name in column"
          width={300}
          rules={[
            {
              required: true,
              message: 'XLS Name is required.',
            },
          ]}
        />
      </ProFormList>
    </>
  );
};

export default ColumnNamesFormList;
