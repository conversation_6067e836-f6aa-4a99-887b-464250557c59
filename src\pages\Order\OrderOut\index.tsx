import { PlusOutlined, SyncOutlined } from '@ant-design/icons';
import { Button, message, Drawer, Tag, Card } from 'antd';
import React, { useState, useRef } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import UpdateForm from './components/UpdateForm';

import Util, { getLexOfficeLink, nf2, sn, urlFull } from '@/util';
import CreateForm from './components/CreateForm';
import {
  getOrderOutList,
  deleteOrderOut,
  exportOrderOutToTask,
} from '@/services/app/Order/order-out';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import { dsLatestLoVoucherList } from '@/services/app/LexOffice/lo-voucher-list';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormText } from '@ant-design/pro-form';

/**
 *  Delete node
 *
 * @param selectedRows
 */

export const handleRemove = async (selectedRows: API.OrderOut[]) => {
  const hide = message.loading('Deleting');
  if (!selectedRows) return true;

  try {
    await deleteOrderOut({
      id: selectedRows.map((row) => row.order_no),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

const OrderOutList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();

  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.OrderOut>();
  const [selectedRowsState, setSelectedRows] = useState<API.OrderOut[]>([]);

  const columns: ProColumns<API.OrderOut>[] = [
    {
      title: 'Order No',
      dataIndex: 'order_no',
      sorter: true,
      width: 80,
      defaultSortOrder: 'descend',
      render: (dom, record) => {
        return (
          <a
            href={urlFull(`/order-out-detail/${record.order_no}?task_id=${record.task?.id}`)}
            target="_blank"
            rel="noreferrer"
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: 'Customer',
      dataIndex: 'customer',
      sorter: true,
      hideInForm: true,
    },
    /* {
      title: 'Lotus Notes ID',
      dataIndex: 'lotus_notes_id',
      sorter: true,
      hideInForm: true,
      ellipsis: true,
      render: (dom, record) =>
        record.lotus_notes_id ? (
          <Row wrap={false}>
            <Col flex={'auto'}>
              <Typography.Text ellipsis> {record.lotus_notes_id}</Typography.Text>
            </Col>
            <Col flex="0 0 40px">
              <a
                href={(getCodeValue(DictCode.LOTUS_PATH) ?? '').replace(
                  '{lotusNotesId}',
                  record.lotus_notes_id,
                )}
                title="Open Lotus link"
              >
                L
              </a>
              <a
                href={urlFull('/order-out-detail/lotus/' + record.lotus_notes_id)}
                target="_blank"
                rel="noreferrer"
                title="Open order out detail"
                style={{ marginLeft: 8 }}
              >
                <LinkOutlined />
              </a>
            </Col>
          </Row>
        ) : undefined,
    }, */
    {
      title: 'VK (XLS)',
      dataIndex: 'order_out_details_sum_vk',
      hideInForm: true,
      width: 90,
      align: 'right',
      render(dom, record, index, action, schema) {
        return nf2(record.order_out_details_sum_vk);
      },
    },
    {
      title: 'Invoices',
      dataIndex: 'lo_order_out_invoices',
      hideInForm: true,
      width: 200,
      render(dom, record, index, action, schema) {
        return record.lo_order_out_invoices?.map((x) => (
          <span key={x.lo_id}>
            <a href={getLexOfficeLink(x.lo_type, x.lo_id)} target="_blank" rel="noreferrer">
              <Tag>
                <span className={x.lo_status == 'draft' ? 'c-orange' : ''}>
                  {x.lo_no ?? x.lo_id?.substring(0, 6)}
                </span>
              </Tag>
            </a>
          </span>
        ));
      },
    },
    {
      title: 'Description',
      dataIndex: 'desc',
      sorter: true,
      hideInForm: true,
      ellipsis: true,
    },
    {
      title: 'Latest comment',
      dataIndex: ['task', 'latest_task_comment', 'comment'],
      search: false,
      width: 250,
      className: '',
    },
    {
      title: 'Updated on',
      sorter: true,
      dataIndex: 'updated_on',
      valueType: 'dateTime',
      search: false,
      width: 110,
      className: 'c-grey text-sm',
      render: (dom, record) => Util.dtToDMYHHMM(record.updated_on),
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 70,
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          Edit
        </a>,
      ],
    },
  ];

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<API.OrderOut>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_order_out_list', {})}
          submitter={{
            submitButtonProps: {
              htmlType: 'submit',
            },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => actionRef.current?.reload(),
            render: (form, dom) => {
              return [...dom];
            },
          }}
        >
          <ProFormText name={'order_no'} label="Order No" width={'sm'} placeholder={'Order No'} />
          <ProFormText name={'customer'} label="Customer" width={'sm'} placeholder={'Customer'} />
        </ProForm>
      </Card>

      <ProTable<API.OrderOut, API.PageParams>
        headerTitle={'Order Out list'}
        actionRef={actionRef}
        rowKey="order_no"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        size="small"
        search={false}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
          <Button
            type="default"
            key="export-task"
            title="Export Order Out into task "
            onClick={() => {
              const hide = message.loading('Syncing Order Out entries with tasks...', 0);
              exportOrderOutToTask()
                .then((res) => {
                  if (res) {
                    message.success('Synced successfully.');
                  }
                })
                .finally(() => hide());
            }}
          >
            <SyncOutlined /> Sync with tasks
          </Button>,
          <Button
            type="primary"
            key="ds-lo"
            className="btn-green"
            icon={<SyncOutlined />}
            title="Sync invoices & credit notes from Lex Office"
            onClick={() => {
              const hide = message.loading(
                'Down syncing invoices and credit notes from Lex office...',
                0,
              );
              dsLatestLoVoucherList()
                .catch((res) => {
                  message.error('Failed to sync data.');
                })
                .finally(() => hide());
            }}
          >
            Sync LO Invoices
          </Button>,
        ]}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(
            Util.getSfValues('sf_order_out_list_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION,
          ),
        }}
        params={{ with: 'task,latestTaskComment,orderOutDetailsSumVK,loOrderOutInvoices' }}
        request={(params, sort, filter) => {
          const sfValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_order_out_list', sfValues);
          Util.setSfValues('sf_order_out_list_p', params);

          return getOrderOutList({ ...params, ...sfValues }, sort, filter);
        }}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        columnEmptyText=""
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              chosen{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              OrderOut &nbsp;&nbsp;
            </div>
          }
        >
          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            Bacth deletion
          </Button>
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.order_no && (
          <ProDescriptions<API.OrderOut>
            column={2}
            title={currentRow?.order_no}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.order_no,
            }}
            columns={columns as ProDescriptionsItemProps<API.OrderOut>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default OrderOutList;
