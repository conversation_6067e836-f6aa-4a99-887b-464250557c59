import { getOrderTaskNo, isOrderTask, removeOrderPrefix } from '@/util';
import { Col, Popover, Row, Space, Tag, Typography, Divider } from 'antd';
import { useModel } from 'umi';
import TaskActions from '../Sub/TaskActions';
import type { TaskProps } from '../Task';

type TaskLayoutOrderAProps = TaskProps;

export const getOrderOutTitleInHeadGroup = (task?: API.Task) => {
  const refType = task?.ref_type;

  if (refType == 'OrderOut') {
    if (task?.order_out_head_name)
      return `${task?.order_out_head_name} - ${task.ref?.order_no} ${task.desc}`;
    else return removeOrderPrefix(task?.title, task?.title);
  } else if (refType == 'OrderIn') {
    return removeOrderPrefix(task?.title, task?.title);
  }
  return task?.title;
};

const TaskLayoutOrderA: React.FC<TaskLayoutOrderAProps> = (props) => {
  const { task } = props;
  const { getTaskGroupInModel } = useModel('task-group');

  const ref = task?.ref as API.OrderIn | API.OrderOut;

  const groupInfo = getTaskGroupInModel(props.groupId);
  const isOrderOutGroup = groupInfo?.code == 'OrderOut';
  const isOrderOutHeadGroup = groupInfo?.code == 'OrderOutHead';

  return (
    <>
      <Popover
        placement="topLeft"
        overlayStyle={{ maxWidth: 400 }}
        content={
          <Space direction="vertical" size={8} style={{ maxWidth: 250 }} className="text-sm">
            <div>
              <Tag color="lime">{getOrderTaskNo(task.ref_type, task.ref?.order_no)}</Tag>
              {isOrderTask(task.ref_type) && (
                <span>
                  {task.ref_type == 'OrderIn'
                    ? '' + (ref as API.OrderIn)?.supplier
                    : '' + (ref as API.OrderOut)?.customer}
                </span>
              )}
            </div>
            <div
              dangerouslySetInnerHTML={{
                __html: ref?.desc?.replaceAll?.('\n', '<br />') || '',
              }}
            />

            {task.latest_task_comment?.comment && (
              <>
                <Divider style={{ margin: '0' }} />
                <div>{task.latest_task_comment?.comment}</div>
              </>
            )}
          </Space>
        }
      >
        <Row wrap={false} gutter={16} className="ant-row-middle">
          <Col flex={'auto'}>
            <Typography.Paragraph ellipsis={true} style={{ marginBottom: 0 }}>
              {isOrderOutHeadGroup
                ? getOrderOutTitleInHeadGroup(task)
                : removeOrderPrefix(task.ref_type, task.title)}
            </Typography.Paragraph>
          </Col>
          {/* <Col flex={'0 0 45px'} className="text-xs c-grey text-right" style={{ padding: 0 }}>
            #{task.id}
          </Col> */}
          {isOrderOutGroup && (
            <Col
              className="text-xs c-grey text-right"
              flex={'0 0 50px'}
              style={{ padding: 0, marginLeft: 'auto' }}
            >
              {task.order_out_head_name}
            </Col>
          )}
          <TaskActions {...props} />
        </Row>
      </Popover>
    </>
  );
};

export default TaskLayoutOrderA;
