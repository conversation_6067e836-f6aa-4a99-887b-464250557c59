import {
  Button,
  message,
  Drawer,
  Tag,
  Popover,
  Card,
  Typography,
  Row,
  Col,
  Popconfirm,
} from 'antd';
import React, { useState, useRef } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { sn } from '@/util';
import {
  getEmailList,
  deleteEmail,
  dsPullEmail,
  updateEmail,
  moveEmailAttachments,
} from '@/services/app/Email/email';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import SFooterToolbarExtra from '@/components/Table/SFooterToolbarExtra';
import BatchDeleteAction from '@/components/Table/BatchDeleteAction';
import {
  CheckOutlined,
  CheckSquareOutlined,
  DownloadOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  RightSquareOutlined,
} from '@ant-design/icons/lib/icons';
import EmailTaskAssignmentForm from './components/EmailTaskAssignmentForm';
import ViewEmail from './components/ViewEmail';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormCheckbox } from '@ant-design/pro-form';
import ProForm, { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import EmailBoxTypeIcon from './components/EmailBoxTypeIcon';
import AttachmentIconIcon from './components/AttachmentIcon';

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.Email[]) => {
  const hide = message.loading('Deleting');
  if (!selectedRows) return true;

  try {
    await deleteEmail({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error('Delete failed, please try again!');
    return false;
  }
};

const EmailLinkedType = [
  { value: '', label: 'All' },
  { value: 1, label: 'Unlinked' },
  { value: 2, label: 'Linked' },
];

const EmailList: React.FC = () => {
  const [openAssignModal, setOpenAssignModal] = useState<boolean>(false);

  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.Email>();
  const [selectedRowsState, setSelectedRows] = useState<API.Email[]>([]);

  // Search
  const searchFormRef = useRef<ProFormInstance>();
  const [loading, setLoading] = useState<boolean>(false);

  const columns: ProColumns<API.Email>[] = [
    {
      title: '',
      dataIndex: 'action-hide',
      sorter: false,
      width: 20,
      render(__, record) {
        return (
          <Typography.Link
            onClick={() => {
              updateEmail({ id: record.id, is_hidden: record.is_hidden ? 0 : 1 })
                .then((res) => {
                  actionRef.current?.reload();
                })
                .catch((err) => Util.error(err));
            }}
          >
            {!record.is_hidden ? (
              <EyeInvisibleOutlined
                style={{ color: '#ff8686' }}
                title="Make this email invisible."
              />
            ) : (
              <EyeOutlined title="Make this email visible." />
            )}
          </Typography.Link>
        );
      },
    },
    {
      title: '',
      dataIndex: 'box',
      sorter: false,
      width: 20,
      render(__, record) {
        return <EmailBoxTypeIcon box={record.box} />;
      },
    },
    {
      title: 'From',
      dataIndex: 'sender',
      sorter: true,
      ellipsis: true,
      width: 130,
    },
    {
      title: 'To',
      dataIndex: 'receiver',
      sorter: true,
      ellipsis: true,
      width: 130,
    },
    {
      title: 'Subject',
      dataIndex: 'subject',
      sorter: true,
      ellipsis: true,
      hideInSearch: false,
      width: 300,
    },
    {
      title: '',
      dataIndex: 'attachments',
      sorter: false,
      width: 40,
      render(__, record) {
        if (!record.id || !record.attachments?.length) return null;
        return (
          <>
            <AttachmentIconIcon
              id={record.id}
              attachments={record.attachments}
              is_attachment_moved={record.is_attachment_moved}
            />
            &nbsp;
            <span>
              <Popconfirm
                title={<>Are you sure you want to move?</>}
                okText="Yes"
                cancelText="No"
                overlayStyle={{ maxWidth: 300 }}
                onConfirm={() => {
                  const hide = message.loading('Moving attachments...', 0);
                  moveEmailAttachments(sn(record.id))
                    .then((res) => {
                      message.success('Moved successfully.');
                      actionRef.current?.reload();
                    })
                    .catch(Util.error)
                    .finally(hide);
                }}
              >
                {record.is_attachment_moved ? (
                  <CheckOutlined
                    style={{ color: '#52c41a' }}
                    title={'Attachments already moved. Move again!'}
                  />
                ) : (
                  <RightSquareOutlined
                    style={{ color: '#ff8686' }}
                    title={'Move attachments to Scan Folder.'}
                  />
                )}
              </Popconfirm>
            </span>
          </>
        );
      },
    },
    {
      title: 'Body',
      dataIndex: 'text_plain',
      sorter: true,
      ellipsis: true,
      render: (dom, record) => (
        <a
          onClick={() => {
            setCurrentRow(record);
            setShowDetail(true);
          }}
        >
          {record.text_plain}
        </a>
      ),
    },
    {
      title: 'Tasks',
      dataIndex: 'tasks',
      sorter: true,
      width: 200,
      hideInSearch: true,
      render: (dom, record) => {
        return record.tasks?.map((task) => (
          <Popover
            key={task.id}
            placement="topLeft"
            style={{ maxWidth: 400 }}
            content={
              <>
                <h4>{task.title}</h4>
                <div>{task.desc}</div>
              </>
            }
          >
            <Tag style={{ marginBottom: 2, marginTop: 2, cursor: 'pointer' }}>#{task.id}</Tag>
          </Popover>
        ));
      },
    },
    {
      title: 'Date',
      sorter: true,
      dataIndex: 'date',
      valueType: 'dateTime',
      search: false,
      width: 110,
      ellipsis: true,
      defaultSortOrder: 'descend',
      className: 'c-grey text-sm',
      render: (dom, record) => Util.dtToDMYHHMM(record.date),
    },
    {
      title: 'Mail ID',
      dataIndex: 'mail_id',
      width: 70,
      search: false,
      className: 'c-grey text-sm',
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 80,
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            setOpenAssignModal(true);
            setCurrentRow(record);
          }}
          title="Create or assign to tasks..."
        >
          <CheckSquareOutlined />
        </a>,
      ],
    },
  ];

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<API.Email & { linkedType?: number | string }>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_email_grid', {
            linkedType: 1,
          })}
          submitter={{
            submitButtonProps: {
              loading: loading,
              htmlType: 'submit',
            },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => actionRef.current?.reload(),
            render: (form, dom) => {
              return [...dom];
            },
          }}
        >
          <ProFormText name={'sender'} label="From" width={180} placeholder={'From'} />
          <ProFormText name={'receiver'} label="To" width={180} placeholder={'To'} />
          <ProFormText name={'subject'} label="Subject" width={180} placeholder={'Subject'} />
          <ProFormSelect
            name="linkedType"
            label="Linked?"
            width={130}
            mode="single"
            placeholder={'Linked mode'}
            fieldProps={{ onChange: () => actionRef.current?.reload() }}
            options={EmailLinkedType}
          />
          <ProFormCheckbox
            name="includeHidden"
            label="Show hidden?"
            valuePropName="checked"
            getValueFromEvent={(e) => (e.target.checked ? 1 : 0)}
            initialValue={1}
          />
          <ProFormCheckbox
            name="includedAttachmentMoved"
            label="Show moved?"
            valuePropName="checked"
            getValueFromEvent={(e) => (e.target.checked ? 1 : 0)}
            initialValue={0}
          />
        </ProForm>
      </Card>
      <ProTable<API.Email, API.PageParams>
        headerTitle={'Email list'}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              const hide = message.loading('Downloading emails...', 0);
              dsPullEmail({})
                .then((res) => {
                  actionRef.current?.reload();
                  message.success('Downloaded the latest emails successfully.');
                })
                .catch((e) => Util.error(e))
                .finally(() => hide());
            }}
          >
            <DownloadOutlined /> Sync
          </Button>,
        ]}
        pagination={{
          showSizeChanger: true,
          pageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          setLoading(true);
          return getEmailList(
            {
              ...params,
              ...searchFormValues,
            },
            sort,
            filter,
          ).finally(() => setLoading(false));
        }}
        columns={columns}
        tableAlertRender={false}
        tableAlertOptionRender={false}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />

      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <SFooterToolbarExtra
              title={'email'}
              selectedRowsState={selectedRowsState}
              actionRef={actionRef}
            />
          }
        >
          <BatchDeleteAction
            title="email"
            onConfirm={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          />
        </FooterToolbar>
      )}

      {/* {selectedRowsState?.length > 0 && (
        <SFooterToolbar
          title="email"
          selectedRowsState={selectedRowsState}
          setSelectedRows={setSelectedRows}
          actionRef={actionRef}
          handleRemove={handleRemove}
        />
      )} */}

      <EmailTaskAssignmentForm
        modalVisible={openAssignModal}
        handleModalVisible={setOpenAssignModal}
        email={{
          id: currentRow?.id,
          mail_id: currentRow?.mail_id,
          subject: currentRow?.subject,
          text_html: currentRow?.text_html,
          text_plain: currentRow?.text_plain,
        }}
        reloadParent={() => actionRef.current?.reload()}
      />

      <Drawer
        width={'50%'}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && <ViewEmail email={currentRow} />}
      </Drawer>
    </PageContainer>
  );
};

export default EmailList;
