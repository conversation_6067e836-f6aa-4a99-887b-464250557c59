/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';
import { AC_PER_PAGE_PAGINATION } from '@/constants';

const urlPrefix = '/api/email-template';

/** rule GET /api/email-template */
export async function getEmailTemplateList(
  params: API.PageParams & Partial<API.EmailTemplate>,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.EmailTemplate>> {
  return request<API.Result<API.EmailTemplate>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}


export async function getEmailTplACList(params: API.PageParams, sort?: any, filter?: any): Promise<any[]> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize ?? AC_PER_PAGE_PAGINATION,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) =>
    res.message.data.map((x: API.EmailTemplate) => ({
      value: x.id,
      text: `${(x.subject || '').substring(0, 50)}`,
      label: `#${x.id} | ${(x.subject || '').substring(0, 50)}`,
    })),
  );
}

/** put PUT /api/email-template */
export async function updateEmailTemplate(
  data: Partial<API.EmailTemplate>,
  options?: { [key: string]: any },
) {
  return request<API.EmailTemplate>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** put PUT /api/email-template/{id} */
export async function updateOrderEmailTemplate(
  id?: number,
  data?: Partial<API.EmailTemplate> & { by_email_id?: number; type?: string },
  options?: { [key: string]: any },
) {
  return request<API.EmailTemplate>(`${urlPrefix}/${id}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/email-template */
export async function addEmailTemplate(data: API.EmailTemplate, options?: { [key: string]: any }) {
  return request<API.EmailTemplate>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/email-template/{id} */
export async function deleteEmailTemplate(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}



export async function getEmailTemplateMappingFields(params?: Record<string, any>): Promise<any> {
  return request<API.ResultObject<{ fields: Record<string, string> }>>(`${urlPrefix}/mapping-fields`, {
    method: 'GET',
    params,
    paramsSerializer,
    withToken: true,
  }).then((res) => res.message);
}
