import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import update from 'immutability-helper';
import { message, Spin } from 'antd';
import { useCallback, useEffect, useState } from 'react';
// import { updateImageSort, updateImageType } from '@/services/foodstore-one/Item/ean';
import type { DefaultOptionType } from 'antd/lib/select';
import { WorkflowStepOption } from './WorkflowStepOption';
import { updateSysWorkflowStep } from '@/services/app/Sys/sys-workflow-step';
import Util, { sn } from '@/util';

export type OptionTypeExt = DefaultOptionType & { id?: number };

export type DragItem = {
  index: number;
  orgIndex: number;
  id: string;
  type: string;
} & OptionTypeExt;

export type WorkflowStepOptionsCellProps = {
  step?: Partial<API.SysWorkflowStep>;
  onSaveCallback?: (id?: number, data?: Partial<OptionTypeExt>[]) => Promise<void>;
};

const WorkflowStepOptionsCell: React.FC<WorkflowStepOptionsCellProps> = (props) => {
  const { step, onSaveCallback } = props;

  const [loading, setLoading] = useState<boolean>(false);
  const [options, setOptions] = useState<OptionTypeExt[]>([]);

  useEffect(() => {
    setOptions((step?.options || []).map((x, index) => ({ ...x, id: index })));
  }, [step?.options]);

  const moveItem = useCallback((dragIndex: number, hoverIndex: number) => {
    setOptions((prev: OptionTypeExt[]) =>
      update(prev, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, prev[dragIndex] as OptionTypeExt],
        ],
      }),
    );
  }, []);

  const dropFileCallback = useCallback(
    async (item: DragItem) => {
      // console.log(' ==> dropped', item, step?.id);
      /* setLoading(true);
      updateImageSort(step?.id, +item.id, { index: item.index, orgIndex: item.orgIndex })
        .then((res) => {
          setOptions(res);
        })
        .catch((e) => Util.error(e))
        .finally(() => setLoading(false)); */
      setLoading(true);
      updateSysWorkflowStep(sn(step?.id), {
        options: options.map((x) => ({ ...x, id: undefined })),
      })
        .then((__) => {
          message.success('Saved successfully.');
          onSaveCallback?.(step?.id, options);
        })
        .catch((e) => Util.error(e))
        .finally(() => {
          setLoading(false);
        });
    },
    [step?.id, options, onSaveCallback],
  );

  return (
    <Spin spinning={loading}>
      <DndProvider backend={HTML5Backend}>
        {options.map((option: OptionTypeExt, ind: number) => {
          return (
            <WorkflowStepOption
              key={option.id}
              index={ind}
              id={option.id}
              option={option}
              moveCard={moveItem}
              dropFileCallback={dropFileCallback}
            />
          );
        })}
      </DndProvider>
    </Spin>
  );
};

export default WorkflowStepOptionsCell;
