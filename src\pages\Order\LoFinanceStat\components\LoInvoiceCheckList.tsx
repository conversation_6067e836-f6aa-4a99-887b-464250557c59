import React, { useRef, useEffect } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { getLexOfficeLink, nf2 } from '@/util';

import {
  getLoInvoiceCheckList,
  updateLoInvoiceCheck,
} from '@/services/app/LexOffice/lo-voucher-list';
import { CheckSquareOutlined, LinkOutlined } from '@ant-design/icons';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import useCustomerOrSupplier from './useCustomerOrSupplier';
import { Typography, message, Modal } from 'antd';

type LoInvoiceCheckListPropsType = {
  customerOrSupplier?: Partial<(API.Customer | API.Supplier) & { uid?: string }>;
  searchFormRef?: React.MutableRefObject<ProFormInstance<any> | undefined>;
  refreshTick?: number;
  loadFinanceStat?: () => void;
};

const LoInvoiceCheckList: React.FC<LoInvoiceCheckListPropsType> = ({
  customerOrSupplier,
  searchFormRef,
  refreshTick,
  loadFinanceStat,
  ...rest
}) => {
  const actionRef = useRef<ActionType>();
  const rowRef = useRef<API.LoInvoiceCheck>({}); // used for note update

  const { isSupplier, selectedId } = useCustomerOrSupplier(customerOrSupplier);

  useEffect(() => {
    actionRef.current?.reload();
  }, [customerOrSupplier?.uid, refreshTick]);

  const columns: ProColumns<API.LoInvoiceCheck>[] = [
    {
      title: '',
      dataIndex: 'action-check',
      sorter: false,
      width: 20,
      render(__, record) {
        return (
          <Typography.Link
            onClick={() => {
              const hide = message.loading('Updating checked status', 0);
              updateLoInvoiceCheck(record.lo_id, { is_checked: record.is_checked == 1 ? 0 : 1 })
                .then((res) => {
                  actionRef.current?.reload();
                  loadFinanceStat?.();
                })
                .catch((err) => Util.error(err))
                .finally(() => hide());
            }}
          >
            {!record.is_checked ? (
              <CheckSquareOutlined
                style={{ color: 'lightgrey', opacity: 0.4 }}
                title="Make checked."
              />
            ) : (
              <CheckSquareOutlined style={{ color: '#07c807' }} title="Make unchecked." />
            )}
          </Typography.Link>
        );
      },
    },
    {
      title: 'No',
      dataIndex: 'lo_no',
      sorter: true,
      hideInForm: true,
      width: 70,
      showSorterTooltip: false,
      className: 'text-13',
    },
    {
      title: '',
      dataIndex: 'link',
      hideInForm: true,
      width: 20,
      className: 'p-0',
      render: (dom, record) => (
        <a href={getLexOfficeLink(record.lo_type, record.lo_id)} target="_blank" rel="noreferrer">
          <LinkOutlined />
        </a>
      ),
    },
    {
      title: 'Cust No',
      dataIndex: 'lo_cust_no',
      sorter: true,
      hideInForm: true,
      width: 70,
      showSorterTooltip: false,
      className: 'text-13',
    },
    /* {
      title: 'Customer',
      dataIndex: 'org_customer_id',
      sorter: true,
      hideInForm: true,
      width: 130,
      ellipsis: true,
      showSorterTooltip: false,
      render(dom, record, index, action, schema) {
        return record.org_customer?.id
          ? `${record.org_customer.name}${
              record.org_customer.description
                ? ` - ${record.org_customer.description.substring(0, 20)}`
                : ''
            }`
          : null;
      },
    }, */
    {
      title: 'Type',
      dataIndex: 'lo_type',
      sorter: true,
      hideInForm: true,
      width: 70,
      ellipsis: true,
      showSorterTooltip: false,
    },
    /* {
      title: 'Status',
      dataIndex: 'lo_status',
      sorter: true,
      hideInForm: true,
      width: 90,
      showSorterTooltip: false,
    }, */
    {
      title: 'Voucher Date',
      dataIndex: 'lo_voucher_date',
      sorter: true,
      hideInForm: true,
      width: 80,
      showSorterTooltip: false,
      className: 'text-13',
      // defaultSortOrder: 'descend',
      render: (dom, record) => Util.dtToDMY(record.lo_voucher_date),
    },
    {
      title: 'Gross Amount',
      dataIndex: 'total_gross_amount',
      sorter: true,
      hideInForm: true,
      align: 'right',
      width: 100,
      showSorterTooltip: false,
      className: 'text-13',
      render(dom, record, index, action, schema) {
        return nf2(record.total_gross_amount);
      },
    },
    /* {
      title: 'Contact name',
      dataIndex: 'lo_contact_name',
      sorter: true,
      hideInForm: true,
      width: 200,
      showSorterTooltip: false,
    },
    {
      title: 'Archived?',
      dataIndex: 'lo_archived',
      sorter: true,
      hideInForm: true,
      width: 100,
      showSorterTooltip: false,
      render(dom, record, index, action, schema) {
        return record.lo_archived ? 'Archived' : '';
      },
    }, */
    /* {
      title: 'Created on',
      sorter: true,
      dataIndex: 'lo_created_on',
      valueType: 'dateTime',
      search: false,
      width: 150,
      showSorterTooltip: false,
      className: 'c-grey text-sm',
      render: (dom, record) => Util.dtToDMYHHMM(record.lo_created_on),
    },
    {
      title: 'Updated on',
      sorter: true,
      dataIndex: 'lo_updated_on',
      valueType: 'dateTime',
      search: false,
      width: 150,
      showSorterTooltip: false,
      className: 'c-grey text-sm',
      render: (dom, record) => Util.dtToDMYHHMM(record.lo_updated_on),
    }, */
    /* {
      title: 'ID',
      dataIndex: 'lo_id',
      sorter: true,
      copyable: true,
      ellipsis: true,
      width: 100,
      showSorterTooltip: false,
      className: 'text-13',
    }, */
    {
      title: 'Contact Name',
      dataIndex: 'lo_contact_name',
      sorter: true,
      ellipsis: true,
      showSorterTooltip: false,
      className: 'text-13',
      width: 120,
    },
    {
      title: 'Note',
      dataIndex: 'note',
      sorter: false,
      tooltip: 'Click to edit',
      className: 'text-13 c-grey',
      ellipsis: true,
      width: 100,
      onCell: (record) => {
        return {
          className: 'cursor-pointer',
          onClick: () => {
            const modal = Modal.confirm({
              title: 'Update note',
              icon: false,
              content: (
                <>
                  <ProFormTextArea
                    placeholder={'Enter your note'}
                    fieldProps={{
                      defaultValue: record.note,
                      onChange: (e: any) => {
                        rowRef.current = { ...rowRef.current, note: e.target.value };
                      },
                    }}
                  />
                </>
              ),
              onOk: (close) => {
                const hide = message.loading('Updating note...', 0);
                modal.update({ okButtonProps: { disabled: true } });
                updateLoInvoiceCheck(record.lo_id, { note: rowRef.current?.note })
                  .then((res) => {
                    actionRef.current?.reload();
                    close();
                  })
                  .catch((err) => {
                    Util.error(err);
                    modal.update({ okButtonProps: { disabled: false } });
                  })
                  .finally(() => hide());
              },
            });
          },
        };
      },
    },
  ];

  return (
    <>
      <ProTable<API.LoInvoiceCheck, API.PageParams>
        headerTitle={<>Invoices list</>}
        actionRef={actionRef}
        rowKey="lo_id"
        revalidateOnFocus={false}
        options={{ fullScreen: false, density: false, search: false, setting: false }}
        size="small"
        search={false}
        scroll={{ x: 600 }}
        pagination={{
          showSizeChanger: true,
          pageSize: 10,
        }}
        cardProps={{ bodyStyle: { padding: 0 } }}
        params={
          {
            customerId: isSupplier ? null : selectedId,
            supplierId: isSupplier ? selectedId : null,
            lo_types: ['invoice'],
            nlo_statuses: ['draft'],
            srcPage: 'financialSummary',
          } as any
        }
        request={async (params, sort, filter) => {
          if (!selectedId) return Promise.resolve([]);
          const searchFilters = searchFormRef?.current?.getFieldsValue();
          const res = await getLoInvoiceCheckList(
            { ...searchFilters, ...params },
            Object.keys(sort).length < 1 ? { lo_voucher_date: 'descend', lo_no: 'descend' } : sort,
            filter,
          );
          return res;
        }}
        rowClassName={(record) =>
          (record.lo_status == 'voided' ? 'stroke-all' : '') +
          (record.is_checked == 1 ? ' c-grey-all' : '')
        }
        onRequestError={(err) => Util.error(err)}
        columns={columns}
        columnEmptyText=""
        toolBarRender={() => [
          <span key="1">{(customerOrSupplier as any).default_payment_term ?? ''}</span>,
        ]}
        /* toolBarRender={() => [
          <Button
            type="primary"
            key="ds-lo"
            size="small"
            className="btn-green"
            icon={<SyncOutlined />}
            title="Sync invoices & credit notes from Lex Office"
            onClick={() => {
              const hide = message.loading(
                'Down syncing invoices and credit notes from Lex office...',
                0,
              );
              dsLatestLoVoucherList()
                .then((res) => actionRef.current?.reload())
                .catch((res) => {
                  message.error('Failed to sync data.');
                })
                .finally(() => hide());
            }}
          >
            Sync LO Invoices
          </Button>,
        ]} */
      />
    </>
  );
};

export default LoInvoiceCheckList;
