/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/lo-payment';

/** rule GET /api/lo-payment */
export async function getLoPaymentList(
  params: API.PageParams & Partial<API.LoPayment>,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.LoPayment>> {
  return request<API.Result<API.LoPayment>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** 
 * POST /api/lo-payment 
 * 
 * Create a new payment record, but "balance" only!
 * */
export async function createLoPayment(data: Partial<API.LoPayment>, options?: { [key: string]: any },
): Promise<API.LoPayment> {
  return request<API.ResultObject<API.LoPayment>>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  }).then(res => res.message);
}


/** 
 * PUT /api/lo-payment 
 * 
 * Update a payment record, but "balance" only!
 * */
export async function updateLoPayment(id: number, data: Partial<API.LoPayment>, options?: { [key: string]: any },
): Promise<API.LoPayment> {
  return request<API.ResultObject<API.LoPayment>>(`${urlPrefix}/${id}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then(res => res.message);
}

/** 
 * PUT /api/lo-payment 
 * 
 * Update a payment record without customer/supplier info!
 * */
export async function updateLoPaymentPartial(id: number, data: Partial<API.LoPayment>, options?: { [key: string]: any },
): Promise<API.LoPayment> {
  return request<API.ResultObject<API.LoPayment>>(`${urlPrefix}/${id}/partial`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then(res => res.message);
}

/** 
 * POST /api/lo-payment/bulk 
 * 
 * Create payments records by TSV(Tab Separate Value) data.
 * */
export async function createLoPaymentBulk(
  data: { data?: string },
  options?: { [key: string]: any },
) {
  return request<API.LoPayment>(`${urlPrefix}/bulk`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** put PUT /api/lo-payment/set-customer-or-supplier */
export async function updateCustomerOrSupplierID(ids: number[], customerId?: number, isSupplier?: boolean, options?: { [key: string]: any },
) {
  return request<API.LoPayment>(`${urlPrefix}/set-customer-or-supplier`, {
    method: 'PUT',
    data: {
      ids: ids,
      customerId,
      isSupplier,
    },
    ...(options || {}),
  });
}

/** delete DELETE /api/lo-payment/{id} */
export async function deleteLoPayment(id?: number, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}




