import { Col, Row, Typography } from 'antd';
import TaskActions from '../Sub/TaskActions';
import type { TaskProps } from '../Task';

type TaskLayoutAProps = TaskProps;

const TaskLayoutA: React.FC<TaskLayoutAProps> = (props) => {
  const { task } = props;

  return (
    <>
      <Row wrap={false} gutter={16} className="ant-row-middle">
        <Col flex={'auto'}>
          <Typography.Paragraph ellipsis={true} style={{ marginBottom: 0 }}>
            {task.title}
          </Typography.Paragraph>
        </Col>
        <Col flex={'0 0 50px'} className="text-right">
          <span className="text-sm c-grey">#{task.id}</span>
        </Col>
        <TaskActions {...props} />
      </Row>
    </>
  );
};

export default TaskLayoutA;
