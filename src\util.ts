import { message, notification } from 'antd';
import type { DefaultOptionType } from 'antd/lib/select';
import type { RcFile } from 'antd/lib/upload';
import _, { round } from 'lodash';
import moment from 'moment';
import type { KeyboardEvent } from 'react';
import { CURRENT_YEAR, DT_FORMAT_DMY, DT_FORMAT_MY, DT_FORMAT_TIME_MAX_S, DT_FORMAT_YMD, LS_PREFIX, OrderPrefix } from './constants';
import type { ArgsProps } from 'antd/lib/notification';

const safeInt = (valParam: any) => {
  if (!valParam) return 0;
  const val = parseInt(valParam, 10);
  if (isNaN(val)) return 0;
  else return val;
};

const safeNumber = (valParam: any, decimals?: number) => {
  if (!valParam) return 0;
  const val = Number(valParam);
  if (isNaN(val)) return 0;
  else return typeof decimals !== 'undefined' ? round(val, decimals) : val;
};
export const sn = (valParam: any, decimals?: number) => {
  return safeNumber(valParam, decimals);
};

export const urlFull = (route: string, withoutOrigin?: boolean) => {
  const routerBase = (window as any).routerBase;
  return (withoutOrigin ? '' : window.location.origin) + routerBase + (route[0] == '/' ? route.substring(1) : route);
};

function isZero(value: number, digits?: number) {
  const valueNew = round(value, (digits ?? 0) + 1);
  return !(valueNew > 0 || valueNew < 0);
}

export const numTo2Digits = (num?: string | number): string => {
  const n = sn(num);
  if (n < 10) return `0${n}`;
  else return `${n}`;
};

const numberFormat = (value: any, zero_show?: boolean, digits = 0, removeLeadingZero = false) => {
  if (isZero(value, digits)) {
    return typeof zero_show == 'undefined' || !zero_show ? '' : removeLeadingZero && !zero_show ? '' : 0;
  }

  const config: Intl.NumberFormatOptions = {
    minimumFractionDigits: removeLeadingZero ? 0 : digits,
    maximumFractionDigits: digits,
    signDisplay: 'auto',
  };

  // eslint-disable-next-line no-param-reassign
  if (isZero(value, digits)) value = 0;

  const x = new Intl.NumberFormat('de-DE', {
    ...config,
    trailingZeroDisplay: 'auto',
  } as any).format(value);

  if (x == '-0') return zero_show ? 0 : '';

  return x;
};

const numberFormatEn = (value: any, zero_show?: boolean, digits = 0) => {
  if (isZero(value)) return typeof zero_show == 'undefined' || !zero_show ? '' : 0;
  const x = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: digits,
    maximumFractionDigits: digits,
  }).format(value);

  return x;
};

export const ni = (value: any, zero_show?: boolean) => {
  return numberFormat(value, zero_show);
};

export const nf2 = (value: any, zero_show?: boolean, removeLeadingZero?: boolean) => {
  return numberFormat(value, zero_show, 2, removeLeadingZero);
};

export const nf3 = (value: any, zero_show?: boolean, removeLeadingZero?: boolean) => {
  return numberFormat(value, zero_show, 3, removeLeadingZero);
};

export const isSnDiff = (value1?: any, value2?: any, margin = 0.05) => {
  return Math.abs(sn(value1) - sn(value2)) > margin;
};



const error = (errorMsg: string | any, duration = 0) => {
  if (!errorMsg) return;
  const notProp: ArgsProps = { message: null, placement: 'top' };
  if (typeof errorMsg === 'string') {
    notProp.message = errorMsg;
  } else {
    const obj: any = errorMsg.data ?? errorMsg;
    if (obj.message) {
      if (typeof obj.message == 'string') {
        notProp.message = obj.message;
      }

      if (process.env.NODE_ENV === 'development' && obj?.trace) {
        notProp.description = JSON.stringify(obj?.trace || '');
      }
    }

    // We suppress trace detail in 401 or 403
    if (obj?.code === 401 || obj?.code == 403) {
      notProp.description = null;
      notProp.duration = 5;
    }
  }
  if (notProp.message || notProp.description) {
    notification.error({ duration, ...notProp });
  }
};

const safeJsonParse = (val: any) => {
  if (!val) return null;
  if (typeof val == 'string') {
    try {
      return JSON.parse(val);
    } catch (e) {
      return null;
    }
  } else {
    return val;
  }
};

const waitTime = async (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

const LS_APP_SETTING = 'WHC_TASK_APP_SETTING';
const lsUpdate = (path: any, value: any, override?: boolean) => {
  const old = localStorage.getItem(LS_APP_SETTING) ?? '{}';
  const oldObj = safeJsonParse(old);
  if (override) _.set(oldObj, path, typeof (value === 'string') ? value : JSON.stringify(value));
  else _.set(oldObj, path, typeof (value === 'string') ? value : JSON.stringify(value));
  // console.log(oldObj);
  localStorage.setItem(LS_APP_SETTING, JSON.stringify(oldObj));
};
const lsGet = (path: any) => {
  const old = localStorage.getItem(LS_APP_SETTING) ?? '{}';
  const oldObj = safeJsonParse(old);
  return _.get(oldObj, path);
};

const dtToMY = (value: any) => {
  if (!value) return undefined;
  const m = moment(value);
  if (m.isValid()) return m.format(DT_FORMAT_MY);
  return undefined;
};

/**
 * Date formatter value. Hybrid usage by `format`
 *
 * @param value
 * @param format
 * @param defaultInvalidValue
 * @returns
 */
const dtToDMY = (value: any, format?: string, defaultInvalidValue?: any, tzoffset?: number) => {
  if (!value) return defaultInvalidValue;
  const m = moment(value);
  if (m.isValid() && tzoffset) {
    m.add(tzoffset, 'hours');
  }

  if (m.isValid()) return m.format(format ?? DT_FORMAT_DMY);
  return defaultInvalidValue;
};

const dtToDMYHHMM = (value: any, invalidValue?: any) => {
  return dtToDMY(value, `${DT_FORMAT_DMY} HH:mm`, invalidValue);
};

const dtToHHMM = (value: any, invalidValue?: any) => {
  return dtToDMY(value, `HH:mm`, invalidValue);
};

const dtBrowserTzOffset = () => -new Date().getTimezoneOffset() / 60;

/**
 * Get local date from datetime in GMT-0 timezone.
 *
 * @param value
 * @param invalidValue
 * @returns
 */
const dtToDMYHHMMTz = (value: any, invalidValue?: any) => {
  const tzoffset = dtBrowserTzOffset();
  return dtToDMY(value, `${DT_FORMAT_DMY} HH:mm`, invalidValue, tzoffset);
};

/**
 * Note: srcFormat may be variable.
 * @param value
 * @param srcFormat
 * @returns
 */
const dtToYMD = (value: any, srcFormat = DT_FORMAT_DMY) => {
  if (!value) return undefined;
  const m = typeof value === 'string' ? moment(value, value.includes('-') ? DT_FORMAT_YMD : srcFormat) : moment(value);
  if (m.isValid()) return m.format(DT_FORMAT_YMD);
  return undefined;
};

export const dtYw = (value: string) => {
  const arr = `${value}`.split('|');
  if (arr.length > 2) {
    return `${dtToDMY(arr[1], 'DD.MM.')} ~ ${dtToDMY(arr[2], 'DD.MM.')}`;
  }
  return '';
};

export const dtYwNo = (value: string) => {
  const arr = `${value}`;
  if (arr.length >= 6) {
    const year = +arr.substring(0, 4);
    if (!year) return '';
    return `${+arr.substring(4, 6)}` + (year != CURRENT_YEAR ? " '" + Math.abs(year - 2000) : '');
  }
  return '';
};

const getBase64 = (file: RcFile): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (errorInner) => reject(errorInner);
  });

function isTabPressed(e: KeyboardEvent) {
  return (e.which && e.which == 9) || (e.keyCode && e.keyCode == 9) || e.key == 'Tab';
}

function isEnterKey(e: KeyboardEvent) {
  return (e.which && e.which == 13) || (e.keyCode && e.keyCode == 13) || e.key == 'Enter';
}

function isFirefox() {
  return navigator.userAgent.search('Firefox') > -1;
}

/**
 * Get formatted prices: gross and netPrice
 * @param priceParam
 * @param vat  % unit
 * @returns
 */
/* function fPrices(netPriceParam: any, vat: any, zeroShow = false, isNet = true): string[] {
  const price = safeNumber(netPriceParam);
  const grossPrice = isNet
    ? price * (1 + safeNumber(vat) / 100)
    : price / (1 + safeNumber(vat) / 100);
  const prices = ['0', '0'];

  if (zeroShow) {
    prices[0] = price.toFixed(2);
    prices[1] = grossPrice.toFixed(2);
  } else {
    prices[0] = isZero(price) ? '' : price.toFixed(2);
    prices[1] = isZero(grossPrice) ? '' : grossPrice.toFixed(2);
  }
  return isNet ? prices : [prices[1], prices[0]];
} */

export const toRound = function (number: number, precision: number) {
  if (precision < 0) {
    const factor = Math.pow(10, precision);
    return Math.round(number * factor) / factor;
  } else return +(Math.round(Number(number + 'e+' + precision)) + 'e-' + precision);
};

function fPrices(netPriceParam: any, vat: any, zeroShow = false, isNet = true): string[] {
  const precision = 7;
  const price = safeNumber(netPriceParam);
  const grossPrice = isNet
    ? price * (1 + safeNumber(vat) / 100)
    : price / (1 + safeNumber(vat) / 100);
  const prices: any[] = ['0', '0'];

  if (zeroShow) {
    prices[0] = toRound(price, precision);
    prices[1] = toRound(grossPrice, precision);
  } else {
    prices[0] = isZero(price) ? '' : toRound(price, precision);
    prices[1] = isZero(grossPrice) ? '' : toRound(grossPrice, precision);
  }
  return isNet ? prices : [prices[1], prices[0]];
}

/**
 * Safely get divided value
 * @param price any
 * @param case_qty any
 */
export function casePrice(price: any, caseQty: any) {
  const val = sn(price);
  const safeCaseQty = sn(caseQty);
  return caseQty ? val / safeCaseQty : val;
}

export const sCap = (str: string | undefined | null) => {
  return `${str || ''}`.replace(/\w+/g, (word: string) => {
    return word.charAt(0).toUpperCase() + word.slice(1);
  });
};

const sInitials = (str: string) => {
  return str
    ?.match(/(\b\S)?/g)
    ?.join('')
    .toUpperCase();
};

export const nl2br = (str: any) => {
  return (str ?? '').replaceAll(/\n/gi, '<br />');
}

export function sEllipsed(str = '', width = 10, endWidth?: number): string {
  if (str && str.length <= width) {
    return str;
  }
  if (endWidth) return str ? `${str.slice(0, width)}...${str.slice(-endWidth)}` : '';
  else return str ? `${str.slice(0, width)}...` : '';
}

/**
 * Search Form setting
 * @param key
 * @param defaultValue
 * @returns
 */
const getSfValues = (key: string, defaultValue?: any) => {
  return safeJsonParse(localStorage.getItem(`${LS_PREFIX}${key}`)) ?? defaultValue ?? {};
};

const setSfValues = (key: string, data?: any) => {
  localStorage.setItem(`${LS_PREFIX}${key}`, JSON.stringify(data));
};

export const isOrderTask = (refType?: string) => refType == 'OrderIn' || refType == 'OrderOut';
export const getOrderTaskNo = (refType?: string, orderNo?: string | number) => {
  const prefix = refType == 'OrderIn' || refType == 'OrderOut' ? `${OrderPrefix[refType]} ` : '';
  return `${prefix}${orderNo ?? '-'}`;
};

export const getOrderSummary = (refType?: string, ref?: API.OrderIn & API.OrderOut) => {
  let summary = getOrderTaskNo(refType, ref?.order_no);
  const supplierOrCustomer = refType == 'OrderIn' ? ref?.supplier : ref?.customer;
  if (supplierOrCustomer) {
    summary += ' - ' + supplierOrCustomer.substring(0, 15);
  }
  if (ref?.desc) {
    summary += ' - ' + ref?.desc;
  }
  return summary;
};

export const removeOrderPrefix = (refType?: string, title?: string) => {
  if (!title) return '';
  if (refType == 'OrderOut') {
    return title.substring(0, 3) == 'WA ' ? title.substring(3) : title;
  } else if (refType == 'OrderIn') {
    return title.substring(0, 3) == 'WE ' ? title.substring(3) : title;
  }
  return '';
};

export const inCsvStr = (keyStr: string, csvStr: string) => {
  return `,${csvStr},`.includes(`,${keyStr},`);
};

const genNewKey = (prefix?: string) => (prefix ?? `key_${_.now()}_`) + _.uniqueId();

type TmpFuncType = (x: any) => any;

const getOptionValues = (
  arr: any[],
  valueField: string | TmpFuncType,
  labelField: string | TmpFuncType,
): DefaultOptionType[] => {
  return arr.map((x) => ({
    value: typeof valueField === 'string' ? x[valueField] : valueField(x),
    label: typeof labelField === 'string' ? x[labelField] : labelField(x),
  }));
};

export type DateRangeType = {
  from?: string;
  to?: string;
  title?: string;
};

/**
 * Build date range by Range type
 *
 * @param intervalType
 * @param lastInterval
 * @returns
 */
const dtBuildRanges = (intervalType: 'd' | 'w' | 'm', lastInterval: number, offset?: number, isReverse?: boolean) => {
  const ranges: DateRangeType[] = [];
  const startInd = offset ?? 0;

  for (let ind = startInd; ind < lastInterval + startInd; ind++) {
    let range: DateRangeType = {};
    if (intervalType == 'd') {
      range = {
        from: moment().subtract(ind, 'days').format(DT_FORMAT_YMD),
        to: moment().subtract(ind, 'days').format(DT_FORMAT_YMD) + DT_FORMAT_TIME_MAX_S,
      };
      range.title = dtToDMY(range.from, 'DD.MM.', '');
    } else if (intervalType == 'w') {
      range = {
        from: moment().startOf('isoWeek').subtract(ind, 'w').format(DT_FORMAT_YMD),
        to:
          moment().endOf('isoWeek').subtract(ind, 'w').format(DT_FORMAT_YMD) + DT_FORMAT_TIME_MAX_S,
      };
      range.title = dtToDMY(range.from, 'DD.MM.', '') + '~' + dtToDMY(range.to, 'DD.MM.', '');
    } else {
      range = {
        from: moment().startOf('month').subtract(ind, 'months').format(DT_FORMAT_YMD),
        to:
          moment().endOf('month').subtract(ind, 'months').format(DT_FORMAT_YMD) +
          DT_FORMAT_TIME_MAX_S,
      };
      range.title = dtToDMY(range.from, 'MMM `YY', '');
    }
    ranges.push(range);
  }

  return isReverse ? ranges.reverse() : ranges;
};

const strpad = (str: string, length = 2, prefix = '0') => {
  return str.padStart(length, prefix);
}

const dtAbsoluteMonths = (str: string) => {
  const months = Number(moment(str).format("MM"));
  const years = Number(moment(str).format("YYYY"));
  return months + (years * 12);
}

const dtMonthDiff = (start: string, end: string) => {
  const startAbsMonth = dtAbsoluteMonths(start);
  const endAbsMonth = dtAbsoluteMonths(end);
  return endAbsMonth - startAbsMonth;
}

const dtBuildRangesFromLastMonth = (intervalType: 'd' | 'w' | 'm', lastInterval: number, lastMonthStr: string, isReverse?: boolean) => {
  /* const offset = dtMonthDiff(new Date().toISOString(), lastMonthStr);
  console.log(' ---> ', new Date().toISOString(), lastMonthStr, offset);
  return dtBuildRanges(intervalType, lastInterval, offset, isReverse); */

  const ranges: DateRangeType[] = [];
  const startInd = 0;

  for (let ind = startInd; ind < lastInterval + startInd; ind++) {
    let range: DateRangeType = {};
    if (intervalType == 'd') {
      range = {
        from: moment(lastMonthStr).subtract(ind, 'days').format(DT_FORMAT_YMD),
        to: moment(lastMonthStr).subtract(ind, 'days').format(DT_FORMAT_YMD) + DT_FORMAT_TIME_MAX_S,
      };
      range.title = dtToDMY(range.from, 'DD.MM.', '');
    } else if (intervalType == 'w') {
      range = {
        from: moment(lastMonthStr).startOf('isoWeek').subtract(ind, 'w').format(DT_FORMAT_YMD),
        to:
          moment(lastMonthStr).endOf('isoWeek').subtract(ind, 'w').format(DT_FORMAT_YMD) + DT_FORMAT_TIME_MAX_S,
      };
      range.title = dtToDMY(range.from, 'DD.MM.', '') + '~' + dtToDMY(range.to, 'DD.MM.', '');
    } else {
      range = {
        from: moment(lastMonthStr).startOf('month').subtract(ind, 'months').format(DT_FORMAT_YMD),
        to:
          moment(lastMonthStr).endOf('month').subtract(ind, 'months').format(DT_FORMAT_YMD) +
          DT_FORMAT_TIME_MAX_S,
      };
      range.title = dtToDMY(range.from, 'MMM `YY', '');
    }
    ranges.push(range);
  }

  return isReverse ? ranges.reverse() : ranges;
}

const dtBuildRangesInFy = (now?: Date | string | number, isReverse?: boolean, includeLastMonth?: boolean): DateRangeType[] => {
  const baseDt = now ? (typeof now == 'string' ? new Date(now) : (typeof now === 'number' ? new Date(`${1 + now}-06-30`) : now)) : new Date();
  // Year & Month of 'Now' ('Now' depends on input parameter)
  const year = baseDt.getFullYear();
  const month = baseDt.getMonth();

  // Year & Month of start month of fiscal year based on year/month
  const fyYear = month >= 6 ? year : year - 1;
  const fyMonth = 6;  // July
  const ranges: DateRangeType[] = [];

  const todayYear = new Date().getFullYear();
  const todayMonth = new Date().getMonth();
  let fyLastMonth = month + (includeLastMonth ? 1 : 0);
  if (year == todayYear && fyLastMonth > todayMonth) {
    fyLastMonth = todayMonth;
  }

  let m = fyMonth;
  for (let y = fyYear; y <= year; y++) {
    while (!(y == year && m == fyLastMonth)) {
      const mm = pad(m + 1, 2);
      const range = {
        from: `${y}-${mm}-01`,
        to: `${y}-${mm}-${new Date(y, m + 1, 0).getDate()}`,
        title: dtToDMY(`${y}-${mm}-01`, 'MMM `YY', '')
      };
      ranges.push(range);
      if (m == 11) {
        y++;
      }
      // increase month
      m = (m + 1) % 12;
    }
  }

  return isReverse ? ranges.reverse() : ranges;
}


const dtCurrentFy = (today?: string | Date): number => {
  let dt;
  if (today) {
    dt = typeof today === 'string' ? new Date(today) : today;
  } else {
    dt = new Date();
  }
  return (dt.getMonth() < 6) ? dt.getFullYear() - 1 : dt.getFullYear();
}

const dtBuildFyKv = (lastYear?: number, length?: number): Record<number | string, string> => {
  const ret: Record<number, string> = {};
  for (let i = 0; i < (length ?? 10); i++) {
    const y = (lastYear ?? (dtCurrentFy() + 1)) - i;
    ret[y] = `${y % 2000}/${(y + 1) % 2000}`;
  }
  return ret;
}


/**
 * Build fiscal years list.
 * @param now 
 * @param isReverse 
 * @param includeLastMonth 
 */
const dtBuildFyList = (lastYear?: number, length?: number): DefaultOptionType[] => {
  const kv = dtBuildFyKv(lastYear, length);
  return Object.keys(kv).sort((a, b) => a < b ? 1 : -1).map(y => ({
    value: +y,
    label: kv[`${y}`]
  }));
}

function pad(a: number, b: number) {
  return (1e15 + a + '').slice(-b);
}


export const getLexOfficeLink = (type?: string, id?: string) => {
  let subLink = 'invoices';
  if (type == 'creditnote') subLink = 'credit-notes';
  else if (type == 'order_confirmation') subLink = 'order-confirmations';
  else if (type == 'quotation') subLink = 'quotations';
  return `https://app.lexoffice.de/permalink/${subLink}/view/${id}`;
}

export const urlLODocumentOnSystem = (order_no?: number, id?: string) => {
  return (`${API_URL}/data/lo-invoices/${order_no}-${id}.pdf`);
}

export const kvToOptions = (kv: Record<string | number, any>, getLabel?: any): DefaultOptionType[] => {
  return Object.keys(kv).map(k => ({
    value: k,
    label: getLabel ? getLabel(k) : kv[k]
  }));
}

export const getFileExt = (fileName: string) => {
  const arr = fileName.split('.');

  return arr.length ? arr[arr.length - 1].toLowerCase() : '';
}

export const isImageFile = (fileName: string) => {
  const ext = getFileExt(fileName);
  return ext === 'png' || ext === 'jpg' || ext === 'jpeg' || ext === 'bmp' || ext === 'gif';
}

export const isPdfFile = (fileName: string) => {
  const ext = getFileExt(fileName);
  return ext === 'pdf';
}


const flashSysMsg = (resData: API.BaseResult) => {
  const flashMsg = resData?.messageFlash;
  if (flashMsg) {
    for (const key in flashMsg) {
      const msgStr = flashMsg[key].join('\n');
      if (key == 'w') {
        // message.warning(msgStr, 10);
        notification.warning({ message: null, description: msgStr, duration: 0, placement: 'top' });
      } else if (key == 'e') {
        // message.error(msgStr, 10);
        notification.error({ message: null, description: msgStr, duration: 0, placement: 'top' });
      } else if (key == 's') {
        message.success(msgStr, 10);
      } else if (key == 'i') {
        // message.info(msgStr, 10);
        notification.error({ message: null, description: msgStr, duration: 0, placement: 'top' });
      }
    }
  }
};

/**
 * Get valid & formatted data from steps
 * 
 * @param stepData 
 * @param stepParam 
 * @returns 
 */
export const getWfStepData = (stepData?: API.OrderOutWorkflowStep[] | API.OrderOutSupWorkflowStep[] | API.TaskWorkflowStep[], stepParam?: API.SysWorkflowStep) => {
  const step = stepData?.[0]?.step ?? stepParam;
  if (stepData && stepData?.length) {
    let value = stepData?.[0]?.value;
    const fieldType = step?.field_type;
    if (value) {
      if (fieldType == 'date') {
        value = dtToDMY(value);
      } else if (fieldType == 'daterange') {
        value = dtToDMY(value);
        if (stepData?.[0]?.value2) {
          value += ' ~ ' + dtToDMY(stepData?.[0]?.value2);
        }
      } else if (fieldType == 'checkbox' || fieldType == 'multiselect') {
        value = stepData.map(x => step?.options?.find(s => s.value == x.value)?.label || '').join(', ');
      } else if (fieldType == 'radio' || fieldType == 'select' || fieldType == 'switch') {
        value = step?.options?.find(x => x.value == value)?.label;
      } else if (fieldType == 'number') {
        value = numberFormat(value, true, 2, true);
      }
    }
    return value;
  }
}


const Util = {
  waitTime,
  safeInt,
  isZero,
  sn,
  safeNumber,
  numberFormat,
  numberFormatEn,
  error,
  lsUpdate,
  lsGet,
  safeJsonParse,
  dtToMY,
  dtToDMY,
  dtToDMYHHMM,
  dtToYMD,
  dtBuildRanges,
  dtBuildRangesInFy,
  dtBuildFyList,
  dtBuildFyKv,
  dtCurrentFy,
  dtBuildRangesFromLastMonth,
  dtAbsoluteMonths,
  dtMonthDiff,

  getBase64,
  isTabPressed,
  isEnterKey,
  fPrices,

  sCap,
  getSfValues,
  setSfValues,
  sInitials,
  strpad,

  urlFull,
  genNewKey,
  getOptionValues,

  kvToOptions,
  pad,
  nl2br,
};
export default Util;
