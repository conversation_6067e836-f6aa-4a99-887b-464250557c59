import type { Dispatch, SetStateAction } from 'react';
import { useMemo, useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Col, message, Modal, Popover, Row, Typography } from 'antd';
import { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { DeleteFilled } from '@ant-design/icons';
import type { ActionType, EditableFormInstance, ProColumns } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import _ from 'lodash';
import {
  getAddressMappingFields,
  getSysAddressXlsTpl,
  updateMappingDetails,
} from '@/services/app/Sys/sys-address-xls-tpl';
import Util from '@/util';
import { InfoCircleOutlined } from '@ant-design/icons/lib/icons';

const getNewKey = (index: number) => 'key_' + _.uniqueId();

export type EditableRowDataType = API.SysAddressXlsTplDetail;

export type XlsTplDetailsProps = {
  tpl?: API.SysAddressXlsTpl;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
};

const XlsTplDetails: React.FC<XlsTplDetailsProps> = (props) => {
  const [loading, setLoading] = useState(false);
  const [tplData, setTplData] = useState<API.SysAddressXlsTpl | undefined>(props.tpl);
  const [mappingData, setMappingData] = useState<{ fields?: any }>({ fields: {} });

  // Editable table form
  const editableFormRef = useRef<EditableFormInstance>();
  const actionRef = useRef<ActionType>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);
  const [dataSource, setDataSource] = useState<EditableRowDataType[]>(() => []);

  useEffect(() => {
    if (props.modalVisible && props.tpl?.id) {
      setLoading(true);
      Promise.all([
        getSysAddressXlsTpl(props.tpl.id).then((res) => setTplData(res)),
        getAddressMappingFields(props.tpl?.id).then((res) => setMappingData(res)),
      ]).finally(() => setLoading(false));
    }
  }, [props.modalVisible, props.tpl?.id]);

  useEffect(() => {
    if (props.modalVisible && tplData) {
      setDataSource(tplData.details ?? []);
      setEditableRowKeys((tplData.details ?? []).map((x) => x.id));
    }
  }, [props.modalVisible, tplData]);

  const srcFieldOptions = useMemo(() => {
    if (!mappingData?.fields) return [];
    return Object.keys(mappingData.fields).map((x) => ({
      value: `{${x}}`,
      label: `${mappingData.fields[x]}`,
    }));
  }, [mappingData?.fields]);

  const columns: ProColumns<EditableRowDataType>[] = [
    {
      title: 'Type',
      dataIndex: ['mapping_type'],
      valueType: 'select',
      valueEnum: {
        default: 'Default',
        custom: 'Custom',
        list: 'List',
      },
      width: 80,
    },
    {
      title: 'Field',
      dataIndex: ['src_field'],
      width: 300,
      formItemProps: (form) => {
        const rules = [{ required: true, message: 'Field is required!' }];
        return {
          rules,
          hasFeedback: false,
        };
      },
      className: 'm-0',
      onCell: () => {
        return {};
      },
      renderFormItem(schema, config) {
        const type = config.record?.mapping_type;
        return type == 'custom' ? (
          <ProFormText formItemProps={{ ...schema.formItemProps, style: { marginBottom: 0 } }} />
        ) : (
          <ProFormSelect
            showSearch
            options={srcFieldOptions}
            fieldProps={{ ...schema.fieldProps, dropdownMatchSelectWidth: false } as any}
            formItemProps={{ ...schema.formItemProps, style: { marginBottom: 0 } }}
          />
        );
      },
    },
    {
      title: 'XLS Cell',
      dataIndex: ['xls_field'],
      width: 200,
      formItemProps: (form) => {
        const rules = [{ required: false, message: 'XLS Cell is required!' }];
        return {
          rules,
          hasFeedback: false,
        };
      },
    },
    {
      title: 'Conditional?',
      dataIndex: ['is_conditional'],
      valueType: 'switch',
      align: 'center',
      width: 70,
    },
    {
      title: 'Cond. Field',
      dataIndex: ['condition_field'],
      valueType: 'switch',
      width: 120,
      renderFormItem(schema) {
        return (
          <ProFormSelect
            options={srcFieldOptions}
            formItemProps={{ ...schema.formItemProps, style: { marginBottom: 0 } }}
          />
        );
      },
      editable: (value, record) => !!record.is_conditional,
    },
    {
      title: 'Condition',
      dataIndex: ['condition_type'],
      valueType: 'select',
      valueEnum: {
        eq: 'Equal',
        neq: 'Not Equal',
      },
      width: 120,
      editable: (value, record) => !!record.is_conditional,
    },
    {
      title: 'Cond. Value',
      dataIndex: ['condition_value'],
      width: 120,
      editable: (value, record) => !!record.is_conditional,
    },
    {
      title: 'Cond. XLS Cell',
      dataIndex: ['xls_field2'],
      width: 80,
      editable: (value, record) => !!record.is_conditional,
    },
    /* {
      title: 'Settings',
      dataIndex: ['settings'],
      width: 150,
      // editable: (value, record) => record.mapping_type == 'list',
      editable: false,
    }, */
    {
      title: 'Option',
      valueType: 'option',
      width: 50,
      fixed: 'right',
      align: 'center',
      render: (__) => {
        return null;
      },
    },
  ];

  return (
    <Modal
      title={
        <Row>
          <Col>{`XLS Mapping - ${tplData?.name ?? 'N/A'}`}</Col>
          <Col style={{ marginLeft: 'auto', marginRight: 40 }}>
            <Popover
              title="Mapping code."
              content={
                <div style={{ maxHeight: 500, overflowY: 'auto' }}>
                  <Alert
                    message="List Type"
                    description={
                      <>
                        <div>XLS Cell example: h10:desc,I10:hs_code,j10:weight</div>
                        <div>List Type cannot be used in Custom type</div>
                      </>
                    }
                    type="info"
                  />

                  {srcFieldOptions.map((x) => (
                    <Typography.Paragraph
                      key={x.value}
                      copyable={{ text: x.value }}
                      style={{ marginBottom: 2, fontSize: 12 }}
                    >
                      {x.label} : <span style={{ fontWeight: 'bold' }}>{x.value}</span>
                    </Typography.Paragraph>
                  ))}
                </div>
              }
            >
              <InfoCircleOutlined title="View mapping code definitions for advanced usages." />
            </Popover>
          </Col>
        </Row>
      }
      width="1300px"
      open={props.modalVisible}
      onCancel={() => props.handleModalVisible(false)}
      footer={
        <>
          <Button
            type="primary"
            onClick={() => {
              editableFormRef.current
                ?.validateFields()
                .then(() => {
                  setLoading(true);
                  updateMappingDetails(props.tpl?.id, { details: dataSource })
                    .then(() => {
                      message.success('Successfully updated.');
                    })
                    .catch((err) => Util.error(err))
                    .finally(() => setLoading(false));
                })
                .catch((validationDetail) => {
                  message.error(
                    validationDetail.errorFields.map((d: any, ind: number) => (
                      // eslint-disable-next-line react/no-array-index-key
                      <div key={ind}>{d.errors.join(' ')}</div>
                    )),
                  );
                });
            }}
          >
            Save
          </Button>
        </>
      }
    >
      <Card style={{ marginTop: 0 }} bodyStyle={{ padding: 0 }} bordered={false}>
        <EditableProTable<EditableRowDataType>
          editableFormRef={editableFormRef}
          actionRef={actionRef}
          rowKey="id"
          sticky
          bordered={false}
          controlled
          debounceTime={200}
          className="editable-table"
          size="small"
          cardProps={{
            style: { marginBottom: '2rem' },
            bodyStyle: { padding: 0 },
          }}
          scroll={{
            x: 1600,
          }}
          style={{
            padding: 0,
          }}
          recordCreatorProps={{
            newRecordType: 'dataSource',
            position: 'bottom',
            creatorButtonText: 'Add new row',
            record: (index: number) => {
              return { id: getNewKey(index), mapping_type: 'default' };
            },
          }}
          loading={loading}
          columns={columns}
          value={dataSource}
          onChange={setDataSource}
          locale={{ emptyText: <></> }}
          columnEmptyText={''}
          editable={{
            type: 'multiple',
            editableKeys,
            actionRender: (row, config, defaultDoms) => {
              const actionButtons: any[] = [defaultDoms.delete];
              /* if (row.mapping_type == 'list') {
                actionButtons.push(
                  <a key="setting" onClick={(e) => {}}>
                    <SettingOutlined />
                  </a>,
                );
              } */
              return actionButtons;
            },
            onValuesChange: (record, recordList) => {
              setDataSource(recordList);
            },
            onChange: setEditableRowKeys,
            deletePopconfirmMessage: 'Are you sure you want to delete?',
            onlyAddOneLineAlertMessage: 'You can only add one.',
            deleteText: <DeleteFilled style={{ marginLeft: 'auto' }} />,
          }}
        />
      </Card>
    </Modal>
  );
};

export default XlsTplDetails;
