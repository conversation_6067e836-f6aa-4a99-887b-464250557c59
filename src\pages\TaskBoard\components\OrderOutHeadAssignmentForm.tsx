import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { Button, Col, message, Row } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { assignOrderOutHead } from '@/services/app/Task/task';
import Util from '@/util';
import _ from 'lodash';
import { SelectOutlined } from '@ant-design/icons';
import type { onTaskClickType } from '..';
import { getOrderOutHeadACList } from '@/services/app/Order/order-out-head';

const handleAssignOrderOutHead = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    const res = await assignOrderOutHead(fields);
    hide();
    message.success('Update is successful');
    return res;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.Task>;

export type OrderOutHeadAssignmentFormProps = {
  task?: Partial<API.Task>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Task) => Promise<boolean | void>;
  onTaskClick?: onTaskClickType;
};

const OrderOutHeadAssignmentForm: React.FC<OrderOutHeadAssignmentFormProps> = (props) => {
  const { task } = props;
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (!props.modalVisible) return;

    if (formRef.current) {
      formRef.current.setFieldsValue({ order_out_head_id: task?.order_out_head_id });
    }
  }, [props.modalVisible, task?.order_out_head_id]);

  return (
    <ModalForm
      title={
        <>
          <SelectOutlined className="c-blue" /> Assign Task Groups into task #{task?.id}
        </>
      }
      width="600px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="vertical"
      labelAlign="left"
      formRef={formRef}
      submitter={{
        render(__) {
          return [
            <Button key="cancel" onClick={() => props.handleModalVisible(false)}>
              {' '}
              Cancel
            </Button>,
            <Button key="submit" type="primary" onClick={() => formRef.current?.submit()}>
              Save
            </Button>,
          ];
        },
      }}
      onFinish={async (value) => {
        const data = {
          ...value,
          id: task?.id,
        };
        const res = await handleAssignOrderOutHead(data);

        if (res) {
          if (props.onSubmit) props.onSubmit(data);
        }
      }}
    >
      <Row gutter={32}>
        <Col span={12}>
          <ProFormSelect
            name="order_out_head_id"
            label="Order Out Head"
            required
            mode="single"
            showSearch
            formItemProps={{ style: { width: '500px' } }}
            request={(params) => getOrderOutHeadACList(params, { created_on: 'descend' })}
          />
        </Col>
      </Row>
    </ModalForm>
  );
};

export default OrderOutHeadAssignmentForm;
