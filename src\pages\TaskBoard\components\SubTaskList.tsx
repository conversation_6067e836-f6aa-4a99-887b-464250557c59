import { deleteTask, getTaskList } from '@/services/app/Task/task';
import Util, { sn } from '@/util';
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Col, Popconfirm, Row, Space } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useMemo, useRef, useState } from 'react';
import type { onTaskClickType } from '..';
import { TaskClickAction } from '..';
import CreateForm from './CreateForm';
import TaskStatusComp from './TaskStatus';
import UpdateForm from './UpdateForm';

type SubTaskListProps = {
  initialValues?: API.Task;
  tableTitle?: any;
  refreshTick?: number; // refresh tick

  groupId?: number;
  droppableId?: string;
  index?: number;

  handleSubTasksModalVisible?: Dispatch<SetStateAction<boolean>>;
  onTaskClick?: onTaskClickType;

  // to control creation modal in parent.
  subCreateModalVisible?: boolean;
  setSubCreateModalVisible?: Dispatch<SetStateAction<boolean>>;
};

const SubTaskList: React.FC<SubTaskListProps> = (props) => {
  const {
    initialValues: task,
    refreshTick,

    groupId,
    droppableId,
    handleSubTasksModalVisible,
    onTaskClick,
  } = props;

  // const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.Task>();

  const { id: id } = task || {};

  const [subCreateModalVisible, setSubCreateModalVisible] = useState<boolean>(false);
  const [subUpdateModalVisible, setSubUpdateModalVisible] = useState<boolean>(false);

  const columns: ProColumns<API.Task>[] = useMemo(() => {
    return [
      {
        title: 'Title',
        sorter: true,
        dataIndex: ['title'],
        valueType: 'text',
        search: false,
        ellipse: true,
        render: (dom, record) => {
          return (
            <>
              <Row wrap={false} gutter={16}>
                <Col flex="auto">{record.title}</Col>
                <Col
                  flex="0 0 80px"
                  className="text-right text-sm c-grey"
                  style={{ lineHeight: '24px' }}
                >
                  <TaskStatusComp
                    status={record.status}
                    style={{ lineHeight: '16px', margin: 0 }}
                  />
                </Col>
                <Col
                  flex="0 0 80px"
                  className="text-right text-sm c-grey"
                  style={{ lineHeight: '24px' }}
                >
                  {Util.dtToDMY(record.created_on)}
                </Col>
                <Col flex="0 0 50px" className="text-right text-sm c-grey">
                  <Space size={4}>
                    <Button
                      icon={<EditOutlined />}
                      block
                      type="link"
                      size="small"
                      onClick={() => {
                        setCurrentRow(record);
                        setSubUpdateModalVisible(true);
                      }}
                    />
                    <Popconfirm
                      className="c-red cursor-pointer"
                      title={<>Are you sure you want to delete?</>}
                      okText="Yes"
                      cancelText="No"
                      overlayStyle={{ width: 300 }}
                      onConfirm={async () => {
                        deleteTask({ id: record.id })
                          .then(() => actionRef.current?.reload())
                          .catch((reason) => Util.error(reason));
                      }}
                    >
                      <Button icon={<DeleteOutlined />} block type="link" size="small" />
                    </Popconfirm>
                  </Space>
                </Col>
              </Row>
              <Row>
                <Col className="c-grey">
                  <div
                    style={{ paddingTop: 4 }}
                    dangerouslySetInnerHTML={{
                      __html: record?.desc?.replaceAll('\n', '<br />') || '',
                    }}
                  />
                </Col>
              </Row>
            </>
          );
        },
      },
    ];
  }, []);

  useEffect(() => {
    if (refreshTick && refreshTick > 0) {
      actionRef.current?.reload();
    }
  }, [refreshTick]);

  // We open/close creation modal by props
  useEffect(() => {
    setSubCreateModalVisible(!!props.subCreateModalVisible);
  }, [props.subCreateModalVisible]);

  return (
    <>
      <ProTable<API.TaskComment, API.PageParams>
        rowKey="id"
        headerTitle={props.tableTitle ?? <></>}
        actionRef={actionRef}
        revalidateOnFocus={false}
        options={{ fullScreen: true, density: false, search: false, setting: false }}
        size="small"
        locale={{ emptyText: <></> }}
        showHeader={false}
        pagination={{
          showSizeChanger: false,
          hideOnSinglePage: true,
          pageSize: 10,
        }}
        toolBarRender={() => [
          <Button
            key="new"
            type="primary"
            size="small"
            onClick={() => setSubCreateModalVisible(true)}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        search={false}
        cardProps={{
          bodyStyle: { padding: 0 },
        }}
        request={(params, sort, filter) => {
          if (!props.initialValues?.id)
            return new Promise((resolve) => resolve({ data: [], total: 0, success: true }));
          return getTaskList(
            { ...params, parent_id: id, block_id: task?.block_id },
            sort,
            filter,
          ) as any;
        }}
        columns={columns}
      />
      {subCreateModalVisible && (
        <CreateForm
          modalVisible={subCreateModalVisible}
          blockId={task?.block_id}
          parentId={task?.id}
          handleModalVisible={setSubCreateModalVisible}
          onSubmit={async (value) => {
            setSubCreateModalVisible(false);
            props.setSubCreateModalVisible?.(false);
            actionRef.current?.reload();
            handleSubTasksModalVisible?.(true);
            onTaskClick?.(
              task as any,
              droppableId,
              props.index,
              undefined,
              TaskClickAction.createSubCallback,
              { subTask: value },
            );
          }}
        />
      )}
      {subUpdateModalVisible && (
        <UpdateForm
          modalVisible={subUpdateModalVisible}
          handleModalVisible={setSubUpdateModalVisible}
          initialValues={{
            ...currentRow,
            group_id: groupId,
            block_id: sn(droppableId),
          }}
          onSubmit={async (value) => {
            if (value) {
              // console.log(value);
              actionRef.current?.reload();
            }
          }}
          onCancel={() => {
            setSubUpdateModalVisible(false);
          }}
        />
      )}
    </>
  );
};

export default SubTaskList;
