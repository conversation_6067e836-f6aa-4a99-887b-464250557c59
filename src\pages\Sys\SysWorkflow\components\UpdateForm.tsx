import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { updateSysWorkflow } from '@/services/app/Sys/sys-workflow';
import Util from '@/util';
import { SysWorkflowTypeOptionsKvCreatable } from '@/constants';

const handleUpdate = async (id?: number, fields?: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateSysWorkflow(id, fields);
    hide();
    message.success('Update is successful');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.SysWorkflow>;

export type UpdateFormProps = {
  initialValues?: Partial<API.SysWorkflow>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.SysWorkflow) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef && formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, props.initialValues]);

  return (
    <ModalForm
      title={'Update Workflow'}
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 7 }}
      wrapperCol={{ span: 16 }}
      initialValues={props.initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate(props.initialValues?.id, { ...value });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormText
        required
        rules={[
          {
            required: true,
            message: 'Name is required',
          },
          {
            max: 255,
          },
        ]}
        width="md"
        name="name"
        label="Name"
      />
      <ProFormSelect
        required
        rules={[
          {
            required: true,
            message: 'Type is required',
          },
        ]}
        width="md"
        name="type"
        label="Type"
        valueEnum={SysWorkflowTypeOptionsKvCreatable}
      />
    </ModalForm>
  );
};

export default UpdateForm;
