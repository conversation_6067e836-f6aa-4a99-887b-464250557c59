/* eslint-disable */
import { AC_PER_PAGE_PAGINATION } from '@/constants';
import Util from '@/util';
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/order/order-out-head';

/** orderOut GET /api/order/order-out-head */
export async function getOrderOutHeadList(
  params: API.PageParams,
  sort: any,
  filter: any,
): Promise<API.PaginatedResult<API.OrderOutHead>> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** orderOut GET /api/order/order-out-head/{id} */
export async function getOrderOutHead(
  id?: number,
  params?: API.PageParams & { lotus_notes_id?: string },
) {
  const url = params?.lotus_notes_id ? `${urlPrefix}/lotus` : `${urlPrefix}/${id}`;

  return request<API.ResultObject<API.OrderOutHead>>(url, {
    method: 'GET',
    params: {
      ...params,
    },
    paramsSerializer,
    withToken: true,
  })
    .then((res) => res.message)
    .catch((e) => Util.error(e));
}

/** put PUT /api/order/order-out-head */
export async function updateOrderOutHead(data: API.OrderOutHead, options?: { [key: string]: any }) {
  return request<API.OrderOutHead>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/order/order-out-head */
export async function addOrderOutHead(data: API.OrderOutHead, options?: { [key: string]: any }) {
  return request<API.OrderOutHead>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/order/order-out-head/{id} */
export async function deleteOrderOutHead(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}

export async function getOrderOutHeadACList(
  params: API.PageParams,
  sort?: any,
  filter?: any,
): Promise<any[]> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize ?? AC_PER_PAGE_PAGINATION,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) =>
    res.message.data.map((x: API.OrderOutHead) => ({
      value: x.id,
      label: `${x.name} (${Util.dtToDMYHHMM(x.created_on)})`,
    })),
  );
}
