import { EditOutlined } from '@ant-design/icons';
import { Card, Space } from 'antd';
import { useState } from 'react';
import UpdateOrderOutAddressForm from './UpdateOrderOutAddressForm';

type OrderOutAddressProps = {
  order_no?: number;
  type: 'OrderIn' | 'OrderOut';
  customer_id?: number;
  supplier_id?: number;
  card_title: string;
  card_class?: string;

  address_type: 'shipping' | 'invoice' | 'loading';
  address?: API.Address;
  same_with_invoice?: boolean;
  addressList?: API.Address[]; // selectable address List
  loadOrderOut?: (no?: number) => void;
};

const OrderOutAddress: React.FC<OrderOutAddressProps> = (props) => {
  const {
    order_no,
    type,
    customer_id,
    supplier_id,
    card_title,
    card_class,
    addressList,
    address,
    address_type,
    same_with_invoice,
    loadOrderOut,
  } = props;

  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  return (
    <>
      <Card
        style={{ height: '100%' }}
        size="small"
        title={card_title}
        className={card_class ?? ''}
        extra={
          <Space size={4}>
            <a
              onClick={() => {
                handleUpdateModalVisible(true);
              }}
              title="Edit address..."
            >
              <EditOutlined />
            </a>
            {/* {address_type == 'loading' && (
              <a
                onClick={() => {
                  setOpenAddressTplSelectionModal(true);
                }}
                title="Export address on XLS template..."
              >
                <ExportOutlined />
              </a>
            )} */}
          </Space>
        }
      >
        {same_with_invoice ? (
          <span className="c-grey italic">Same as invoice address</span>
        ) : (
          <>
            {address?.company && <div>{address.company}</div>}
            {address?.full ?? '-'}
            {address?.opening_hours && (
              <div style={{ marginTop: 4, fontSize: 9 }}>{address.opening_hours}</div>
            )}
          </>
        )}
        {order_no && (
          <UpdateOrderOutAddressForm
            title={`Update ${address_type} address`}
            modalVisible={updateModalVisible}
            handleModalVisible={handleUpdateModalVisible}
            initialValues={address}
            order_no={order_no}
            customer_id={customer_id}
            supplier_id={supplier_id}
            type={type}
            address_type={address_type}
            addressList={addressList}
            onSubmit={async () => {
              loadOrderOut?.(order_no);
            }}
            onCancel={() => {
              handleUpdateModalVisible(false);
            }}
          />
        )}
        {/* {order_no && supplier_id && address?.id && (
          <AddressTplSelectionModal
            supplierId={supplier_id}
            addressId={address.id}
            modalVisible={openAddressTplSelectionModal}
            handleModalVisible={setOpenAddressTplSelectionModal}
            onSubmit={async (value: any) => {
              console.log('On Submit...', value);
              const supplierIdAndAddressIdStr = value.supplierIdAndAddressId;
              const arr = supplierIdAndAddressIdStr.split('_') ?? [];
              if (arr.length != 2) return;
              const hide = message.loading('Exporting XLS file...', 0);
              exportAddressByTpl(value.tpl_id, {
                supplierId: arr[0],
                orderNo: order_no,
                addressId: arr[1],
              })
                .then((res) => {
                  console.log(res);
                  message.success('Exported successfully.');
                  window.location.href = `${API_URL}${res.file_url}`;
                })
                .catch((err) => Util.error(err))
                .finally(() => hide());
            }}
          />
        )} */}
      </Card>
    </>
  );
};
export default OrderOutAddress;
