import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormDigit } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { addOrderOut } from '@/services/app/Order/order-out';
import { message } from 'antd';
import Util from '@/util';
import SProFormTextAreaExpanded from './SProFormTextAreaExpanded';

const handleAdd = async (fields: API.OrderOut) => {
  const hide = message.loading('Adding...');
  const data = { ...fields };
  try {
    await addOrderOut(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

// eslint-disable-next-line @typescript-eslint/ban-types
export type FormValueType = {};

export type CreateFormProps = {
  values?: Partial<API.OrderOut>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.OrderOut) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={'New Order Out'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 7 }}
      wrapperCol={{ span: 16 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.OrderOut);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormDigit
        required
        rules={[
          {
            required: true,
            message: 'Order No is required',
          },
        ]}
        width="sm"
        name="order_no"
        label="Order No"
      />
      <ProFormText
        required
        rules={[
          {
            required: true,
            message: 'Customer is required',
          },
        ]}
        width="lg"
        name="customer"
        label="Customer"
      />
      <ProFormText width="lg" name="lotus_notes_id" label="Lotus Notes ID" />
      <SProFormTextAreaExpanded width="lg" name="desc" label="Description" formRef={formRef} />
    </ModalForm>
  );
};

export default CreateForm;
