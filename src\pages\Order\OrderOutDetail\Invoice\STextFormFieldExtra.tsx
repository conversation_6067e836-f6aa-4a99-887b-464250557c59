import { getSysTextModuleACList } from '@/services/app/Sys/text-module';
import { sn } from '@/util';
import { InfoCircleOutlined, LoadingOutlined } from '@ant-design/icons';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import type { ProFormFieldItemProps } from '@ant-design/pro-form/lib/interface';
import type { InputProps, InputRef } from 'antd';
import { Dropdown, Menu } from 'antd';
import type { ItemType } from 'rc-menu/lib/interface';
import { useState } from 'react';
import _ from 'lodash';

type STextFormFieldExtraProps = {
  form?: ProFormInstance<any>;
  defaultIsEditing?: boolean;
  record?: API.OrderOutItem;
  namePath?: any;
  changeCallback: (value: string) => void;
} & ProFormFieldItemProps<InputProps, InputRef>;

const STextFormFieldExtra: React.FC<STextFormFieldExtraProps> = ({
  form,
  defaultIsEditing,
  record,
  children,
  namePath,
  changeCallback,
  ...restProps
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [texts, setTexts] = useState<any[]>([]);
  const [open, setOpen] = useState<boolean>(false);
  const [selectionStart, setSelectionStart] = useState<number | null>();
  const [selectionEnd, setSelectionEnd] = useState<number | null>();

  const recordId = record?.id ?? '';

  /**
   * Find the available number from the last position.
   * e.g. YYY#123 xxx
   *
   * @param position
   * @returns
   */
  const getNumber = (position?: number | null) => {
    const curDesc = form?.getFieldValue(namePath ?? [recordId, 'desc']);
    if (curDesc && position) {
      // find # character
      let start = position - 1;
      let foundedCorrectNumber = false;
      while (start >= 0) {
        const ch = curDesc.substring(start, start + 1);
        if (ch == '#') {
          foundedCorrectNumber = true;
          break;
        }
        if (isNaN(Number(ch))) {
          break;
        }
        start--;
      }

      if (foundedCorrectNumber) {
        const numStr = curDesc.substring(start + 1, position);
        const num = sn(numStr);
        if (numStr && num) {
          return [start, num];
        }
      }
    }
    return null;
  };

  // Handle TextModule search by a pattern "#123 "
  const handleDescriptionHandler = async (e: React.KeyboardEvent) => {
    if (!record) return;
    const ele: HTMLInputElement = e.target as HTMLInputElement;
    /* console.log(e);
    console.log('Key: ', recordId, record.desc, ', Keyboard:', e.key);
    console.log(ele.selectionStart, ele.selectionEnd, ele.getClientRects()); */

    setSelectionEnd(ele.selectionEnd);
    if (e.key == '#' || open) {
      if (e.key == '#') {
        setSelectionStart(ele.selectionEnd);
        setOpen(true);
        setLoading(true);
        getSysTextModuleACList({ number: 0 })
          .then((res) => {
            setTexts(res);
          })
          .finally(() => setLoading(false));
      } else {
        const data = getNumber(ele.selectionEnd);
        console.log(data, ele.selectionEnd);
        const curDesc = form?.getFieldValue(namePath ?? [recordId, 'desc']);

        if (data) {
          console.log('loading again...', curDesc);
          setLoading(true);
          getSysTextModuleACList({ number: data[1] })
            .then((res) => {
              setTexts(res);
            })
            .finally(() => setLoading(false));
        }
      }
    }
    if (e.key == ' ' && ele.selectionEnd) {
      const data = getNumber(ele.selectionEnd - 1);
      const curDesc = form?.getFieldValue(namePath ?? [recordId, 'desc']);
      if (data !== null) {
        setLoading(true);
        getSysTextModuleACList({ numberExact: data[1] })
          .then((res) => {
            // We found a Text by Number, so we need to put it in the text field.
            if (res?.[0]) {
              const newValue =
                curDesc.substring(0, data[0]) + res[0].text + curDesc.substring(ele.selectionEnd);
              form?.setFieldValue(namePath ?? [recordId, 'desc'], newValue);
              const newPos = ele.selectionEnd + res?.[0].text.length - 1;
              ele.setSelectionRange(newPos, newPos);

              // call cb func
              changeCallback(newValue);

              setOpen(false);
              setTexts([]);
              setSelectionStart(null);
              setSelectionEnd(null);
            }
          })
          .finally(() => {
            setLoading(false);
          });
      }
    }
  };

  return (
    <>
      <Dropdown
        open={open}
        overlay={
          <Menu
            onClick={(e) => {
              if (sn(e.key) < 1) return;
              const replacedText = _.get(_.find(texts, { value: sn(e.key) }), 'text');
              const curDesc = form?.getFieldValue(namePath ?? [recordId, 'desc']);
              const newValue =
                curDesc.substring(0, sn(selectionStart) - 1) +
                replacedText +
                curDesc.substring(selectionEnd);
              form?.setFieldValue(namePath ?? [recordId, 'desc'], newValue);
              /* const fieldInstance = form?.getFieldInstance(namePath ?? [recordId, 'desc']);
              console.log(fieldInstance); */
              /* const newPos = ele.selectionEnd + res?.[0].text.length - 1;
              ele.setSelectionRange(newPos, newPos); */

              // call cb func
              changeCallback(newValue);
              setOpen(false);
              setTexts([]);
              setSelectionStart(null);
              setSelectionEnd(null);
            }}
            items={
              loading
                ? [
                    {
                      key: -1,
                      label: (
                        <>
                          <LoadingOutlined /> Loading...
                        </>
                      ),
                    },
                  ]
                : texts.map(
                    (x) =>
                      ({
                        key: x.value,
                        label: x.label,
                        disabled: false,
                      } as ItemType),
                  )
            }
          />
        }
      >
        <ProFormText
          {...restProps}
          formItemProps={{ ...restProps.formItemProps }}
          fieldProps={{
            ...restProps.fieldProps,
            onBlur: (e) => {
              setTimeout(() => setOpen(false), 300);
            },
            onKeyUp: async (e) => {
              if (e.key == 'Escape') {
                setOpen(false);
                return;
              }
              await handleDescriptionHandler(e);
            },
            suffix: loading ? <LoadingOutlined /> : undefined,
            addonAfter: (
              <InfoCircleOutlined title='Please insert a pre-defined text by starting to type "#" and numbers. Insert by pressing "space key" or by selection.' />
            ),
          }}
        />
      </Dropdown>
    </>
  );
};
export default STextFormFieldExtra;
