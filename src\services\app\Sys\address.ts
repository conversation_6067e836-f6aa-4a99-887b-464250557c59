/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/address';

/** get address GET /api/address */
export async function getAddressList(
  params: API.PageParams &
    Partial<API.Address> & {
      customer_id?: number;
      supplier_id?: number;
      order_no?: number;
      supplier_ids?: number[];
      with?: string;
      isDefaultLoading?: boolean;
    },
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.Address>> {
  return request<API.Result<API.Address>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** put PUT /api/address */
export async function updateAddress(data: Partial<API.Address>, options?: { [key: string]: any }) {
  return request<API.Address>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** put PUT /api/address/order-address/{order_no} */
export async function updateOrderAddress(
  order_no?: number,
  data?: Partial<API.Address> & { by_address_id?: number; type?: string },
  options?: { [key: string]: any },
) {
  return request<API.Address>(`${urlPrefix}/order-address/${order_no}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/address */
export async function addAddress(data: API.Address, options?: { [key: string]: any }) {
  return request<API.Address>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/address/{id} */
export async function deleteAddress(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** get address GET /api/address/ac-list */
export async function getAddressACList(
  params: API.PageParams &
    Partial<API.Address> & {
      customer_id?: number;
      supplier_id?: number;
      order_no?: number;
      supplier_ids?: number[];
      with?: string;
    },
): Promise<API.Address[]> {
  return request<API.BaseResult>(`${urlPrefix}/ac-list`, {
    method: 'GET',
    params: {
      ...params,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}
