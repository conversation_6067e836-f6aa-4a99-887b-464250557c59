import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormDependency, ProFormDigit } from '@ant-design/pro-form';
import { ProFormItem, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { updateSysXlsColNameMap } from '@/services/app/Sys/sys-xls-col-name-map';
import Util from '@/util';
import ColumnNamesFormList from './ColumnNamesFormList';
import { DbFieldType } from '@/constants';
import type { MappableTableType } from '..';
import { getMappableDBTablesOptions } from '..';

const handleUpdate = async (id?: number, fields?: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateSysXlsColNameMap(id, fields);
    hide();
    message.success('Update is successful');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.SysXlsColNameMap>;

export type UpdateFormProps = {
  initialValues?: Partial<API.SysXlsColNameMap>;
  modalVisible: boolean;
  tables?: Record<string, MappableTableType>;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.SysXlsColNameMap) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      newValues.xls_column_list = newValues.xls_column?.map((x, index) => ({
        id: index,
        value: x,
      }));
      formRef.current.setFieldsValue(newValues);
    }
  }, [props.initialValues]);

  return (
    <ModalForm
      title={'Update XLS Column Map'}
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 7 }}
      wrapperCol={{ span: 16 }}
      initialValues={props.initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate(props.initialValues?.id, {
          ...value,
          xls_column: value.xls_column_list?.map((x: any) => x.value),
        });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormSelect
        name="table_name"
        label="Table Name"
        placeholder="Please select table name"
        required
        rules={[
          {
            required: true,
            message: 'Table Name is required.',
          },
        ]}
        options={getMappableDBTablesOptions(props.tables)}
        width="md"
        readonly
      />
      <ProFormText
        name="db_field"
        label="DB Field"
        required
        rules={[
          {
            required: true,
            message: 'DB Field is required.',
          },
        ]}
      />
      <ProFormSelect
        name="db_field_type"
        label="DB Field Type"
        options={DbFieldType}
        required
        rules={[
          {
            required: true,
            message: 'DB Field Type is required.',
          },
        ]}
      />
      <ProFormDependency name={['db_field_type', 'table_name']}>
        {(depValues) => {
          const isLengthAvailable = depValues.db_field_type == 'varchar';
          return !isLengthAvailable || !props.tables?.[depValues.table_name]?.columnDeletable ? (
            <></>
          ) : (
            <>
              <ProFormDigit
                width="md"
                name="db_field_length"
                tooltip="Legnth"
                label="Length"
                required
                initialValue={255}
                rules={[
                  {
                    required: true,
                    message: 'Length is required.',
                  },
                ]}
              />
            </>
          );
        }}
      </ProFormDependency>
      <ProFormText name="db_field_comment" label="DB Field Comment" />
      <ProFormItem label="XLS Names" style={{ marginBottom: 0 }}>
        <ColumnNamesFormList />
      </ProFormItem>
    </ModalForm>
  );
};

export default UpdateForm;
