import SProFormDateRangeFull, { DRSelection } from '@/components/SProFormDateRangeFull';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import { getFinDetailStatList } from '@/services/app/Fin/fin-detail-stat';
import Util from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormText } from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import { ProTable, type ActionType, type ProColumns } from '@ant-design/pro-table';
import { Card } from 'antd';
import { useRef } from 'react';

type SearchFormType = Partial<API.FinDetail>;
type TableRowType = Partial<API.FinDetail>;

const FinDetailStat: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance<SearchFormType>>();

  const columns: ProColumns<TableRowType>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: 'Order No',
      dataIndex: 'order_no',
      sorter: true,
    },
  ];

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_fin_detail_stat', {})}
          submitter={{
            submitButtonProps: {
              htmlType: 'submit',
            },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => actionRef.current?.reload(),
            render: (form, dom) => {
              return [...dom];
            },
          }}
        >
          <SProFormDateRangeFull
            label="Date"
            startDateName="date"
            endDateName="end_date"
            formRef={searchFormRef}
            selectorOptions={[
              DRSelection.DR_THIS_MONTH,
              DRSelection.DR_LAST_MONTH,
              DRSelection.DR_FYSCAL_THIS_YEAR,
              DRSelection.DR_FYSCAL_LAST_YEAR,
              DRSelection.DR_THIS_YEAR,
              DRSelection.DR_LAST_YEAR,
              DRSelection.DR_SINCE_BEGINNING,
              DRSelection.DR_CUSTOM,
            ]}
          />
        </ProForm>
      </Card>
      <ProTable<TableRowType, API.PageParams>
        headerTitle={'Invoices Stat'}
        actionRef={actionRef}
        rowKey="uid"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        pagination={{
          showSizeChanger: true,
          pageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        request={getFinDetailStatList}
        columns={columns}
      />
    </PageContainer>
  );
};
export default FinDetailStat;
