/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/task/task-comment';

/** task GET /api/task/task-comment */
export async function getTaskCommentList(
  params: Partial<API.PageParams & API.TaskComment>,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.TaskComment>> {
  return request<API.Result<API.TaskComment>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** put PUT /api/task/task-comment */
export async function updateTaskComment(
  data: Partial<API.TaskComment>,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject<API.TaskComment>>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** post POST /api/task/task-comment */
export async function addTaskComment(
  data: Partial<API.TaskComment>,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject<API.TaskComment>>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** delete DELETE /api/task/task-comment/{id} */
export async function deleteTaskComment(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}
