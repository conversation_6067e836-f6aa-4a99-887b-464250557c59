import { <PERSON><PERSON>, <PERSON>, Modal, Space } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React, { useState, useRef, useEffect } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { nf2 } from '@/util';

import { FileExcelOutlined } from '@ant-design/icons';

import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import ImportModalForm from './components/ImportModalForm';
import { getFinSinfoList } from '@/services/app/Fin/fin-sinfo';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';

type RowType = API.FinSinfo;
type SfFormType = API.FinSinfo;

type FinSinfoListModalProps = {
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
};

const FinSinfoListModal: React.FC<FinSinfoListModalProps> = ({
  modalVisible,
  handleModalVisible,
}) => {
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();

  // XLS import
  const [openImportModal, setOpenImportModal] = useState<boolean>(false);

  const columns: ProColumns<RowType>[] = [
    {
      title: 'Artikelnummer',
      dataIndex: 'Artikelnummer',
      sorter: true,
      search: false,
      align: 'right',
      width: 80,
    },
    {
      title: 'UVP',
      dataIndex: 'uvp',
      search: false,
      align: 'right',
      width: 80,
      render: (dom, record) => nf2(record.uvp),
    },
    {
      title: 'Artikel-Bez',
      dataIndex: 'item_designation',
      search: false,
      width: 120,
    },
    {
      title: 'Groesse',
      dataIndex: 'size',
      search: false,
      width: 100,
    },
    {
      title: 'Farbe',
      dataIndex: 'color',
      search: false,
      width: 100,
    },
    {
      title: 'Gewicht',
      dataIndex: 'weight',
      search: false,
      align: 'right',
      width: 80,
      render: (dom, record) => nf2(record.uvp),
    },
    {
      title: 'Zolltarifnr',
      dataIndex: 'customs_tariff_no',
      sorter: false,
      search: false,
    },
  ];

  useEffect(() => {
    const lastSf = Util.getSfValues('sf_fin_sinfo_list_modal', {});
    searchFormRef.current?.setFieldsValue(lastSf);
    Util.setSfValues(lastSf);
  }, []);

  return (
    <Modal
      title="Financial Sinfos"
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      width={750}
      maskClosable={false}
      footer={false}
    >
      <Card style={{ marginBottom: 16 }} bordered={false} bodyStyle={{ padding: 0 }}>
        <ProForm<SfFormType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            submitButtonProps: {
              htmlType: 'submit',
            },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => actionRef.current?.reload(),
            render: (form, dom) => {
              return [...dom];
            },
          }}
        >
          <ProFormText
            name={'Artikelnummer'}
            label="Artikelnummer"
            width={'xs'}
            placeholder={'Artikelnummer'}
          />
          <ProFormText
            name={'customs_tariff_no'}
            label="Zolltarifnr"
            width={'xs'}
            placeholder={'Zolltarifnr'}
          />
        </ProForm>
      </Card>

      <ProTable<RowType, API.PageParams>
        headerTitle={'Sinfos list'}
        actionRef={actionRef}
        rowKey="Artikelnummer"
        revalidateOnFocus={false}
        options={{ fullScreen: false, setting: false, density: false }}
        search={false}
        size="small"
        pagination={{
          showSizeChanger: true,
          pageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        cardProps={{ bodyStyle: { padding: 0 } }}
        toolBarRender={() => [
          <Space key="new">
            <Button
              type="primary"
              size="small"
              icon={<FileExcelOutlined />}
              onClick={() => setOpenImportModal(true)}
              title="Import by XLS or CSV"
            >
              Import
            </Button>
          </Space>,
        ]}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_fin_sinfo_list_modal', searchFormValues);

          return getFinSinfoList({ ...params, ...searchFormValues }, { ...sort }, filter).then(
            (res) => {
              return res;
            },
          );
        }}
        onRequestError={(err) => Util.error(err)}
        columns={columns}
        tableAlertRender={false}
        columnEmptyText=""
      />

      <ImportModalForm
        modalVisible={openImportModal}
        handleModalVisible={setOpenImportModal}
        onSubmit={async (value) => {
          actionRef.current?.reload();
          setOpenImportModal(false);
        }}
      />
    </Modal>
  );
};

export default FinSinfoListModal;
