import { DeleteOutlined } from '@ant-design/icons';
import { Button, Popconfirm } from 'antd';
import type { ButtonType } from 'antd/es/button';

const BatchDeleteAction: React.FC<{
  title?: string;
  btnTitle?: string;
  btnType?: ButtonType;
  onConfirm?: any;
}> = ({ title, btnTitle, btnType, onConfirm, ...rest }) => {
  return (
    <Popconfirm
      title={<>Are you sure you want to delete selected {title ?? 'row'}s?</>}
      okText="Yes"
      cancelText="No"
      overlayStyle={{ maxWidth: 300 }}
      onConfirm={onConfirm}
    >
      <Button type={btnType ?? 'default'} danger icon={<DeleteOutlined />}>
        {btnTitle ?? 'Bacth deletion'}
      </Button>
    </Popconfirm>
  );
};

export default BatchDeleteAction;
