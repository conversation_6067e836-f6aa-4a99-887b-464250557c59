import { PlusOutlined } from '@ant-design/icons';
import { But<PERSON>, message, Drawer, Tag, Space, Typography } from 'antd';
import React, { useState, useRef } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import UpdateForm from './components/UpdateForm';

import Util from '@/util';
import CreateForm from './components/CreateForm';
import {
  getTaskGroupList,
  deleteTaskGroup,
  createReservedTaskGroup,
} from '@/services/app/Task/task-group';
import {
  DEFAULT_PER_PAGE_PAGINATION,
  TaskGroupLayoutKV,
  UpdateTaskLayout,
  UpdateTaskLayoutKV,
} from '@/constants';
import UpdatePartialForm from './components/UpdatePartialForm';

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.TaskGroup[]) => {
  const hide = message.loading('Deleting...', 0);
  if (!selectedRows) return true;

  try {
    await deleteTaskGroup({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

const TaskGroupList: React.FC = () => {
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);

  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [sortModalVisible, handleSortModalVisible] = useState<boolean>(false);
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.TaskGroup>();
  const [selectedRowsState, setSelectedRows] = useState<API.TaskGroup[]>([]);

  const columns: ProColumns<API.TaskGroup>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: 'Name',
      dataIndex: 'name',
      sorter: true,
      width: 200,
      render: (dom, record) => {
        return (
          <a
            onClick={() => {
              handleUpdateModalVisible(true);
              setCurrentRow(record);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: 'Code',
      dataIndex: 'code',
      sorter: true,
      hideInForm: true,
      width: 100,
      renderText: (val: string) => `${val}`,
    },
    {
      title: 'Sort',
      dataIndex: 'sort',
      sorter: true,
      hideInForm: true,
      align: 'center',
      width: 80,
      showSorterTooltip: false,
      tooltip: 'Click on the cell to edit.',
      className: 'cursor-pointer',
      shouldCellUpdate: (prev, next) => {
        return prev.sort != next.sort;
      },
      onCell: (record, index?: number) => {
        return {
          onClick: (e) => {
            handleSortModalVisible(true);
            setCurrentRow(record);
          },
        };
      },
    },
    {
      title: 'Layout',
      dataIndex: ['settings', 'layout'],
      sorter: true,
      hideInForm: true,
      width: 100,
      valueEnum: TaskGroupLayoutKV,
    },
    {
      title: 'Task Layout',
      dataIndex: ['settings', 'updateTaskLayout'],
      sorter: true,
      hideInForm: true,
      width: 100,
      ellipsis: true,
      valueEnum: UpdateTaskLayoutKV,
      render: (dom, record) => record.settings?.updateTaskLayout ?? UpdateTaskLayout.LAYOUT_A,
    },
    {
      title: 'Blocks',
      dataIndex: 'task_blocks',
      sorter: false,
      hideInForm: true,
      hideInSearch: true,
      render: (dom, record) => {
        return record.task_blocks?.map((block) => (
          <Space key={block.id} style={{ marginBottom: 4 }}>
            <Tag style={{ width: 120 }}>
              <Typography.Text ellipsis>{`Pos: ${block.position}, ${block.name}`}</Typography.Text>
            </Tag>
          </Space>
        ));
      },
    },
    {
      title: 'Block IDs',
      dataIndex: 'task_blocks_ids',
      sorter: false,
      hideInForm: true,
      hideInSearch: true,
      render: (dom, record) => {
        return record.task_blocks?.map((block) => (
          <Space key={block.id} style={{ marginBottom: 4 }}>
            <Tag>
              <Typography.Text ellipsis>{`${block.id}`}</Typography.Text>
            </Tag>
          </Space>
        ));
      },
    },
    {
      title: 'Users',
      dataIndex: 'users',
      sorter: false,
      hideInForm: true,
      hideInSearch: true,
      render: (dom, record) => {
        return record.users?.map((user) => (
          <Space key={user.user_id} style={{ marginBottom: 4 }}>
            <Tag style={{ width: 120 }}>
              <Typography.Text ellipsis>{`${user.username}-${Util.sInitials(
                user.name,
              )}`}</Typography.Text>
            </Tag>
          </Space>
        ));
      },
    },
    {
      title: 'Use initials?',
      dataIndex: ['settings', 'useInitials'],
      search: false,
      width: 80,
      valueType: 'radio',
      render: (dom, record) => (record.settings?.useInitials ? 'Yes' : ''),
    },
    /* {
      title: 'Created on',
      sorter: true,
      dataIndex: 'created_on',
      valueType: 'dateTime',
      search: false,
      width: 120,
      className: 'c-grey text-sm',
      render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
    }, */
    {
      title: 'Updated on',
      sorter: true,
      dataIndex: 'updated_on',
      valueType: 'dateTime',
      search: false,
      width: 100,
      className: 'c-grey text-sm',
      render: (dom, record) => Util.dtToDMYHHMM(record.updated_on),
    },
    {
      title: 'ID',
      dataIndex: 'id',
      colSize: 1,
      search: false,
      width: 70,
      className: 'p-0',
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 50,
      className: 'p-0',
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          Edit
        </a>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.TaskGroup, API.PageParams>
        headerTitle={'Task Group list'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={{
          labelWidth: 'auto',
          searchText: 'Search',
          span: 6,
          filterType: 'query',
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="new"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
          <Button
            type="default"
            key="new-reserved"
            onClick={() => {
              const hide = message.loading('Creating reserved Group: Order In and Order Out...', 0);
              createReservedTaskGroup()
                .then((res) => {
                  if (res) {
                    message.success('Created successfully.');
                    actionRef.current?.reload();
                  }
                })
                .finally(() => hide());
            }}
          >
            <PlusOutlined /> Create OrderIn/Order Out
          </Button>,
        ]}
        pagination={{
          showSizeChanger: true,
          pageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        request={(params, sort, filter) =>
          getTaskGroupList({ ...params, with: 'taskBlocks,users' }, sort, filter)
        }
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        columnEmptyText=""
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              chosen{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              Task Group &nbsp;&nbsp;
            </div>
          }
        >
          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            Bacth deletion
          </Button>
          {/* <Button type="primary">batch approval</Button> */}
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <UpdatePartialForm
        modalVisible={sortModalVisible}
        handleModalVisible={handleSortModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleSortModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.name && (
          <ProDescriptions<API.TaskGroup>
            column={2}
            title={currentRow?.name}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.name,
            }}
            columns={columns as ProDescriptionsItemProps<API.TaskGroup>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default TaskGroupList;
