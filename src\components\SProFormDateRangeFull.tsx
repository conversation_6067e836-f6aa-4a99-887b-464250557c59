import type { ProFormInstance, ProFormItemProps } from '@ant-design/pro-form';
import { ProFormDependency } from '@ant-design/pro-form';
import { ProFormItem } from '@ant-design/pro-form';
import type { ProFormFieldItemProps } from '@ant-design/pro-form/lib/interface';
import moment from 'moment';
import type { ReactNode } from 'react-markdown/lib/ast-to-react';
import SDatePicker from './SDatePicker';
import SProFormSelect from './SProFormSelect';
import { useModel } from 'umi';
import { useEffect } from 'react';

export enum DRSelection {
  DR_TODAY = 'DR_T',
  DR_YESTERDAY = 'DR_YESTERDAY',
  DR_2_DAYS_AGO = 'DR_2_DAYS_AGO',
  DR_THIS_WEEK = 'DR_TW',
  DR_LAST_WEEK = 'DR_LW',
  DR_LAST_30_DAYS = 'DR_LAST_30_DAYS',
  DR_THIS_MONTH = 'DR_TM',
  DR_LAST_MONTH = 'DR_LM',
  DR_THIS_YEAR = 'DR_TY',
  DR_LAST_YEAR = 'DR_LY',
  DR_FYSCAL_THIS_YEAR = 'DR_FY_THIS',
  DR_FYSCAL_LAST_YEAR = 'DR_FY_LAST',
  DR_SINCE_BEGINNING = 'DR_SB',
  DR_CUSTOM = 'DR_CUSTOM',
}

export const DRSelectionKv: Record<string, string> = {
  [DRSelection.DR_TODAY]: 'Today',
  [DRSelection.DR_YESTERDAY]: 'Yesterday',
  [DRSelection.DR_2_DAYS_AGO]: '2 Days Ago',
  [DRSelection.DR_THIS_WEEK]: 'This Week',
  [DRSelection.DR_LAST_WEEK]: 'Last Week',
  [DRSelection.DR_LAST_30_DAYS]: 'Last 30 Days',
  [DRSelection.DR_THIS_MONTH]: 'Cur. Month',
  [DRSelection.DR_LAST_MONTH]: 'Last Month',
  [DRSelection.DR_FYSCAL_THIS_YEAR]: 'Cur. Finance Year',
  [DRSelection.DR_FYSCAL_LAST_YEAR]: 'Last Finance Year',
  [DRSelection.DR_THIS_YEAR]: 'Cur. Calendar Year',
  [DRSelection.DR_LAST_YEAR]: 'Last Calendar Year',
  [DRSelection.DR_SINCE_BEGINNING]: 'Since Beginning',
  [DRSelection.DR_CUSTOM]: 'Custom',
};

export const DRSelectionOptions = [
  DRSelection.DR_TODAY,
  DRSelection.DR_YESTERDAY,
  DRSelection.DR_2_DAYS_AGO,
  DRSelection.DR_THIS_WEEK,
  DRSelection.DR_LAST_WEEK,
  DRSelection.DR_LAST_30_DAYS,
  DRSelection.DR_THIS_MONTH,
  DRSelection.DR_LAST_MONTH,
  DRSelection.DR_THIS_YEAR,
  DRSelection.DR_LAST_YEAR,
  DRSelection.DR_FYSCAL_THIS_YEAR,
  DRSelection.DR_FYSCAL_LAST_YEAR,
  DRSelection.DR_SINCE_BEGINNING,
  DRSelection.DR_CUSTOM,
].map((x) => ({ value: x, label: DRSelectionKv[x] }));

type SProFormDateRangeFullPropsType = {
  label?: string | ReactNode;
  startDateName?: string;
  endDateName?: string;
  formRef?: React.MutableRefObject<ProFormInstance<any> | undefined>;

  fieldItemProps1?: ProFormFieldItemProps;
  fieldItemProps2?: ProFormFieldItemProps;

  // Range selection list
  hideRangeList?: boolean;
  selectionFieldName?: string;
  fieldItemPropsSelection?: ProFormFieldItemProps;

  initialDrSelection?: DRSelection;
  selectorOptions?: DRSelection[];
} & ProFormItemProps;

const SProFormDateRangeFull: React.FC<SProFormDateRangeFullPropsType> = ({
  label,
  startDateName,
  endDateName,

  formRef,
  fieldItemProps1,
  fieldItemProps2,

  // Range selection list
  hideRangeList,
  fieldItemPropsSelection,
  selectionFieldName: selectionFieldNameProp,
  initialDrSelection,
  selectorOptions,
  ...rest
}) => {
  const {
    appSettings: { drSelection },
  } = useModel('app-settings');

  const startDateFieldName = startDateName ?? 'start_date';
  const endDateFieldName = endDateName ?? 'end_date';
  const selectionFieldName = selectionFieldNameProp ?? 'dr_selection';

  useEffect(() => {
    const range = drSelection[initialDrSelection as any];
    formRef?.current?.setFieldValue(selectionFieldName, initialDrSelection);

    if (range && initialDrSelection != DRSelection.DR_CUSTOM) {
      formRef?.current?.setFieldValue(startDateFieldName, range[0] ? range[0] : null);
      formRef?.current?.setFieldValue(endDateFieldName, range[1] ? range[1] : null);
    }
  }, [
    drSelection,
    endDateFieldName,
    formRef,
    initialDrSelection,
    selectionFieldName,
    startDateFieldName,
  ]);

  return (
    <ProFormItem label={label} {...rest}>
      <div style={{ display: 'flex' }}>
        {!hideRangeList && (
          <SProFormSelect
            {...fieldItemPropsSelection}
            name={selectionFieldName}
            width={170}
            options={
              selectorOptions
                ? selectorOptions.map((x) => ({ value: x, label: DRSelectionKv[`${x}`] }))
                : DRSelectionOptions
            }
            showSearch
            fieldProps={{
              ...fieldItemPropsSelection?.fieldProps,
              onChange: (value, option) => {
                const range = drSelection[value];
                console.log('DR: ', range, value, option);
                if (range && value != DRSelection.DR_CUSTOM) {
                  formRef?.current?.setFieldValue(startDateFieldName, range[0] ? range[0] : null);
                  formRef?.current?.setFieldValue(endDateFieldName, range[1] ? range[1] : null);
                }
              },
            }}
            disabled={rest.disabled}
          />
        )}
        <SDatePicker
          {...fieldItemProps1}
          label={undefined}
          width={110}
          name={startDateFieldName}
          placeholder="Start date"
          fieldProps={{
            ...fieldItemProps1?.fieldProps,
            onChange: (e) => {
              formRef?.current?.setFieldValue(selectionFieldName, DRSelection.DR_CUSTOM);
            },
          }}
          disabled={rest.disabled}
        />
        <ProFormDependency name={[`${startDateFieldName}`]}>
          {(depValues) => {
            const startDate = depValues[startDateFieldName];
            return (
              <SDatePicker
                {...fieldItemProps2}
                width={110}
                name={endDateFieldName}
                placeholder="End date"
                addonBefore="~"
                fieldProps={{
                  ...fieldItemProps2?.fieldProps,
                  // style: { marginLeft: 0 },
                  disabledDate: (current) => {
                    return current.isBefore(moment(startDate));
                  },
                  onChange: (e) => {
                    formRef?.current?.setFieldValue(selectionFieldName, DRSelection.DR_CUSTOM);
                  },
                }}
                disabled={rest.disabled}
              />
            );
          }}
        </ProFormDependency>
      </div>
    </ProFormItem>
  );
};

export default SProFormDateRangeFull;
