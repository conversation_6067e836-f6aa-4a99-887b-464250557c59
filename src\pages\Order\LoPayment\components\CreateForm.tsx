import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { createLoPaymentBulk } from '@/services/app/LexOffice/lo-payment';
import { message } from 'antd';
import Util from '@/util';

export type FormValueType = {
  data?: string;
};

const handleAdd = async (dataParam: FormValueType) => {
  const hide = message.loading('Importing...', 0);
  const data = { ...dataParam };
  try {
    await createLoPaymentBulk(data);
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type CreateFormProps = {
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: FormValueType) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm<FormValueType>
      title={'Import payments data'}
      width="700px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="vertical"
      labelAlign="left"
      formRef={formRef}
      onFinish={async (formValue) => {
        const success = await handleAdd(formValue);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(formValue);
        }
      }}
    >
      <ProFormTextArea
        rules={[
          {
            required: true,
            message: 'Data is required',
          },
        ]}
        fieldProps={{ rows: 10 }}
        // width="lg"
        name="data"
        label="Data"
      />
    </ModalForm>
  );
};

export default CreateForm;
