import { useEffect, useMemo, useState } from 'react';
import type { FormValueType } from '../MiscFileBrowser';
import { WF_ID_ORDER_OUT_INCOMING_INVOICE } from '@/constants';
import WorkflowOrderOutIncomingInvoice from './workflows/WorkflowOrderOutIncomingInvoice';
import { getSysWorkflowStepList } from '@/services/app/Sys/sys-workflow-step';
import Util from '@/util';
import { Spin } from 'antd';

type WorkflowScanFileType = {
  formValues?: FormValueType;
  refreshTick?: number;
};

const WorkflowScanFile: React.FC<WorkflowScanFileType> = (props) => {
  const { refreshTick, formValues } = props || {};

  const { cat1, cat2, order_out_order_no, order_in_order_no } = formValues || {};

  console.log('render in workflow section:', cat1, cat2, order_out_order_no, order_in_order_no);

  /* useEffect(() => {
    //
  }, [cat1, order_out_order_no, order_in_order_no, cat2]); */

  const [loading, setLoading] = useState(false);

  // Keep all workflows related.
  const [workflowsKv, setWorkflowsKv] = useState<Record<number, API.SysWorkflow>>({});

  // load all workflows related
  useEffect(() => {
    if (!cat1) return;
    let order_no;

    if (cat1 == 'OrderOut') order_no = order_out_order_no;
    else if (cat1 == 'OrderIn') order_no = order_in_order_no;

    setLoading(true);
    getSysWorkflowStepList({
      pageSize: 500,
      with: 'orderOutWorkflowSteps,latestOrderOutWorkflowStepLog,workflow',
      order_no,
      workflow_ids: [WF_ID_ORDER_OUT_INCOMING_INVOICE],
    })
      .then((res) => {
        const tmp: Record<number, API.SysWorkflow> = {};
        res.data?.forEach((step) => {
          if (!step.workflow_id) return;

          if (!(step.workflow_id in tmp)) {
            tmp[step.workflow_id] = { ...step.workflow, id: step.workflow_id, steps: [] };
          }
          tmp[step.workflow_id]?.steps?.push(step);
        });
        setWorkflowsKv(tmp);
      })
      .catch(Util.error)
      .finally(() => setLoading(false));
  }, [cat1, order_in_order_no, order_out_order_no]);

  const body = useMemo(() => {
    let mainEle = null;
    if (cat1 == 'OrderOut') {
      mainEle = [WF_ID_ORDER_OUT_INCOMING_INVOICE].map((wfId) => {
        const wf = workflowsKv[wfId];
        let subEle = null;
        if (wfId == WF_ID_ORDER_OUT_INCOMING_INVOICE) {
          subEle = (
            <WorkflowOrderOutIncomingInvoice
              wf={wf}
              steps={wf?.steps ?? []}
              order_no={order_out_order_no}
            />
          );
        }

        return <div key={wfId}>{subEle}</div>;
      });
    } else if (cat1 == 'OrderIn') {
    } else {
    }
    return mainEle;
  }, [workflowsKv, cat1, order_out_order_no]);

  return <Spin spinning={loading}>{body}</Spin>;
};

export default WorkflowScanFile;
