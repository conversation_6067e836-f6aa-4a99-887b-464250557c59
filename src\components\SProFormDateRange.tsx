import type { ProFormInstance, ProFormItemProps } from '@ant-design/pro-form';
import { ProFormDependency } from '@ant-design/pro-form';
import { ProFormItem } from '@ant-design/pro-form';
import type { ProFormFieldItemProps } from '@ant-design/pro-form/lib/interface';
import moment from 'moment';
import type { ReactNode } from 'react-markdown/lib/ast-to-react';
import SDatePicker from './SDatePicker';

type SProFormDateRangePropsType = {
  label?: string | ReactNode;
  startDateName: string;
  endDateName?: string;
  formRef?: React.MutableRefObject<ProFormInstance<any> | undefined>;

  fieldItemProps1?: ProFormFieldItemProps;
  fieldItemProps2?: ProFormFieldItemProps;
} & ProFormItemProps;

const SProFormDateRange: React.FC<SProFormDateRangePropsType> = ({
  label,
  startDateName,
  endDateName,

  formRef,
  fieldItemProps1,
  fieldItemProps2,
  ...rest
}) => {
  return (
    <ProFormItem label={label} {...rest}>
      <div style={{ display: 'flex' }}>
        <SDatePicker
          {...fieldItemProps1}
          label={undefined}
          width={110}
          name={startDateName}
          addonAfter="~"
          placeholder="Start date"
          fieldProps={{ ...fieldItemProps1?.fieldProps }}
        />
        <ProFormDependency name={[`${startDateName}`]}>
          {(depValues) => {
            const startDate = depValues[startDateName];
            return (
              <SDatePicker
                {...fieldItemProps2}
                width={110}
                name={endDateName}
                placeholder="End date"
                fieldProps={{
                  style: { marginLeft: 6 },
                  ...fieldItemProps2?.fieldProps,
                  disabledDate: (current) => {
                    return current.isBefore(moment(startDate));
                  },
                }}
              />
            );
          }}
        </ProFormDependency>
      </div>
    </ProFormItem>
  );
};

export default SProFormDateRange;
