import React, { useRef } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util from '@/util';
import { getDictList, updateDict } from '@/services/app/Sys/sys-dict';
import { DEFAULT_PER_PAGE_PAGINATION, DictCode, DictType, DictTypeKv } from '@/constants';
import EditableCell from '@/components/EditableCell';
import { getAddressList } from '@/services/app/Sys/address';
import { getSysAddressXlsTplACList } from '@/services/app/Sys/sys-address-xls-tpl';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import { Card } from 'antd';

const DictList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance<API.Dict>>();

  const columns: ProColumns<API.Dict>[] = [
    {
      title: 'Code',
      dataIndex: 'code',
      sorter: true,
      width: 250,
      className: 'text-sm c-grey',
      copyable: true,
    },
    {
      title: 'Type',
      dataIndex: 'type',
      sorter: true,
      width: 150,
      ellipsis: true,
      className: 'text-sm c-grey',
      render(__, record, index, action, schema) {
        return record.type ? DictTypeKv[record.type as DictType] ?? record.type : undefined;
      },
    },
    {
      title: 'Label',
      dataIndex: 'label',
      sorter: true,
      width: 300,
      ellipsis: true,
      render: (dom, record) => {
        return (
          <EditableCell
            dataType="text"
            defaultValue={record.label}
            triggerUpdate={function (value: any): Promise<void> {
              const data: API.Dict = { label: value, code: record.code };
              return updateDict(data).then(() => actionRef.current?.reload());
            }}
          >
            {record.label}
          </EditableCell>
        );
      },
    },
    {
      title: 'Value',
      dataIndex: 'value',
      render: (dom, record) => {
        let dataType: API.HtmlFieldType = 'text';

        if (record.code == DictCode.CODE_ALWAYS_ON_LOADING_ADDRESS) {
          dataType = 'multiselect';
          return (
            <EditableCell
              dataType={dataType}
              defaultValue={(record.value ?? '').split(',')}
              convertValue={(value) => `${value}`}
              request={async (params) => {
                return getAddressList({ ...params, pageSize: 200, page: 1 }).then((res) =>
                  res.data.map((a) => ({ value: a.id, label: `${a.company ?? '-'} | ${a.full}` })),
                );
              }}
              triggerUpdate={function (
                value: any,
                cancelEdit?: (() => void) | undefined,
              ): Promise<void> {
                const data: API.Dict = { value: (value || []).join(','), code: record.code };
                return updateDict(data)
                  .then(() => {
                    actionRef.current?.reload();
                    cancelEdit?.();
                  })
                  .catch((e) => Util.error(e));
              }}
            >
              {record.value}
            </EditableCell>
          );
        } else if (record.code == DictCode.ORDER_OUT_CRM_XLS_TEMPLATE) {
          dataType = 'select';
          return (
            <EditableCell
              dataType={dataType}
              defaultValue={`${record.value}`}
              convertValue={(value) => `${value}`}
              request={async (params) => {
                return getSysAddressXlsTplACList({ ...params, pageSize: 200, page: 1 }).then(
                  (res) => res.map((x) => ({ ...x, value: `${x.value}` })),
                );
              }}
              triggerUpdate={function (
                value: any,
                cancelEdit?: (() => void) | undefined,
              ): Promise<void> {
                const data: API.Dict = { value: value, code: record.code };
                return updateDict(data)
                  .then(() => {
                    actionRef.current?.reload();
                    cancelEdit?.();
                  })
                  .catch((e) => Util.error(e));
              }}
            >
              {record.value}
            </EditableCell>
          );
        }

        return (
          <EditableCell
            dataType={dataType}
            defaultValue={record.value}
            triggerUpdate={function (
              value: any,
              cancelEdit?: (() => void) | undefined,
            ): Promise<void> {
              const data: API.Dict = { value: value, code: record.code };
              /* if (value == 'switch') {
                data.options = YNOptions;
                // data.default_value = [0];
              } else if (value == 'radio') {
                data.options = YNOptionsStr;
                // data.default_value = ['0'];
              } else if (value == 'text' || value == 'textarea') {
                data.options = [];
              } */
              return updateDict(data)
                .then(() => {
                  actionRef.current?.reload();
                  cancelEdit?.();
                })
                .catch((e) => Util.error(e));
            }}
          >
            {record.value}
          </EditableCell>
        );
      },
    },
    {
      title: 'Note',
      dataIndex: 'desc',
      sorter: true,
      width: 200,
      className: 'text-sm c-grey',
    },
  ];

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<API.Dict>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_sys_dict', {})}
          submitter={{
            submitButtonProps: { htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => {
              searchFormRef.current?.setFieldsValue({});
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormSelect
            width="md"
            name="type"
            label="Type"
            options={Object.values(DictType).map((x) => ({
              value: x,
              label: DictTypeKv[x] ?? x,
            }))}
            fieldProps={{ onChange: (value) => actionRef.current?.reload() }}
          />
        </ProForm>
      </Card>
      <ProTable<API.Dict, API.PageParams>
        headerTitle={'System Configurations & Dictionaries'}
        actionRef={actionRef}
        rowKey="code"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        scroll={{ x: 800 }}
        size="small"
        search={false}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        request={(params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue() ?? {};
          Util.setSfValues('sf_sys_dict', searchFormValues);
          return getDictList({ ...params, ...searchFormValues }, sort, filter);
        }}
        columns={columns}
        columnEmptyText=""
        /* rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }} */
      />
      {/* {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              chosen{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              Dict &nbsp;&nbsp;
            </div>
          }
        >
          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            Bacth deletion
          </Button>
        </FooterToolbar>
      )} */}

      {/* <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      /> */}

      {/* <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.number && (
          <ProDescriptions<API.Dict>
            column={2}
            title={currentRow?.number}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.number,
            }}
            columns={columns as ProDescriptionsItemProps<API.Dict>[]}
          />
        )}
      </Drawer> */}
    </PageContainer>
  );
};

export default DictList;
