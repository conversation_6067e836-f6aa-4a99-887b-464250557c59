import { FileCategory } from '@/constants';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormUploadDragger } from '@ant-design/pro-form';
import { message, Modal } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect } from 'react';
import { useRef } from 'react';
import _ from 'lodash';
import type { UploadFile } from 'antd/lib/upload/interface';
import { uploadOrderOutDetailFile } from '@/services/app/Order/order-out';
import Util from '@/util';
import { deleteFile } from '@/services/app/file';

type FileDetailUploadFormProps = {
  orderOut: API.OrderOut;
  setOrderOut: SetStateAction<Dispatch<API.OrderOut>>;
  // loadOrderOut?: (cb?: any) => void;
};

const FileDetailUploadForm: React.FC<FileDetailUploadFormProps> = (props) => {
  const { orderOut, setOrderOut } = props;

  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    formRef.current?.setFieldValue('files', orderOut.detail_file ? [orderOut.detail_file] : []);
  }, [orderOut.detail_file]);

  return (
    <>
      <ProForm<{ files?: UploadFile[] }>
        formRef={formRef}
        layout="horizontal"
        labelCol={{ span: 14 }}
        labelAlign="left"
        className="hor-dragger-wrap"
        onValuesChange={async (changedValues, formValues) => {
          if ('files' in changedValues && changedValues.files.length) {
            const lastFile = changedValues.files[changedValues.files.length - 1];
            if (lastFile.originFileObj) {
              const formData = new FormData();
              formData.set('order_no', `${orderOut.order_no}`);
              formData.set('file', lastFile.originFileObj);
              formData.set('category', FileCategory.CAT_ORDER_OUT_DETAIL_FILE);

              const hide = message.loading('Uploading...', 0);
              uploadOrderOutDetailFile(formData)
                .then((res) => {
                  message.success('Uploaded successfully.');
                  formRef.current?.setFieldValue('files', [res]);
                  setOrderOut((prev) => ({ ...prev, detail_file_id: res.id, detail_file: res }));
                })
                .catch((e) => Util.error(e))
                .finally(() => hide());
            }
          }
        }}
        submitter={false}
      >
        <ProFormUploadDragger
          name="files"
          label=""
          title="Select File"
          description="or drag & drop XLS file here."
          accept=".xls,.xlsx"
          required
          icon={false}
          formItemProps={{ style: { marginBottom: 0, maxWidth: '100%', width: '100%' } }}
          fieldProps={{
            iconRender: undefined,
            beforeUpload: async (file, fList) => {
              return false;
            },
            onRemove: async (file: API.File) => {
              const { confirm } = Modal;
              return new Promise((resolve, reject) => {
                confirm({
                  title: 'Are you sure you want to delete?',
                  onOk: async () => {
                    if (file.id) {
                      await deleteFile(file.id).catch((e) => Util.error(e));
                      setOrderOut((prev) => ({
                        ...prev,
                        detail_file: null,
                        detail_file_id: undefined,
                      }));
                    }
                    resolve(true);

                    formRef.current?.setFieldValue('files', []);
                    return true;
                  },
                  onCancel: () => {
                    reject(true);
                  },
                });
              });
            },
          }}
        />
      </ProForm>
    </>
  );
};

export default FileDetailUploadForm;
