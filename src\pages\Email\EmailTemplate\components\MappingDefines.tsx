import { InfoCircleOutlined } from '@ant-design/icons';
import { Popover, Typography } from 'antd';
import { useModel } from 'umi';

export type MappingDefinesProps = object;

const MappingDefines: React.FC<MappingDefinesProps> = (props) => {
  const { mappingData } = useModel('email-template-mapping');

  return (
    <>
      <Popover
        title="Mapping code."
        // trigger="click"
        content={
          <div style={{ maxHeight: 550, overflowY: 'auto' }}>
            {Object.keys(mappingData.fields).map((x) => (
              <Typography.Paragraph
                key={x}
                copyable={{ text: `{${x}}` }}
                style={{ marginBottom: 2, fontSize: 12, display: 'flex', paddingRight: 12 }}
              >
                <span style={{ display: 'flex', flex: '0 0 250px', paddingRight: 8 }}>
                  {mappingData.fields[x]}
                </span>
                <span style={{ fontWeight: 'bold' }}>{x}</span>
              </Typography.Paragraph>
            ))}
          </div>
        }
      >
        <InfoCircleOutlined title="View mapping code definitions for advanced usages." />
      </Popover>
    </>
  );
};

export default MappingDefines;
