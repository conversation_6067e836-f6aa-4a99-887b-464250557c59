import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useState } from 'react';
import React from 'react';
import { <PERSON>ton, Card, Col, message, Row } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import Util from '@/util';
import { getCustomerList, updateCustomer } from '@/services/app/BasicData/customer';
import { updateOrderOut } from '@/services/app/Order/order-out';
import AddressListVariant from '@/pages/Sys/AddressList/variant';
import OrderOutAddress from './address/OrderOutAddress';
import { getAddressList } from '@/services/app/Sys/address';
import { isEqualAddresses } from '..';
import ContactList from '@/pages/Contact/ContactList';
import { ContactType } from '@/constants';
import HtmlEditor from '@/components/HtmlEditor';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateOrderOut(fields);
    hide();
    message.success('Update is successful');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.OrderOut>;

export type CustomerSelectFormProps = {
  initialValues?: API.OrderOut;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  formRef: React.MutableRefObject<ProFormInstance<FormValueType> | undefined>;

  loadOrderOut?: (no?: number) => void;
};

const CustomerSelectForm: React.FC<CustomerSelectFormProps> = (props) => {
  const { initialValues, modalVisible, handleModalVisible, formRef, loadOrderOut } = props;
  const [customerId, setCustomerId] = useState<any>();
  const [refreshTick, setRefreshTick] = useState<number>(0); // used to refresh child contact list

  // customer's address list
  const [addressList, setAddressList] = useState<API.Address[]>([]);

  useEffect(() => {
    setCustomerId(initialValues?.customer_id);
  }, [initialValues?.customer_id]);

  useEffect(() => {
    if (!modalVisible) return;
    if (customerId) {
      getAddressList({ customer_id: customerId })
        .then((res) => {
          setAddressList(res.data);
        })
        .catch((e) => Util.error(e));
    } else {
      setAddressList([]);
    }
    setRefreshTick((prev) => prev + 1);
  }, [customerId, modalVisible]);

  return (
    <ModalForm
      title={'Select customer'}
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ flex: '0 0 130px' }}
      initialValues={initialValues || {}}
      formRef={formRef}
      submitter={false}
      width={customerId ? 1200 : 800}
      style={{ position: 'relative' }}
    >
      <Row gutter={32} wrap={false}>
        <Col span={customerId ? 16 : 24}>
          <ProFormSelect
            name={['customer_id']}
            label="Customer"
            showSearch={true}
            fieldProps={{
              filterOption: false,
              onChange: (value) => {
                handleUpdate({
                  customer_id: value ?? null,
                  order_no: initialValues?.order_no,
                })
                  .then((res) => {
                    loadOrderOut?.(initialValues?.order_no);
                    setCustomerId(value);
                  })
                  .catch((e) => Util.error(e));
              },
            }}
            request={(params) =>
              getCustomerList(params, {}, {}).then((res) =>
                res.data?.map((x) => ({
                  value: x.id,
                  label: `${x.name}${x.description ? ` - ${x.description.substring(0, 20)}` : ''}`,
                })),
              )
            }
          />
          <Card bodyStyle={{ padding: 0, marginBottom: 24 }} bordered={false}>
            <ContactList
              type={'OrderOut'}
              contact_type={ContactType.CUSTOMER_CONTACT}
              customer_id={customerId}
              refreshTick={refreshTick}
            />
          </Card>

          <Card bodyStyle={{ padding: 0 }} bordered={false}>
            <AddressListVariant
              order_no={props.initialValues?.order_no}
              customer_id={customerId}
              tableTitle="Customer addreses"
              addressList={addressList}
              setAddressList={setAddressList}
              loadOrderOut={loadOrderOut}
            />
          </Card>
          <Row gutter={24} style={{ marginTop: 24 }}>
            <Col span={12}>
              <OrderOutAddress
                card_title="Invoice address"
                customer_id={customerId}
                order_no={props.initialValues?.order_no}
                type="OrderOut"
                address_type="invoice"
                addressList={addressList}
                address={initialValues?.addresses?.find((x) => x.address_type == 'invoice')}
                loadOrderOut={loadOrderOut}
              />
            </Col>
            <Col span={12}>
              <OrderOutAddress
                card_title="Shipping address"
                customer_id={customerId}
                order_no={props.initialValues?.order_no}
                type="OrderOut"
                address_type="shipping"
                addressList={addressList}
                address={initialValues?.addresses?.find((x) => x.address_type == 'shipping')}
                loadOrderOut={loadOrderOut}
                same_with_invoice={isEqualAddresses(
                  initialValues?.addresses?.find((x) => x.address_type == 'invoice'),
                  initialValues?.addresses?.find((x) => x.address_type == 'shipping'),
                )}
              />
            </Col>
          </Row>
        </Col>
        {customerId && (
          <Col span={8}>
            <div
              style={{
                display: 'flex',
                marginBottom: 12,
                gap: 16,
                height: 40,
                alignItems: 'center',
              }}
            >
              <ProFormText
                name="pallet"
                label="Pallet"
                initialValue={props.initialValues?.customer_obj?.pallet}
                labelCol={{ flex: '0 0 50px' }}
                formItemProps={{ style: { marginBottom: 0, flex: 1 } }}
              />
              <Button
                type="primary"
                size="small"
                onClick={() => {
                  const pallet = formRef.current?.getFieldValue('pallet');
                  updateCustomer({ id: customerId, pallet: pallet })
                    .then((res) => {
                      message.success('Saved pallet successfully.');
                    })
                    .catch((e) => Util.error(e));
                }}
              >
                Save
              </Button>
            </div>
            <div style={{ marginBottom: 12 }}>
              Notes:{' '}
              <Button
                type="primary"
                size="small"
                style={{ float: 'right' }}
                onClick={() => {
                  const notes = formRef.current?.getFieldValue('notes');
                  updateCustomer({ id: customerId, notes: notes })
                    .then((res) => {
                      message.success('Saved notes successfully.');
                    })
                    .catch((e) => Util.error(e));
                }}
              >
                Save
              </Button>
            </div>
            <ProForm.Item name={'notes'} labelCol={{ span: 0 }}>
              <HtmlEditor initialValue={props.initialValues?.customer_obj?.notes} height={500} />
            </ProForm.Item>
          </Col>
        )}
      </Row>
    </ModalForm>
  );
};

export default CustomerSelectForm;
