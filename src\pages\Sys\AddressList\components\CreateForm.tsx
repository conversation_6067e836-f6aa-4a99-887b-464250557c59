import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { addAddress } from '@/services/app/Sys/address';
import { Col, Divider, message, Row } from 'antd';
import Util from '@/util';
import { useModel } from 'umi';
import ContactInfo from './ContactInfo';
import HtmlEditor from '@/components/HtmlEditor';
import { AddressLoTaxTypeOptions } from '@/constants';

const handleAdd = async (fields: API.Address) => {
  const hide = message.loading('Adding...');
  const data = { ...fields };
  try {
    await addAddress(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

// eslint-disable-next-line @typescript-eslint/ban-types
export type FormValueType = {};

export type CreateFormProps = {
  values?: Partial<API.Address>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Address) => Promise<boolean | void>;

  customer_id?: number;
  supplier_id?: number;
  isSupplier?: boolean;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const { modalVisible, handleModalVisible, onSubmit, customer_id, supplier_id, isSupplier } =
    props;
  const formRef = useRef<ProFormInstance>();
  const { countries } = useModel('sys-country');
  // const [country, setCountry] = useState<string>('DE');
  // const [regionList, setRegionList] = useState<DefaultOptionType[]>([]);

  /* useEffect(() => {
    if (modalVisible) {
      // setCountry('DE');
      // We don't set DE as a default.
      // formRef.current?.setFieldsValue({ country_code: 'DE' });
    }
  }, [modalVisible]); */

  /* useEffect(() => {
    getCountryRegionList({ country_code: country, pageSize: 500 }, {}, {}).then((res) => {
      setRegionList(res.data.map((x) => ({ value: x.id, label: x.default_name })));
      formRef.current?.setFieldsValue({ region_id: res.data?.[0]?.id });
    });
  }, [country]); */

  return (
    <ModalForm
      title={'New address'}
      width="1000px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ flex: '0 0 120px' }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd({ ...value, customer_id, supplier_id } as API.Address);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <Row gutter={32}>
        <Col span={12}>
          <ContactInfo isSupplier={isSupplier} />
          <Divider />
          <ProFormText width="md" name="street" label="Street 1" placeholder="Street 1" />
          <ProFormText width="md" name="street2" label="Street 2" placeholder="Street 2" />
          <ProFormText width="sm" name="postcode" label="Zip" placeholder="Zip" />
          <ProFormText width="sm" name="city" label="City" placeholder="City" />

          <ProFormSelect
            name={['country_code']}
            label="Country"
            showSearch
            // fieldProps={{ onChange: (value) => setCountry(value) }}
            options={countries.map((x) => ({ value: x.code, label: x.name }))}
          />

          {/* <ProFormSelect
        name={['region_id']}
        label="Region"
        mode="single"
        options={regionList}
        showSearch
      />

      <ProFormDependency name={['region_id']}>
        {(depValues) => {
          return depValues.region_id ? (
            <></>
          ) : (
            <>
              <Col style={{ paddingLeft: 120 }}>
                <Divider>or</Divider>
              </Col>
              <ProFormText
                width="md"
                name="region"
                tooltip="Fill regison in case of non-existence"
                label={null}
                wrapperCol={{ style: { marginLeft: 120 } }}
              />
            </>
          );
        }}
      </ProFormDependency> */}
          {props.isSupplier && (
            <>
              <Divider />
              <ProFormText name={['opening_hours']} label="Opening hours" />
            </>
          )}

          {!props.isSupplier && (
            <>
              <Divider />
              <ProFormSelect
                name={['lo_tax_type']}
                label="TaxFree Invoices"
                showSearch
                options={AddressLoTaxTypeOptions}
              />
            </>
          )}
        </Col>
        <Col span={12}>
          <div style={{ marginBottom: 12 }}>Detail: </div>
          <ProForm.Item name={'detail'} labelCol={{ span: 0 }} id={`detail-creation`}>
            <HtmlEditor initialValue={''} min_height={600} />
          </ProForm.Item>
        </Col>
      </Row>
    </ModalForm>
  );
};

export default CreateForm;
