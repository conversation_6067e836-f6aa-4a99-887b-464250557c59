import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useState } from 'react';
import React from 'react';
import { <PERSON>ton, Card, Col, message, Row } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import Util from '@/util';
import { getSupplierList, updateSupplier } from '@/services/app/BasicData/supplier';
import { updateOrderOut } from '@/services/app/Order/order-out';
import AddressListVariant from '@/pages/Sys/AddressList/variant';
import { getAddressList } from '@/services/app/Sys/address';
import OrderOutAddress from './address/OrderOutAddress';
import { loadLastOrderTypeBySupplier, saveLastOrderTypeBySupplier } from '..';
import ContactList from '@/pages/Contact/ContactList';
import { ContactType } from '@/constants';
import HtmlEditor from '@/components/HtmlEditor';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateOrderOut(fields);
    hide();
    message.success('Update is successful');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.OrderOut>;

export type SupplierSelectFormProps = {
  initialValues?: Partial<API.OrderOut>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  formRef: React.MutableRefObject<ProFormInstance<FormValueType> | undefined>;

  loadOrderOut?: (no?: number) => void;
};

const SupplierSelectForm: React.FC<SupplierSelectFormProps> = (props) => {
  const { initialValues, modalVisible, handleModalVisible, formRef, loadOrderOut } = props;
  const [supplierId, setSupplierId] = useState<any>();
  const [refreshTick, setRefreshTick] = useState<number>(0);

  // supplier's address list
  const [addressList, setAddressList] = useState<API.Address[]>([]);

  useEffect(() => {
    setSupplierId(initialValues?.supplier_id);
  }, [initialValues?.supplier_id]);

  useEffect(() => {
    if (supplierId) {
      getAddressList({ supplier_id: supplierId })
        .then((res) => {
          setAddressList(res.data);
        })
        .catch((e) => Util.error(e));
    } else {
      setAddressList([]);
    }

    setRefreshTick((prev) => prev + 1);
  }, [supplierId, modalVisible]);

  useEffect(() => {
    formRef.current?.setFieldValue('pallet', initialValues?.supplier_obj?.pallet || '');
  }, [formRef, initialValues?.supplier_obj?.pallet]);

  return (
    <ModalForm
      title={'Select supplier'}
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      initialValues={initialValues || {}}
      formRef={formRef}
      width={supplierId ? 1200 : 800}
      submitter={false}
    >
      <Row gutter={32} wrap={false}>
        <Col span={supplierId ? 16 : 24}>
          <ProFormSelect
            name={['supplier_id']}
            label="Supplier"
            showSearch={true}
            debounceTime={200}
            fieldProps={{
              filterOption: false,
              onChange: (value) => {
                const data = {
                  supplier_id: value ?? null,
                  order_no: initialValues?.order_no,
                };
                // save last order type section
                if (value && !initialValues?.type_code && loadLastOrderTypeBySupplier(value)) {
                  (data as any).type_code = loadLastOrderTypeBySupplier(value);
                }
                handleUpdate(data)
                  .then((res) => {
                    loadOrderOut?.(initialValues?.order_no);
                    setSupplierId(value);
                    // save last order type section
                    if (value && initialValues?.type_code) {
                      saveLastOrderTypeBySupplier(value, initialValues?.type_code);
                    }
                  })
                  .catch((e) => Util.error(e));
              },
            }}
            request={(params) =>
              getSupplierList(params, {}, {}).then((res) =>
                res.data?.map((x) => ({
                  value: x.id,
                  label: `${x.name}${x.description ? ` - ${x.description.substring(0, 20)}` : ''}`,
                })),
              )
            }
          />
          <Card bodyStyle={{ padding: 0, marginBottom: 24 }} bordered={false}>
            <ContactList
              type={'OrderOut'}
              contact_type={ContactType.SUPPLIER_CONTACT}
              supplier_id={supplierId}
              refreshTick={refreshTick}
            />
          </Card>

          <Card bodyStyle={{ padding: 0 }} bordered={false}>
            <AddressListVariant
              order_no={props.initialValues?.order_no}
              supplier_id={supplierId}
              tableTitle="Supplier addresses"
              addressList={addressList}
              setAddressList={setAddressList}
              loadOrderOut={loadOrderOut}
              isSupplier={true}
            />
          </Card>
          <Row gutter={24} style={{ marginTop: 24 }}>
            <Col span={12}>
              <OrderOutAddress
                card_title="Loading address"
                supplier_id={supplierId}
                order_no={props.initialValues?.order_no}
                type="OrderOut"
                address_type="loading"
                addressList={addressList}
                address={initialValues?.addresses?.find((x) => x.address_type == 'loading')}
                loadOrderOut={loadOrderOut}
              />
            </Col>
          </Row>
        </Col>
        {supplierId && (
          <Col span={8}>
            <div
              style={{
                display: 'flex',
                marginBottom: 12,
                gap: 16,
                height: 40,
                alignItems: 'center',
              }}
            >
              <ProFormText
                name="pallet"
                initialValue={props.initialValues?.supplier_obj?.pallet}
                label="Pallet"
                labelCol={{ flex: '0 0 50px' }}
                formItemProps={{ style: { marginBottom: 0, flex: 1 } }}
              />
              <Button
                type="primary"
                size="small"
                onClick={() => {
                  const pallet = formRef.current?.getFieldValue('pallet');
                  updateSupplier({ id: supplierId, pallet: pallet })
                    .then((res) => {
                      message.success('Saved pallet successfully.');
                      loadOrderOut?.(initialValues?.order_no);
                    })
                    .catch((e) => Util.error(e));
                }}
              >
                Save
              </Button>
            </div>
            <div style={{ marginBottom: 12 }}>
              Notes:{' '}
              <Button
                type="primary"
                size="small"
                style={{ float: 'right' }}
                onClick={() => {
                  const notes = formRef.current?.getFieldValue('notes');
                  updateSupplier({ id: supplierId, notes: notes })
                    .then((res) => {
                      message.success('Saved notes successfully.');
                      loadOrderOut?.(initialValues?.order_no);
                    })
                    .catch((e) => Util.error(e));
                }}
              >
                Save
              </Button>
            </div>
            <ProForm.Item name={'notes'} labelCol={{ span: 0 }}>
              <HtmlEditor initialValue={props.initialValues?.supplier_obj?.notes} height={500} />
            </ProForm.Item>
          </Col>
        )}
      </Row>
    </ModalForm>
  );
};

export default SupplierSelectForm;
