// @ts-ignore
/* eslint-disable */
import qs from 'qs';
import { request } from 'umi';

export const paramsSerializer = (paramsTmp: any) => {
  return qs.stringify(paramsTmp, {
    encoder(str, defaultEncoder, charset, type) {
      if (type == 'value' && typeof str == 'boolean') return str ? 1 : '';
      return str;
    },
  });
};

/** user info GET /api/currentUser */
export async function currentUser(options?: { [key: string]: any }): Promise<API.CurrentUser> {
  return request<API.BaseResult>('/api/currentUser', {
    method: 'GET',
    ...(options || {}),
    withToken: true,
  }).then((res) => res.message);
}

/** user info GET /api/countries */
export async function getCountries(options?: { [key: string]: any }): Promise<any[]> {
  return request<API.BaseResult>('/api/countries', {
    method: 'GET',
    ...(options || {}),
    withToken: true,
  }).then((res) => res.message);
}

const urlPrefixMagento = '/api/magento';
/** rule GET /api/magento/store/storeConfig */
export async function dsStoreConfig() {
  return request<API.BaseResult>(`${urlPrefixMagento}/store/storeConfig`, {
    method: 'GET',
    withToken: true,
    paramsSerializer: (paramsTmp: any) => {
      return qs.stringify(paramsTmp);
    },
  }).then((res) => res.message);
}

/** productAttributes GET /api/magento/store/productAttributes */
export async function dsProductAttributes() {
  return request<API.BaseResult>(`${urlPrefixMagento}/store/productAttributes`, {
    method: 'GET',
    withToken: true,
    paramsSerializer: (paramsTmp: any) => {
      return qs.stringify(paramsTmp);
    },
  }).then((res) => res.message);
}

/** productAttributeSet GET /api/magento/store/productAttributeSet */
export async function dsProductAttributeSet() {
  return request<API.BaseResult>(`${urlPrefixMagento}/store/productAttributeSet`, {
    method: 'GET',
    withToken: true,
    paramsSerializer: (paramsTmp: any) => {
      return qs.stringify(paramsTmp);
    },
  }).then((res) => res.message);
}

/** orders GET /api/magento/orders/latest */
export async function dsOrders() {
  return request<API.BaseResult>(`${urlPrefixMagento}/orders/latest`, {
    method: 'GET',
    withToken: true,
    paramsSerializer: (paramsTmp: any) => {
      return qs.stringify(paramsTmp);
    },
  }).then((res) => res.message);
}

/** orders GET /api/magento/orders/latest */
export async function dsFullOrders() {
  return request<API.BaseResult>(`${urlPrefixMagento}/orders/full`, {
    method: 'GET',
    withToken: true,
    paramsSerializer: (paramsTmp: any) => {
      return qs.stringify(paramsTmp);
    },
  }).then((res) => res.message);
}

/** store config info GET /api/magento-data/store/storeConfig */
export async function getMagentoStoreConfig(options?: {
  [key: string]: any;
}): Promise<API.CurrentUser> {
  return request<API.BaseResult>('/api/magento-data/store/storeConfig', {
    method: 'GET',
    ...(options || {}),
    withToken: true,
  }).then((res) => res.message);
}

/** store websites info GET /api/magento-data/store/storeWebsites */
export async function getMagentoStoreWebsites(options?: {
  [key: string]: any;
}): Promise<API.CurrentUser> {
  return request<API.BaseResult>('/api/magento-data/store/storeWebsites', {
    method: 'GET',
    ...(options || {}),
    withToken: true,
  }).then((res) => res.message);
}

/** Get application settings: GET /api/app-settings */
export async function getAppSettings(options?: { [key: string]: any }): Promise<API.AppSettings> {
  return request<API.BaseResult>('/api/app-settings', {
    method: 'GET',
    ...(options || {}),
    withToken: true,
  }).then((res) => res.message);
}

/** Get table lists GET /api/db/tables */
export async function getDBTables(params?: any, options?: { [key: string]: any }): Promise<any> {
  return request<API.BaseResult>('/api/db/tables', {
    method: 'GET',
    params,
    ...(options || {}),
    withToken: true,
  }).then((res) => res.message);
}

/** Get table lists GET /api/db/mappable-tables */
export async function getMappableDBTables(
  params?: any,
  options?: { [key: string]: any },
): Promise<any> {
  return request<API.BaseResult>('/api/db/mappable-tables', {
    method: 'GET',
    params,
    ...(options || {}),
    withToken: true,
  }).then((res) => res.message);
}

/** Get table lists GET /api/db/fields */
export async function getDBTableFields(table_name: string): Promise<any> {
  return request<API.BaseResult>('/api/db/fields', {
    method: 'GET',
    params: {
      table_name,
    },
    withToken: true,
  }).then((res) => res.message);
}
