import { useCallback, useState } from 'react';

export default () => {
  const [appSettings, setAppSettings] = useState<API.AppSettings>({ drSelection: {} });

  const getDictOptionsByType = useCallback(
    (type?: string) => {
      if (!type || !appSettings.dict) return [];

      return Object.keys(appSettings.dict)
        .filter((x) => (appSettings.dict as any)?.[x]?.type == type)
        .map((x) => ({
          value: (appSettings.dict as any)?.[x]?.code,
          label: (appSettings.dict as any)?.[x]?.label,
        }));
    },
    [appSettings.dict],
  );

  const getDictListByType = useCallback(
    (type?: string) => {
      if (!type || !appSettings.dict) return [];

      return Object.keys(appSettings.dict).filter((x) => (appSettings.dict as any)?.[x]?.type == type);
    },
    [appSettings.dict],
  );

  const getCode = useCallback(
    (code?: string) => {
      return (appSettings.dict as any)?.[code || ''];
    },
    [appSettings.dict],
  );

  const getCodeLabel = useCallback(
    (code?: string) => {
      return getCode(code)?.label;
    },
    [getCode],
  );

  const getCodeValue = useCallback(
    (code?: string) => {
      return getCode(code)?.value;
    },
    [getCode],
  );

  return {
    appSettings,
    setAppSettings,
    getDictListByType,
    getDictOptionsByType,
    getCodeLabel,
    getCode,
    getCodeValue,
  };
};
