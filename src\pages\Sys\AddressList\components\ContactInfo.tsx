import { ProFormText } from '@ant-design/pro-form';

type ContactInfoProps = {
  isSupplier?: boolean;
};
const ContactInfo: React.FC<ContactInfoProps> = (props) => {
  return (
    <>
      <ProFormText width="md" name="company" label="Company" placeholder="Company" />
      <ProFormText name="company2" label="Company 2" placeholder="Company 2" />

      {!props.isSupplier && <ProFormText name="cust_no" label="Cust No" placeholder="Cust No" />}
      {!props.isSupplier && <ProFormText name="vat_id" label="VAT ID" placeholder="VAT ID" />}
      {/* <Row gutter={16}>
        <Col span={14}>
          <ProFormText width="md" name="firstname" label="Name" placeholder="First name" />
        </Col>
        <Col span={10}>
          <ProFormText width="md" name="lastname" label="" placeholder="Last name" />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={14}>
          <ProFormText name="telephone" label="Telephone" placeholder="Telephone" />
        </Col>
        <Col span={10}>
          <ProFormText name="fax" label="Fax" placeholder="Fax" labelCol={{ flex: '0 1 50px' }} />
        </Col>
      </Row>
      <ProFormText name="email" label="Email" placeholder="Email" /> */}
    </>
  );
};
export default ContactInfo;
