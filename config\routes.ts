export default [
  {
    path: '/user',
    layout: false,
    routes: [
      {
        name: 'login',
        path: '/user/login',
        component: './user/Login',
      },
    ],
  },
  {
    path: '/welcome',
    name: 'Dashboard',
    icon: 'dashboard',
    component: './Welcome',
  },
  {
    path: '/order',
    name: 'Order',
    icon: 'form',
    routes: [
      {
        path: '/order/in',
        name: 'Order In',
        icon: 'ShopOutlined',
        component: './Order/OrderIn',
        access: 'canAdmin',
      },
      {
        path: '/order/out',
        name: 'Order Out',
        icon: 'CarryOutOutlined',
        // component: './Order/OrderOut', // version 1.0
        component: './Order/OrderOut/OrderOutListWithWorkflowStep', // version 2.0
        access: 'canAdmin',
      },
      {
        path: '/order/in-document',
        name: 'Order In Document',
        icon: 'ShopOutlined',
        hideInMenu: true,
        component: './Order/OrderInDocument',
      },
      {
        path: '/order/out-document',
        name: 'Order Out Document',
        icon: 'CarryOutOutlined',
        hideInMenu: true,
        component: './Order/OrderOutDocument/OrderOutDocumentListWithWorkflowStep', // version 2.0
      },
      {
        path: '/order/task-board-calendar',
        name: 'Task Calendar',
        icon: 'CalendarOutlined',
        hideInMenu: true,
        component: './TaskBoard/TaskCalendarPage',
      },
    ],
  },
  {
    path: '/order-out-detail',
    name: 'Order Out Detail',
    icon: 'CarryOutOutlined',
    hideInMenu: true,
    layout: false,
    component: '../layouts/OrderLayout',
    routes: [
      {
        hideInMenu: true,
        path: '/order-out-detail/lotus/:lotus_notes_id',
        name: 'Order Out Detail',
        component: './Order/OrderOutDetail',
        exact: true,
      },
      {
        hideInMenu: true,
        path: '/order-out-detail/:order_no',
        name: 'Order Out Detail',
        component: './Order/OrderOutDetail',
        exact: true,
      },
    ],
  },
  {
    path: '/task-board',
    name: 'Task Board',
    icon: 'DragOutlined',
    routes: [
      {
        path: '/task-board/:id',
        name: 'Task Group',
        icon: 'GroupOutlined',
        hideInMenu: true,
        component: './TaskBoard',
      },
    ],
  },
  {
    path: '/payments',
    name: 'Payments',
    icon: 'table',
    access: 'canAdmin',
    routes: [
      {
        path: '/payments/financial-summary',
        name: 'Financial Summary',
        icon: 'ShopOutlined',
        component: './Order/LoFinanceStat',
        access: 'canAdmin',
      },
      {
        path: '/payments/list',
        name: 'Payments',
        icon: 'ShopOutlined',
        component: './Order/LoPayment',
        access: 'canAdmin',
      },
      {
        path: '/payments/susa',
        name: 'SuSa',
        icon: 'ShopOutlined',
        component: './Order/LoSusa',
        access: 'canAdmin',
      },
      {
        path: '/payments/susa-summary',
        name: 'SuSa Summary',
        icon: 'ShopOutlined',
        component: './Order/LoSusa/LoSusaSummary',
        access: 'canAdmin',
      },
      {
        path: '/payments/invoices',
        name: 'Invoices',
        icon: 'ShopOutlined',
        component: './Order/LoInvoiceCheck',
        access: 'canAdmin',
      },
      {
        path: '/payments/invoices-stat',
        name: 'Invoices - Stat',
        icon: 'table',
        component: './Order/LoInvoiceCheck/LoInvoiceCheckStatList',
        access: 'canAdmin',
      },

      /* {
        path: '/payments/invoices-stat',
        name: 'Invoices - Stat',
        icon: 'table',
        component: './Fin/FinDetailStat',
        access: 'canAdmin',
      }, */
    ],
  },
  {
    path: '/email',
    name: 'Email',
    icon: 'MailOutlined',
    access: 'canAdmin',
    routes: [
      {
        path: '/email/list',
        name: 'Emails',
        icon: 'MailOutlined',
        component: './Email/EmailList',
      },
      {
        path: '/email/accounts-setting',
        name: 'Email Accounts',
        icon: 'SettingOutlined',
        component: './Email/EmailAccountList',
      },
      {
        path: '/email/server',
        name: 'Email Servers',
        icon: 'CloudServerOutlined',
        component: './Email/EmailServerList',
      },
      {
        path: '/email/template',
        name: 'Email Templates',
        icon: 'SettingOutlined',
        component: './Email/EmailTemplate',
      },
    ],
  },
  {
    path: '/basic-data',
    name: 'Basic data',
    icon: 'SettingOutlined',
    access: 'canAdmin',
    routes: [
      {
        path: '/basic-data/task/group',
        name: 'Task Groups',
        icon: 'GroupOutlined',
        component: './TaskGroupList',
        access: 'canAdmin',
        authority: ['canAdmin'],
      },
      {
        path: '/basic-data/notification',
        name: 'Notifications',
        icon: 'AlertOutlined',
        component: './Sys/Notification',
        authority: [],
      },
      /* {
        path: '/basic-data/task/block',
        name: 'Task Block',
        icon: 'table',
        component: './TaskBlockList',
      }, */
      {
        path: '/basic-data/customer',
        name: 'Customers',
        icon: 'table',
        component: './BasicData/CustomerList',
      },
      {
        path: '/basic-data/supplier',
        name: 'Suppliers',
        icon: 'table',
        component: './BasicData/SupplierList',
      },
      {
        path: '/basic-data/inventur-all',
        name: 'Inventur All',
        icon: 'table',
        component: './Fin/FinInventurAll',
        access: 'canAdmin',
      },
      {
        path: '/basic-data/text-module',
        name: 'Text Module',
        icon: 'FileTextOutlined',
        component: './Sys/TextModule',
      },
      {
        path: '/basic-data/address',
        name: 'Addresses',
        icon: 'table',
        component: './Sys/AddressList',
      },
      {
        path: '/basic-data/address-xls-tpl',
        name: 'Address XLS Template',
        icon: 'FileExcelOutlined',
        component: './Sys/SysAddressXlsTpl',
      },
      {
        path: '/basic-data/workflow',
        name: 'Workflows',
        icon: 'NodeIndexOutlined',
        component: './Sys/SysWorkflow',
      },
      {
        path: '/basic-data/xls-column-name-map',
        name: 'XLS Col Mapping',
        icon: 'FileExcelOutlined',
        component: './Sys/SysXlsColNameMap',
      },
      {
        path: '/basic-data/system-config',
        name: 'System Config',
        icon: 'SettingOutlined',
        component: './Sys/Dict',
      },
      {
        path: '/basic-data/users',
        name: 'Users',
        icon: 'user',
        component: './UsersList',
        access: 'canAdmin',
      },
    ],
  },
  {
    path: '/monitor',
    name: 'Monitor',
    icon: 'MonitorOutlined',
    routes: [
      /* {
        path: '/monitor/magento-sync-log',
        name: 'Sync Log',
        icon: 'table',
        component: './Audit/MagSyncLog',
      }, */
      {
        path: '/monitor/file-log',
        name: 'File Log',
        icon: 'FileSearchOutlined',
        component: './Audit/FileLog',
      },
    ],
  },
  {
    path: '/file-browser',
    name: 'FileBrowser',
    // icon: 'MonitorOutlined',
    hideInMenu: false,
    routes: [
      {
        path: '/file-browser/default-misc-files',
        name: 'Default Misc Files',
        component: './FolderViewer/MiscFileBrowser',
        hideInMenu: false,
      },
      {
        path: '/file-browser/main',
        name: 'FileBrowser',
        component: './FolderViewer',
        hideInMenu: false,
      },
    ],
  },

  {
    path: '/',
    redirect: '/welcome',
  },

  { component: './404' },
];
