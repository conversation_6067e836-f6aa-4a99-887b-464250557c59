import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect, ProFormText, ProFormUploadButton } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { addSysAddressXlsTpl } from '@/services/app/Sys/sys-address-xls-tpl';
import { message } from 'antd';
import Util from '@/util';
import type { RcFile } from 'antd/lib/upload';
import { XlsTemplateType, XlsTemplateTypeOptions } from '@/constants';

const handleAdd = async (data: API.SysAddressXlsTpl | FormData) => {
  const hide = message.loading('Adding...', 0);
  try {
    await addSysAddressXlsTpl(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type CreateFormProps = {
  values?: Partial<API.SysAddressXlsTpl>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.SysAddressXlsTpl) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={'New Text Module'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 6 }}
      formRef={formRef}
      onFinish={async (values) => {
        const data = new FormData();
        data.set('name', `${values.name}`);
        data.set('type', `${values.type}`);
        if (values?.files) {
          data.append(`file`, values?.files[0].originFileObj as RcFile);
        }

        const success = await handleAdd(data);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(values);
        }
      }}
    >
      <ProFormText
        required
        rules={[
          {
            required: true,
            message: 'Name is required',
          },
        ]}
        width="md"
        name="name"
        label="Name"
      />
      <ProFormSelect
        width="md"
        name="type"
        label="Type"
        rules={[
          {
            required: true,
            message: 'Type is required',
          },
        ]}
        options={XlsTemplateTypeOptions}
        initialValue={XlsTemplateType.ADDRESS}
      />

      <ProFormUploadButton
        max={1}
        name="files"
        label="File"
        title="Select File"
        accept=".xls,.xlsx"
        required
        rules={[
          {
            required: true,
            message: 'File is required',
          },
        ]}
        fieldProps={{
          beforeUpload: (file) => {
            return false;
          },
        }}
      />
    </ModalForm>
  );
};

export default CreateForm;
