import { LS_TOKEN_NAME } from '@/constants';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormUploadDragger } from '@ant-design/pro-form';
import { Modal } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect } from 'react';
import { useRef } from 'react';
import _ from 'lodash';
import type { UploadChangeParam, UploadFile } from 'antd/lib/upload/interface';
import Util from '@/util';
import { deleteFile } from '@/services/app/file';
import type { ProFormDraggerProps } from '@ant-design/pro-form/lib/components/UploadButton';

type FileVariantUploadFormProps = {
  orderOut: API.OrderOut;
  fileCategory: string;
  setOrderOut: SetStateAction<Dispatch<API.OrderOut>>;
  draggerProps?: Partial<ProFormDraggerProps>;
};

const FileVariantUploadForm: React.FC<FileVariantUploadFormProps> = (props) => {
  const { orderOut, setOrderOut, fileCategory, draggerProps } = props;

  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    const files = orderOut.sec_files
      ? [...orderOut.sec_files.filter((x) => x.category == fileCategory)]
      : [];
    formRef.current?.setFieldValue('files', files);
  }, [orderOut.sec_files, fileCategory]);

  return (
    <>
      <ProForm<{ files?: UploadFile[] }>
        formRef={formRef}
        layout="inline"
        labelCol={{ span: 14 }}
        labelAlign="left"
        className="hor-dragger-wrap"
        submitter={false}
      >
        <ProFormUploadDragger
          name="files"
          label=""
          title="Select File"
          description="or drag & drop file here."
          // accept=".xls,.xlsx"
          required
          icon={false}
          formItemProps={{ style: { marginBottom: 0, maxWidth: '100%', width: '100%' } }}
          fieldProps={{
            name: 'file',
            method: 'post',
            multiple: true,
            action: `${API_URL}/api/order/order-out/detail-file/upload`,
            data: {
              order_no: orderOut.order_no,
              category: fileCategory,
            },
            headers: {
              Authorization: `Bearer ${localStorage.getItem(LS_TOKEN_NAME)}`,
            },
            iconRender: undefined,
            onChange: (info: UploadChangeParam, updateState = true) => {
              if (info.file.status == 'done') {
                const res = info.file.response.message;
                info.file.url = res.url;
                info.file.uid = res.uid;
                (info.file as any).id = res.uid;
                (info.file as any).file_name = res.file_name;
                (info.file as any).clean_file_name = res.clean_file_name;
                (info.file as any).path = res.path;
                (info.file as any).org_path = res.org_path;

                const newFiles = [...info.fileList];
                formRef.current?.setFieldsValue({ files: newFiles });
              } else if (typeof info.file.status === 'undefined') {
                const newFiles = formRef.current?.getFieldValue('files') ?? [];
                const index = _.findIndex(newFiles, { uid: info.file.uid });

                if (index > -1) newFiles.splice(index, 1);
                formRef.current?.setFieldsValue({ files: newFiles });
              }
            },
            onRemove: async (file: API.File) => {
              const { confirm } = Modal;
              return new Promise((resolve, reject) => {
                confirm({
                  title: 'Are you sure you want to delete?',
                  onOk: async () => {
                    if (file.id) {
                      await deleteFile(file.id).catch((e) => Util.error(e));
                      setOrderOut((prev) => ({
                        ...prev,
                        sec_files: prev.sec_files?.filter(
                          (x) => x.category == fileCategory && x.id != file.id,
                        ),
                      }));
                    }
                    resolve(true);
                    return true;
                  },
                  onCancel: () => {
                    reject(true);
                  },
                });
              });
            },
          }}
          {...draggerProps}
        />
      </ProForm>
    </>
  );
};

export default FileVariantUploadForm;
