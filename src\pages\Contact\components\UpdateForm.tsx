import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { updateContact } from '@/services/app/Sys/contact';
import Util from '@/util';
import ContactPersonFormPartial from './ContactPersonFormPartial';
import type { ContactType } from '@/constants';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...');

  try {
    await updateContact(fields);
    hide();
    message.success('Update is successful');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

// eslint-disable-next-line @typescript-eslint/ban-types
export type FormValueType = {} & Partial<API.Contact>;

export type UpdateFormProps = {
  initialValues?: Partial<API.Contact>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Contact) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;

  customer_id?: number;
  supplier_id?: number;
  address_id?: number;
  contact_type: ContactType;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  return (
    <ModalForm
      title={'Update contact'}
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ flex: '0 0 120px' }}
      initialValues={props.initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate({
          ...value,
          id: props.initialValues?.id,
          address_id: props.address_id,
          supplier_id: props.supplier_id,
          customer_id: props.customer_id,
          contact_type: props.contact_type,
        });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ContactPersonFormPartial contact_type={props.contact_type} />
    </ModalForm>
  );
};

export default UpdateForm;
