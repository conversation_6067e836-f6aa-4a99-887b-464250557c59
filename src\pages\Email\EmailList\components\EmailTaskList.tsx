import { getEmailTaskList, unlinkEmailToTask } from '@/services/app/Task/task';
import Util, { sn } from '@/util';
import { LinkOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { message, Popconfirm, Tag } from 'antd';
import { useEffect, useRef } from 'react';

export type EmailTaskListProps = {
  id?: number;
  refreshTick?: number;
  reloadParent?: () => void;
};

const EmailTaskList: React.FC<EmailTaskListProps> = (props) => {
  const { id, refreshTick, reloadParent } = props;

  const actionRef = useRef<ActionType>();

  const columns: ProColumns<API.Task>[] = [
    {
      title: 'Title',
      dataIndex: 'title',
      sorter: true,
      width: 200,
      ellipsis: true,
      /* render: (dom, record) => {
        return <a onClick={() => {}}>{dom}</a>;
      }, */
    },
    {
      title: 'Desc',
      dataIndex: 'desc',
      sorter: false,
      width: 200,
      ellipsis: true,
    },
    {
      title: 'Blocks',
      dataIndex: ['task_blocks'],
      sorter: false,
      width: 200,
      render: (dom, record) => {
        return record.task_blocks?.map((block) => (
          <Tag key={block.id} style={{ marginTop: 2, marginBottom: 2 }} className="relative">
            {block.task_group?.name} - {block.name}
          </Tag>
        ));
        /* return record.task_blocks?.map((block) => (
          <Popconfirm
            key={block.id}
            className="cursor-pointer"
            title={<>Are you sure you want to unlink?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ width: 300 }}
            onConfirm={async () => {
              const hide = message.loading('Linking the Email to selected tasks...', 0);
              unlinkEmailToTask(id, [block.id])
                .then((newEmail) => {
                  message.success('Linked successfully.');
                  actionRef.current?.reload();
                })
                .catch((e) => Util.error(e))
                .finally(() => hide());
            }}
          >
            <Tag style={{ marginTop: 2, marginBottom: 2 }} className="relative">
              {block.task_group?.name}-{block.name}
              {/* <div className="absolute text-sm" style={{ right: 0, top: 0 }}>
              <CloseOutlined />
            </div> * /}
            </Tag>
          </Popconfirm>
        )); */
      },
    },
    {
      title: 'ID',
      dataIndex: 'id',
      sorter: true,
      align: 'center',
      width: 70,
      ellipsis: true,
    },
    {
      dataIndex: 'option',
      valueType: 'option',
      width: 50,
      render: (dom, record) => {
        return (
          <>
            <Popconfirm
              className="cursor-pointer c-red"
              title={<>Are you sure you want to unlink?</>}
              okText="Yes"
              cancelText="No"
              overlayStyle={{ width: 300 }}
              onConfirm={async () => {
                const hide = message.loading('Unlinking the Email to selected tasks...', 0);
                unlinkEmailToTask(id, { task_ids: [record?.id] })
                  .then(() => {
                    message.success('Unlinked successfully.');
                    actionRef.current?.reload();
                    reloadParent?.();
                  })
                  .catch((e) => Util.error(e))
                  .finally(() => hide());
              }}
            >
              <LinkOutlined title="Unlink" />
            </Popconfirm>
          </>
        );
      },
    },
  ];

  useEffect(() => {
    if (sn(refreshTick)) {
      actionRef.current?.reload();
    }
  }, [refreshTick]);

  return (
    <>
      <ProTable<API.Task, API.PageParams>
        headerTitle={'Linked Tasks list'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: false, density: false, setting: false, search: false }}
        search={false}
        cardProps={{ bodyStyle: { padding: 0 } }}
        pagination={false}
        request={(params, sort, filter) =>
          getEmailTaskList({ ...params, email_id: id, with: 'taskBlocks' }, sort, filter)
        }
        columns={columns}
        rowSelection={false}
      />
    </>
  );
};

export default EmailTaskList;
