import type { Dispatch, SetStateAction } from 'react';
import { useEffect } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { deleteLoPayment, updateLoPayment } from '@/services/app/LexOffice/lo-payment';
import { Button, message, Popconfirm } from 'antd';
import Util, { sn } from '@/util';
import SDatePicker from '@/components/SDatePicker';
import SProFormDigit2 from '@/components/SProFormDigit2';
import useCustomerOrSupplier from './useCustomerOrSupplier';

export type FormValueType = API.LoPayment;

const handleUpdate = async (dataParam: FormValueType) => {
  const hide = message.loading('Creating...', 0);
  const data = { ...dataParam };
  try {
    await updateLoPayment(sn(data.id), data);
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type UpdateBalanceFormProps = {
  initialValues: API.LoPayment;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: FormValueType) => Promise<boolean | void>;

  customerOrSupplier?: API.Customer | API.Supplier;
};

const UpdateBalanceForm: React.FC<UpdateBalanceFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit, customerOrSupplier } = props;

  const { isSupplier, selectedId } = useCustomerOrSupplier(customerOrSupplier);

  useEffect(() => {
    if (modalVisible) {
      formRef.current?.setFieldsValue(props.initialValues);
    }
  }, [modalVisible, props.initialValues]);

  return (
    <ModalForm<FormValueType>
      title={'Update Balance' + (customerOrSupplier ? ` - ${customerOrSupplier.name}` : '')}
      width="700px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelCol={{ span: 8 }}
      labelAlign="left"
      formRef={formRef}
      initialValues={props.initialValues}
      onFinish={async (formValue) => {
        // Balance only available
        const data = { ...formValue, id: props.initialValues.id, type: 2 };
        if (isSupplier) {
          data.supplier_id = selectedId;
        } else {
          data.customer_id = selectedId;
        }

        const success = await handleUpdate(data);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(formValue);
        }
      }}
      submitter={{
        render(__) {
          return [
            <Popconfirm
              key="delete"
              className="c-red"
              title={<>Are you sure you want to delete?</>}
              okText="Yes"
              cancelText="No"
              overlayStyle={{ width: 300 }}
              onConfirm={async () => {
                deleteLoPayment(props.initialValues.id)
                  .then(() => {
                    onSubmit?.({});
                    message.success('Deleted successfully.');
                  })
                  .catch((reason) => Util.error(reason));
              }}
            >
              <Button type="primary" ghost danger style={{ marginRight: 48 }}>
                Delete
              </Button>
            </Popconfirm>,
            <Button
              key="cancel"
              onClick={() => {
                props.handleModalVisible(false);
              }}
            >
              Cancel
            </Button>,
            <Button
              key="submit"
              type="primary"
              onClick={() => {
                formRef.current?.submit();
              }}
            >
              OK
            </Button>,
          ];
        },
      }}
    >
      <SDatePicker
        name="date"
        width={120}
        label="Date"
        rules={[
          {
            required: true,
            message: 'Date is required',
          },
        ]}
      />
      <ProFormText name="actor" label="Empfänger / Auftraggeber" />
      <SProFormDigit2
        name="amount"
        label="Betrag"
        rules={[
          {
            required: true,
            message: 'Amount is required',
          },
        ]}
        fieldProps={{ precision: 3 }}
        width={150}
        zeroShow
      />
      <ProFormTextArea fieldProps={{ rows: 3 }} name="note" label="Verwendungszweck" />
    </ModalForm>
  );
};

export default UpdateBalanceForm;
