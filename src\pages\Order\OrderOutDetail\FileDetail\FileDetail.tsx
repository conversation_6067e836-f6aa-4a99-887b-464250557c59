import { FileCategory } from '@/constants';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormUploadDragger } from '@ant-design/pro-form';
import { But<PERSON>, Col, Divider, message, Modal, Row } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { useRef } from 'react';
import _ from 'lodash';
import type { UploadFile } from 'antd/lib/upload/interface';
import { getOrderOut, uploadOrderOutDetailFile } from '@/services/app/Order/order-out';
import Util from '@/util';
import { deleteFile } from '@/services/app/file';
import XlsFilePreviewModal from './XlsFilePreviewModal';
import ProDescriptions from '@ant-design/pro-descriptions';
import OrderOutFileDataList from './OrderOutFileDataList';
import useXlsFileLocal, { AggregationColumns } from './useXlsFileLocal';

type FileDetailProps = {
  orderNo: number;
};

const FileDetail: React.FC<FileDetailProps> = (props) => {
  const { orderNo } = props;

  const formRef = useRef<ProFormInstance>();

  const [orderOut, setOrderOut] = useState<API.OrderOut>({});
  // header definition modal
  const [openPreviewModal, setOpenPreviewModal] = useState<boolean>(false);
  const [openDetailListModal, setOpenDetailListModal] = useState<boolean>(false);

  // custom hooks
  const { dbField2NamesList, aggregation } = useXlsFileLocal(orderOut);

  const loadOrderOut = useCallback(() => {
    getOrderOut(orderNo, { with: 'detailFile,detailFileSetting' })
      .then((res) => {
        setOrderOut(res);
        formRef.current?.setFieldValue('files', res && res.detail_file ? [res.detail_file] : []);
      })
      .catch((e) => Util.error(e));
  }, [orderNo]);

  useEffect(() => {
    loadOrderOut();
  }, [loadOrderOut]);

  useEffect(() => {
    if (orderNo) {
    } else {
      formRef.current?.setFieldValue('files', []);
    }
  }, [orderNo]);

  /* const xlsCol2FieldId = useMemo(() => {
    const newMap = {};
    for (const xlsCol in fileSettings?.col2field) {
      newMap[xlsCol] = fileSettings?.col2field?.[xlsCol];
    }
    return newMap;
  }, [fileSettings?.col2field]); */

  const { confirm } = Modal;

  return (
    <>
      <ProForm<{ files?: (UploadFile & { tmp?: boolean })[] }>
        formRef={formRef}
        layout="horizontal"
        labelCol={{ span: 14 }}
        labelAlign="left"
        onValuesChange={async (changedValues) => {
          if ('files' in changedValues && changedValues.files.length) {
            const lastFile = changedValues.files[changedValues.files.length - 1];
            if (lastFile.originFileObj) {
              const formData = new FormData();
              formData.set('order_no', `${orderOut.order_no}`);
              formData.set('file', lastFile.originFileObj);
              formData.set('category', FileCategory.CAT_ORDER_OUT_DETAIL_FILE);

              const hide = message.loading('Uploading...', 0);
              uploadOrderOutDetailFile(formData)
                .then((res) => {
                  message.success('Uploaded successfully.');
                  console.log(res);
                  formRef.current?.setFieldValue('files', [res]);
                  setOrderOut((prev) => ({ ...prev, detail_file_id: res.id, detail_file: res }));
                  setOpenPreviewModal(true);
                })
                .catch((e) => Util.error(e))
                .finally(() => hide());
            }
          }
        }}
        submitter={false}
      >
        <Row gutter={24} wrap={false}>
          <Col flex="0 0 250px">
            <ProFormUploadDragger
              name="files"
              label=""
              title="Select File"
              description="Select or drag & drop XLS file here."
              accept=".xls,.xlsx"
              required
              icon={false}
              formItemProps={{ style: { marginBottom: 0 } }}
              fieldProps={{
                iconRender: undefined,
                beforeUpload: async () => {
                  return false;
                },
                // style: { marginBottom: 24 },
                onRemove: async (file: API.File) => {
                  return new Promise((resolve, reject) => {
                    confirm({
                      title: 'Are you sure you want to delete?',
                      onOk: async () => {
                        if (file.id) {
                          await deleteFile(file.id).catch((e) => Util.error(e));
                          setOrderOut((prev) => ({
                            ...prev,
                            detail_file: null,
                            detail_file_id: undefined,
                          }));
                        }
                        resolve(true);

                        formRef.current?.setFieldValue('files', []);
                        return true;
                      },
                      onCancel: () => {
                        reject(true);
                      },
                    });
                  });
                },
              }}
            />
          </Col>
          {orderOut.detail_file && (
            <>
              <Col>
                <Button
                  type="primary"
                  ghost
                  onClick={() => {
                    setOpenPreviewModal(true);
                  }}
                >
                  Preview & Setting
                </Button>
                <Button
                  type="primary"
                  onClick={() => setOpenDetailListModal(true)}
                  style={{ marginTop: 16 }}
                >
                  Show list
                </Button>
                <Button
                  type="default"
                  onClick={() => {
                    const fileId = orderOut.detail_file.id;
                    if (!fileId) return;
                    confirm({
                      title: 'Are you sure you want to delete?',
                      onOk: async () => {
                        await deleteFile(fileId, {
                          params: { deleteData: 1, orderNo: orderOut.order_no },
                        }).catch((e) => Util.error(e));
                        message.success('Deleted successfully.');
                        setOrderOut((prev) => ({
                          ...prev,
                          detail_file: null,
                          detail_file_id: undefined,
                        }));

                        formRef.current?.setFieldValue('files', []);
                        return true;
                      },
                      onCancel: () => {},
                    });
                  }}
                  style={{ marginTop: 16 }}
                  title="Delete file & imported data"
                  danger
                >
                  Delet all
                </Button>

                {/* <ProDescriptions style={{ marginTop: 16, width: 150 }} size="small" column={1}>
                  <ProDescriptions.Item label="Header in Row">
                    {fileSettings.headerRowNo}
                  </ProDescriptions.Item>
                  <ProDescriptions.Item label="Data Start Row">
                    {fileSettings.dataStartRowNo}
                  </ProDescriptions.Item>
                  <ProDescriptions.Item label="Data Last Row">
                    {fileSettings.xlsMeta?.highestRow}
                  </ProDescriptions.Item>
                </ProDescriptions> */}
              </Col>
              <Col flex="auto">
                <ProDescriptions column={3} size="small">
                  {AggregationColumns.map((column) => {
                    const fieldDef = _.find(dbField2NamesList, { db_field: column });
                    return fieldDef ? (
                      <ProDescriptions.Item
                        key={column}
                        labelStyle={{ width: 100 }}
                        label={fieldDef.db_field_comment ?? fieldDef.db_field}
                      >
                        <b>{Util.numberFormat((aggregation as any)?.[column], false, 2, true)}</b>
                      </ProDescriptions.Item>
                    ) : undefined;
                  })}

                  {/* {Object.keys(xlsCol2FieldId)?.map((xlsCol) => {
                    const fieldDef = _.find(dbField2NamesList, { id: xlsCol2FieldId[xlsCol] });

                    return fieldDef ? (
                      <ProDescriptions.Item
                        key={xlsCol}
                        label={fieldDef.db_field_comment ?? fieldDef.db_field}
                      >
                        <Tag>{xlsCol}</Tag>
                      </ProDescriptions.Item>
                    ) : undefined;
                  })} */}
                </ProDescriptions>
              </Col>
            </>
          )}
        </Row>
      </ProForm>
      {orderOut.detail_file && orderOut.order_no && (
        <>
          <Divider />
          {orderOut.detail_file && orderOut.order_no && (
            <OrderOutFileDataList orderOut={orderOut} hidePageContainer />
          )}
        </>
      )}
      {orderOut.detail_file && openPreviewModal && (
        <XlsFilePreviewModal
          orderOut={orderOut}
          setOrderOut={setOrderOut}
          dbField2NamesList={dbField2NamesList}
          modalVisible={openPreviewModal}
          handleModalVisible={setOpenPreviewModal}
        />
      )}

      <Modal
        title={'Detail list'}
        width="90%"
        open={openDetailListModal}
        onCancel={() => setOpenDetailListModal(false)}
        footer={false}
      >
        {orderOut.detail_file && orderOut.order_no && (
          <OrderOutFileDataList orderOut={orderOut} hidePageContainer />
        )}
      </Modal>
    </>
  );
};

export default FileDetail;
