/* eslint-disable */
import { request } from 'umi';

const urlPrefix = '/api/order/order-out-sup-workflow-step';

/**  
 * 
 * PUT /api/order/order-out-sup-workflow-step 
 */
export async function updateOrderOutSupWorkflowStep(
  data: API.OrderOutSupWorkflowStep & {
    workflow_id?: number;
    supplier_id?: number;
    step_info?: any;
    single_update?: boolean;
  },
  options?: { [key: string]: any },
) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

