/* eslint-disable @typescript-eslint/dot-notation */
import {
  createAssigners,
  deleteAssigners,
  deleteTask,
  getTask,
  moveTaskTo,
  sendToMasterGroup,
  updateTaskOrders,
  updateTaskPartial,
} from '@/services/app/Task/task';
import Util, { sn } from '@/util';
import {
  BookOutlined,
  EyeFilled,
  EyeInvisibleFilled,
  SearchOutlined,
  SwitcherOutlined,
} from '@ant-design/icons';
import type { ProLayoutProps } from '@ant-design/pro-layout';
import { PageContainer } from '@ant-design/pro-layout';
import _, { debounce, isNumber } from 'lodash';
import { Button, Col, message, Modal, Row, Space, Typography } from 'antd';
import { useCallback, useRef, useState } from 'react';
import type { DropResult, ResponderProvided, DraggableLocation } from 'react-beautiful-dnd';
import { DragDropContext } from 'react-beautiful-dnd';
import CreateForm from './components/CreateForm';
import UpdateForm from './components/UpdateForm';
import style from './index.less';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { useEffect } from 'react';
import { useModel } from 'umi';
import {
  addSubTaskGroup,
  getTaskGroup,
  getTaskGroupACList,
  updateTaskGroup,
} from '@/services/app/Task/task-group';
import { getSysNotificationList } from '@/services/app/Sys/notification';
import {
  DictType,
  TaskBlockLayout,
  TaskGroupLayout,
  TaskGroupStatus,
  TaskVisible,
  UpdateTaskLayout,
} from '@/constants';
import { addTaskBlock } from '@/services/app/Task/task-block';
import CreateHeadForm from '@/pages/Order/OrderOutHead/components/CreateForm';
import ViewEmail from '../Email/EmailList/components/ViewEmail';
import Tag from 'antd/lib/tag';
import TaskGroupLayoutDefault from './components/TaskGroupLayout/TaskGroupLayoutDefault';
import TaskGroupLayout2 from './components/TaskGroupLayout/TaskGroupLayout2';
import UpdateFormB from './components/UpdateFormB';
import TaskGroupLayout3 from './components/TaskGroupLayout/TaskGroupLayout3';

export type SearchFormValueType = Partial<API.Task>;

export enum TaskClickAction {
  update = 'update',
  updatePartial = 'updatePartial',
  delete = 'delete',
  invisible = 'invisible',
  visible = 'visible',
  createSubCallback = 'create-sub-callback',

  openSubGroup = 'open-sub-group',
  createSubGroup = 'create-sub-group',
  reopenSubGroup = 'reopenSubGroup',
  hideSubGroup = 'hideSubGroup',

  groupAssignmentCallback = 'groupAssignmentCallback',
  orderOutHeadAssignmentCallback = 'orderOutHeadAssignmentCallback', // Assign to orderOutHead group
  openEmailModal = 'openEmailModal', // open Email Viewer modal
  linkingEmailCallback = 'linkingEmailCallback', // linking callback
  unlinkingEmail = 'unlinkingEmail', // unlinking

  addUser = 'addUser',
  deleteUser = 'deleteUser',
  moveTo = 'moveTo',
  sendToSub = 'sendToSub', // no action processing here
  sendToSubCallback = 'sendToSubCallback',
  sendToMaster = 'sendToMaster',
}

export type onTaskClickType = (
  task: API.Task,
  droppableId?: string, // block ID, but it's string.
  index?: number,
  e?: any,
  actionMode?: TaskClickAction,
  options?: any,
) => Promise<void>;

export type onTaskPanelClickType = (actionMode: 'create', blockId?: number, options?: any) => void;

const reorder = (list: any, startIndex: any, endIndex: any): any => {
  const result = Array.from(list);
  const [removed] = result.splice(startIndex, 1);
  result.splice(endIndex, 0, removed);

  return result;
};

/**
 * Moves an item from one list to another list.
 */
const move = (
  source: any,
  destination: any,
  droppableSource: DraggableLocation,
  droppableDestination: DraggableLocation,
): any => {
  const sourceClone = Array.from(source);
  const destClone = Array.from(destination);
  const [removed] = sourceClone.splice(droppableSource.index, 1);

  destClone.splice(droppableDestination.index, 0, removed);

  const result: any = {};
  result[droppableSource.droppableId] = sourceClone;
  result[droppableDestination.droppableId] = destClone;

  return result;
};

type TaskBoardProps = {
  group_id?: number;
  parent_block_id?: number;
  refreshTick?: number;
};

const TaskBoard: React.FC<TaskBoardProps & ProLayoutProps> = (props) => {
  const {
    taskGroups,
    setTaskGroups,
    filters,
    setFilters,
    getTaskGroupInModel,
    replaceTaskGroup,
    saveUserTaskFilters,
  } = useModel('task-group');

  const { appSettings, getDictOptionsByType } = useModel('app-settings');
  const { taskContext } = useModel('task');

  const groupIdProp = props.group_id; // indicates if this board is sub board in a task
  const groupIdInUrl = props?.match?.params?.id ?? null;

  const [tasks, setTasks] = useState<Record<string, API.Task[]>>({});

  // Current groupId (board ID)
  const [groupId, setGroupId] = useState<number | undefined>(
    sn(Util.lsGet('[taskBoard][groupId]'), undefined),
  );

  const [blockId, setBlockId] = useState<number>();
  const [blocksMap, setBlocksMap] = useState<Record<number, API.TaskBlock[]>>({});

  // layout related
  const [visible6, setVisible6] = useState(false); // show or hide block #6

  // Create/Update form
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [createHeadModalVisible, handleCreateHeadModalVisible] = useState<boolean>(false);

  // email view
  const [openMailModal, setOpenMailModal] = useState<boolean>(false);
  const [selectedTask, setSelectedTask] = useState<API.Task>();

  const [selectedDropInfo, setSelectedDropInfo] = useState<{
    droppableId?: string;
    index?: number;
  }>({});

  // sys notification for board
  const [sysAlerts, setSysAlerts] = useState<API.SysNotification[]>([]);

  // Search form
  const searchFormRef = useRef<ProFormInstance>();

  // sub task group
  const [visibleSubGroup, setVisibleSubGroup] = useState<boolean>(false);
  const [subGroup, setSubGroup] = useState<API.TaskGroup>();
  const [subGroupBlockId, setSubGroupBlockId] = useState<number>();
  const [refreshTickSubGroup, setRefreshTickSubGroup] = useState<number>(0);

  const updateTask = (task?: API.Task, droppableId?: string, index?: number) => {
    setTasks((prev) => {
      const newTasks = { ...prev };
      _.set(newTasks, `[${droppableId}][${index}]`, task);
      return newTasks;
    });
  };

  const removeTaskInPanel = (task?: API.Task, droppableId?: string) => {
    if (!droppableId || !task) return;
    setTasks((prev) => {
      const newTasks = { ...prev };
      newTasks[droppableId] = (newTasks[droppableId] || []).filter((x) => x.id != task?.id) as any;
      return newTasks;
    });
  };

  const onTaskPanelClick = useCallback<onTaskPanelClickType>((action, blockIdParam) => {
    setBlockId(blockIdParam);
    switch (action) {
      case 'create':
        handleModalVisible(true);
        break;
    }
  }, []);

  useEffect(() => {
    if (!groupIdProp) {
      getTaskGroupACList({ with: 'taskBlocks' })
        .then((res) => {
          setTaskGroups(res);
        })
        .catch(() => Util.error('Failed to load task groups. Please try again'));
    }
    if (groupIdProp) {
      console.log('groupId prop change: ', groupIdProp);
      setGroupId(groupIdProp);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [groupIdProp]);

  useEffect(() => {
    if (groupIdInUrl) {
      setGroupId(sn(groupIdInUrl));
    }
  }, [groupIdInUrl]);

  useEffect(() => {
    if (groupId) {
      getSysNotificationList(
        { type: 'board', ref_id: groupId, is_date_valid: 1 },
        { updated_on: 'descend' },
      ).then((res) => setSysAlerts(res.data));
    }
  }, [groupId]);

  /**
   * Getting blocks map by position & updating taskGroups in global scope
   */
  useEffect(() => {
    if (!groupId || !taskGroups) {
      setBlocksMap({});
    } else {
      const tmpGroup: API.TaskGroup | undefined = _.find(taskGroups, { id: groupId });
      let arr: API.TaskBlock[] = _.get(tmpGroup, 'task_blocks', []);
      const obj: any = {};
      if (!tmpGroup && !arr.length) {
        (async () => {
          const res = await getTaskGroup({ id: groupId, with: 'taskBlocks' }, {});
          replaceTaskGroup(res);
          arr = res.task_blocks || [];
          for (const block of arr) {
            const position = block.position || 0;
            if (!obj[position]) obj[position] = [];
            obj[position].push(block);
          }
          setBlocksMap(obj);
        })();
      } else {
        for (const block of arr) {
          const position = block.position || 0;
          if (!obj[position]) obj[position] = [];
          obj[position].push(block);
        }
        setBlocksMap(obj);
      }
    }
  }, [groupId, replaceTaskGroup, taskGroups]);

  const debouncedSearch = useRef(
    debounce(async (keyword) => {
      setFilters((prev) => ({ ...prev, keyword }));
    }, 300),
  ).current;

  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  const debouncedSearchHead = useRef(
    debounce(async (keyword) => {
      setFilters((prev) => ({ ...(prev ?? {}), keywordHead: keyword }));
    }, 300),
  ).current;

  useEffect(() => {
    return () => {
      debouncedSearchHead.cancel();
    };
  }, [debouncedSearchHead]);

  const groupInfo = getTaskGroupInModel(groupId);
  const isOrderOutGroup = groupInfo?.code == 'OrderOut';
  const isOrderOutHeadGroup = groupInfo?.code == 'OrderOutHead';
  const useUserInitials = groupInfo?.settings?.useInitials;

  useEffect(() => {
    if (isOrderOutHeadGroup) {
      setFilters((prev) => ({ ...prev, keywordHead: Util.lsGet('[orderOutHeadKeyword]') }));
    } else {
      setFilters((prev) => ({ ...prev, keywordHead: undefined }));
    }
  }, [isOrderOutHeadGroup, setFilters]);

  async function onDragEnd(result: DropResult, provided: ResponderProvided) {
    const { source, destination } = result;

    // dropped outside the list
    if (!destination) {
      return;
    }
    const sInd = +source.droppableId;
    const dInd = +destination.droppableId;

    if (isOrderOutGroup) {
      if (sInd == dInd) return;
    }
    // console.log(source, destination, result, provided);
    // console.log(provided);

    const prevTasks = { ...tasks };
    // const hide = message.loading('Updating...', 0);
    updateTaskOrders(
      tasks?.[source.droppableId]?.[source.index]?.id,
      tasks?.[destination.droppableId]?.[destination.index]?.id,
      result,
      groupId,
    )
      .then((res) => {
        console.log('Drop END complete:', res);
        if (isOrderOutGroup) {
          if (sInd != dInd) {
            taskContext.reloadLists?.[`${dInd}`]?.();
          }
        }
      })
      .catch((reason) => {
        Util.error(reason);
        setTasks(prevTasks);
      })
      .finally(() => {});

    if (sInd === dInd) {
      const items = reorder(tasks[sInd], source.index, destination.index);
      const newState: Record<string, API.Task[]> = { ...tasks };
      newState[sInd] = items;
      setTasks(newState);
    } else {
      const result2 = move(tasks[sInd], tasks[dInd], source, destination);
      const newState: Record<string, API.Task[]> = { ...tasks };
      newState[sInd] = result2[sInd];
      newState[dInd] = result2[dInd];
      if (newState[dInd]?.[destination.index]) {
        newState[dInd][destination.index] = {
          ...newState[dInd]?.[destination.index],
          block_id: dInd,
        };
      }
      setTasks(newState);
    }
  }

  const onTaskClick = useCallback<onTaskClickType>(
    async (
      task: API.Task,
      dropPanelId?: string,
      index?: number,
      e?: any,
      actionMode?: TaskClickAction,
      options?: any,
    ) => {
      console.log(task, index, 'block:', dropPanelId, actionMode, e, options);
      if (actionMode == TaskClickAction.delete) {
        if (!task.id || !(isNumber(index) && index >= 0)) return Promise.resolve();
        return deleteTask({ id: task.id })
          .then(() => {
            setTasks((prev) => {
              const newTasks = { ...prev };
              (newTasks[`${dropPanelId}`] || []).splice(index, 1);
              return newTasks;
            });
          })
          .catch((reason) => Util.error(reason));
      } else if (actionMode == TaskClickAction.hideSubGroup) {
        if (!task.id || !(isNumber(index) && index >= 0)) return Promise.resolve();
        const sgId = task.sub_task_groups?.[0]?.id;
        if (!sgId) {
          message.warning('Invalid request. No subgrapu ID is provided');
          return Promise.resolve();
        }

        return updateTaskGroup({
          id: sgId,
          status: TaskGroupStatus.HIDDEN,
          _action: 'partialUpdate',
        })
          .then(async () => {
            return getTask(task.id, { with: 'taskBlocks', block_id: dropPanelId }).then(
              (resTask) => {
                updateTask(resTask, dropPanelId, index);
              },
            );
          })
          .catch((reason) => Util.error(reason));
      } else if (actionMode == TaskClickAction.createSubCallback) {
        return getTask(task.id, { with: 'taskBlocks', block_id: dropPanelId }).then((resTask) => {
          updateTask(resTask, dropPanelId, index);
        });
      } else if (actionMode == TaskClickAction.invisible) {
        const hide = message.loading('Updating task...', 0);
        return updateTaskPartial({
          id: task.id,
          block_id: task.block_id, // important
          visible: TaskVisible.INVISIBLE,
        })
          .then(() => {
            removeTaskInPanel({ ...task, ...(options || {}) }, dropPanelId);
          })
          .finally(() => hide());
      } else if (actionMode == TaskClickAction.visible) {
        const hide = message.loading('Updating task...', 0);
        return updateTaskPartial({
          id: task.id,
          block_id: task.block_id, // important
          visible: TaskVisible.VISIBLE,
        })
          .then(() => {
            removeTaskInPanel({ ...task, ...(options || {}) }, dropPanelId);
          })
          .finally(() => hide());
      } else if (actionMode == TaskClickAction.updatePartial) {
        const hide = message.loading('Updating task...', 0);
        return updateTaskPartial({ id: task.id, ...(options || {}) })
          .then(() => {
            updateTask({ ...task, ...(options || {}) }, dropPanelId, index);
          })
          .finally(() => hide());
      } else if (actionMode == TaskClickAction.openSubGroup) {
        setSubGroup(task.sub_task_groups?.[0]);
        setSubGroupBlockId(sn(dropPanelId));
        setVisibleSubGroup(true);
        return Promise.resolve();
      } else if (actionMode == TaskClickAction.reopenSubGroup) {
        const sgId = task.sub_task_groups?.[0]?.id;
        if (!sgId) {
          message.warning('Invalid request. No subgrapu ID is provided');
          return Promise.resolve();
        }
        const hide = message.loading('Re-openning sub task group...', 0);
        return updateTaskGroup({
          id: sgId,
          status: TaskGroupStatus.ACTIVE,
          _action: 'partialUpdate',
        })
          .then(async () => {
            hide();
            setSubGroup({
              ...task.sub_task_groups?.[0],
              id: sgId, // for typescript.
              status: TaskGroupStatus.ACTIVE,
            });
            setSubGroupBlockId(sn(dropPanelId));
            setVisibleSubGroup(true);
            return getTask(task.id, { with: 'taskBlocks', block_id: dropPanelId }).then(
              (resTask) => {
                updateTask(resTask, dropPanelId, index);
              },
            );
          })
          .catch((reason) => {
            Util.error(reason);
            hide();
          });
      } else if (actionMode == TaskClickAction.openEmailModal) {
        setSelectedTask(task);
        setSelectedDropInfo({ droppableId: dropPanelId, index });
        setOpenMailModal(true);
      } else if (actionMode == TaskClickAction.unlinkingEmail) {
        setTasks((prev) => {
          const newTasks = { ...prev };
          _.set(newTasks, `[${dropPanelId}][${index}]`, {
            ...task,
            email_ref: null,
          });
          return newTasks;
        });
      } else if (actionMode == TaskClickAction.linkingEmailCallback) {
        setTasks((prev) => {
          const newTasks = { ...prev };
          _.set(newTasks, `[${dropPanelId}][${index}]`, {
            ...task,
            email_ref: options,
          });
          return newTasks;
        });
      } else if (actionMode == TaskClickAction.orderOutHeadAssignmentCallback) {
        return getTask(task.id, { with: 'taskBlocks', block_id: dropPanelId })
          .then((resTask) => {
            updateTask(resTask, dropPanelId, index);
          })
          .catch((error) => Util.error(error));
      } else if (actionMode == TaskClickAction.moveTo) {
        const hide = message.loading('Moving the task...', 0);
        return moveTaskTo(task.id, {
          ...options,
          srcBlockId: dropPanelId,
        })
          .then((res) => {
            // we need to reload because sorting should be changed
            taskContext?.reloadLists?.[`${dropPanelId}`]?.();
          })
          .finally(() => hide());
      } else if (actionMode == TaskClickAction.sendToMaster) {
        const hide = message.loading('Moving the task into master board...', 0);
        return sendToMasterGroup(
          options.srcTaskId,
          options.destTaskId,
          sn(dropPanelId),
          sn(props.parent_block_id),
        )
          .then((res) => {
            // we need to reload because sorting should be changed
            taskContext?.reloadLists?.[`${dropPanelId}`]?.();
            taskContext?.reloadLists?.[`${props.parent_block_id}`]?.();
          })
          .catch((err) => Util.error(err))
          .finally(() => hide());
      } else if (actionMode == TaskClickAction.sendToSubCallback) {
        // Force reload for time being
        taskContext?.reloadLists?.[`${dropPanelId}`]?.();
        setRefreshTickSubGroup((prev) => prev + 1);
      } else if (actionMode == TaskClickAction.groupAssignmentCallback) {
        // console.log('received blockIds: ', options.block_id, task.block_id, options.old_block_id);
        if ((options.block_ids || [])?.find((x: number) => x == task.block_id)) {
          return getTask(task.id, { with: 'taskBlocks', block_id: dropPanelId })
            .then((resTask) => {
              updateTask(resTask, dropPanelId, index);
            })
            .catch((error) => Util.error(error));
        } else {
          removeTaskInPanel({ ...task }, dropPanelId);
          return Promise.resolve();
        }
      } else if (actionMode == TaskClickAction.createSubGroup) {
        const hide = message.loading('Creating a sub task panel...', 0);
        const res = await addSubTaskGroup({ task_id: task.id }).finally(() => hide());
        setSubGroup(res);
        setVisibleSubGroup(true);
        return getTask(task.id, { with: 'taskBlocks', block_id: dropPanelId }).then((resTask) => {
          updateTask(resTask, dropPanelId, index);
        });
      } else if (actionMode == TaskClickAction.deleteUser) {
        const hide = message.loading('Assigning a user into task...', 0);
        return deleteAssigners(task.id, options.userIds)
          .then((res) => {
            if (isOrderOutGroup) {
              // we need to reload because sorting should be changed
              taskContext?.reloadLists?.[`${dropPanelId}`]?.();
            } else updateTask({ ...task, users: res.users }, dropPanelId, index);
          })
          .finally(() => hide());
      } else if (actionMode == TaskClickAction.addUser) {
        const hide = message.loading('Assigning a user into task...', 0);
        return createAssigners(task.id, options.userIds)
          .then((res) => {
            if (isOrderOutGroup) {
              // we need to reload because sorting should be changed
              taskContext?.reloadLists?.[`${dropPanelId}`]?.();
            } else updateTask({ ...task, users: res.users }, dropPanelId, index);
          })
          .finally(() => hide());
      } else {
        setSelectedDropInfo({ droppableId: dropPanelId, index });
        handleUpdateModalVisible(true);
        return Promise.resolve();
      }
    },
    [isOrderOutGroup, props.parent_block_id, taskContext?.reloadLists],
  );

  const renderTaskGroupLayout = () => {
    if (groupInfo?.settings?.layout == TaskGroupLayout.LAYOUT_2) {
      return (
        <TaskGroupLayout2
          blocksMap={blocksMap}
          group={groupInfo as any}
          onTaskClick={onTaskClick}
          onTaskPanelClick={onTaskPanelClick}
          searchFormRef={searchFormRef}
          tasks={tasks}
          setTasks={setTasks}
          options={{ visible6 }}
          parentGroupId={sn(groupIdProp)}
          parentBlockId={sn(props.parent_block_id)}
        />
      );
    } else if (groupInfo?.settings?.layout == TaskGroupLayout.LAYOUT_3) {
      return (
        <TaskGroupLayout3
          blocksMap={blocksMap}
          group={groupInfo as any}
          onTaskClick={onTaskClick}
          onTaskPanelClick={onTaskPanelClick}
          searchFormRef={searchFormRef}
          tasks={tasks}
          setTasks={setTasks}
          options={{ visible6 }}
          parentGroupId={sn(groupIdProp)}
          parentBlockId={sn(props.parent_block_id)}
        />
      );
    }

    return (
      <TaskGroupLayoutDefault
        blocksMap={blocksMap}
        group={groupInfo as any}
        onTaskClick={onTaskClick}
        onTaskPanelClick={onTaskPanelClick}
        searchFormRef={searchFormRef}
        tasks={tasks}
        setTasks={setTasks}
        options={{ visible6 }}
        parentGroupId={sn(groupIdProp)}
        parentBlockId={sn(props.parent_block_id)}
      />
    );
  };

  useEffect(() => {
    const bid = groupInfo?.task_blocks?.[0]?.id;
    if (props?.refreshTick && bid) {
      taskContext.reloadLists?.[`${bid}`]?.();
    }
  }, [groupInfo?.task_blocks, props?.refreshTick, taskContext.reloadLists]);

  return (
    <PageContainer
      className={`${style.container} ${groupIdProp ? 'hideHeader' : ''}`}
      title={
        groupIdProp ? undefined : (
          <Space size={16} style={{ lineHeight: 0 }}>
            <div>Task Board - {_.get(groupInfo, 'name')}</div>
            {isOrderOutHeadGroup && (
              <>
                <Button
                  icon={<BookOutlined />}
                  type="primary"
                  title="Create a new Order Out Head"
                  onClick={() => handleCreateHeadModalVisible(true)}
                >
                  New
                </Button>
                <ProFormText
                  name="keywordHead"
                  width="xs"
                  formItemProps={{ style: { marginBottom: 0 } }}
                  placeholder="Search Head..."
                  fieldProps={{
                    prefix: <SearchOutlined />,
                    defaultValue: Util.lsGet('[orderOutHeadKeyword]'), //filters['keywordHead'],
                    onChange: (e) => {
                      const keyword = e.target.value;
                      Util.lsUpdate('[orderOutHeadKeyword]', keyword);
                      debouncedSearchHead(keyword);
                    },
                  }}
                />
              </>
            )}
            <ProFormSelect
              name="visible"
              placeholder="Select status"
              width={'xs'}
              initialValue={TaskVisible.VISIBLE}
              options={[
                { value: TaskVisible.VISIBLE, label: 'Visible' },
                { value: TaskVisible.INVISIBLE, label: 'Invisible' },
              ]}
              formItemProps={{ style: { marginBottom: 0 } }}
              fieldProps={{
                onChange: (value) => {
                  setFilters((prev) => ({ ...prev, visible: value }));
                },
              }}
            />
            <ProFormText
              name="keyword"
              width="sm"
              formItemProps={{ style: { marginBottom: 0 } }}
              placeholder="Search..."
              fieldProps={{
                prefix: <SearchOutlined />,
                onChange: (e) => {
                  const keyword = e.target.value;
                  debouncedSearch(keyword);
                },
              }}
            />
            <ProFormSelect
              name="category_code"
              formItemProps={{ style: { marginBottom: 0 } }}
              placeholder="Category"
              options={getDictOptionsByType(DictType.OrderOutCategory)}
              fieldProps={{
                onChange: (value) => {
                  setFilters((prev) => ({ ...prev, category_code: value }));
                },
              }}
            />

            {/* <ProFormSelect
              name="userIds"
              mode="multiple"
              formItemProps={{ style: { marginBottom: 0 } }}
              options={appSettings.users?.map((user) => ({
                value: user.user_id,
                label: user.initials,
              }))}
            /> */}
            {useUserInitials && appSettings.users && (
              <div style={{ maxWidth: 100, display: 'flex', flexWrap: 'wrap' }}>
                {appSettings.users?.map((user) => {
                  const oldUserIds = filters?.userIds ?? {};
                  const itrUserId = sn(user.user_id);
                  return (
                    <Tag
                      key={user.user_id}
                      className="ant-tag-small cursor-pointer"
                      style={{ marginBottom: 2 }}
                      color={oldUserIds[itrUserId] ? 'blue' : undefined}
                      onClick={(e) => {
                        setFilters((prev: any) => ({
                          ...prev,
                          userIds: { ...prev.userIds, [itrUserId]: !prev.userIds?.[itrUserId] },
                        }));
                        saveUserTaskFilters({
                          ...filters,
                          userIds: {
                            ...filters?.userIds,
                            [itrUserId]: !filters?.userIds?.[itrUserId],
                          },
                        });
                      }}
                    >
                      {user.initials ?? itrUserId}
                    </Tag>
                  );
                })}
              </div>
            )}
          </Space>
        )
      }
      extra={
        groupIdProp ? undefined : (
          <>
            <Row wrap={false} align="middle" gutter={32}>
              {sysAlerts?.[0]?.title && (
                <Col style={{ maxWidth: 700 }}>
                  <Typography.Text type="success" ellipsis title={sysAlerts?.[0]?.title}>
                    {sysAlerts?.[0]?.title}
                  </Typography.Text>
                </Col>
              )}
              {(!groupInfo?.settings?.layout ||
                groupInfo?.settings?.layout == TaskGroupLayout.LAYOUT_DEFAULT) && (
                <Col>
                  <Button
                    type="link"
                    icon={visible6 ? <EyeInvisibleFilled /> : <EyeFilled />}
                    title={visible6 ? 'Hide block #6' : 'Show block #6'}
                    onClick={() => setVisible6((prev) => !prev)}
                  />
                </Col>
              )}
            </Row>
          </>
        )
      }
    >
      {/* <Card style={{ marginBottom: 16, borderRadius: 5 }}>
        <ProForm<SearchFormValueType>
            layout="inline"
            formRef={searchFormRef}
            isKeyPressSubmit
            className="search-form"
            submitter={false}
            onFieldsChange={(changedFields, allFields) => {
              console.log('form fields changed', changedFields);
            }}
          >
      </Card> */}
      {groupIdProp && (
        <div className="absolute" style={{ right: 50, top: 10 }}>
          <Button
            type="link"
            icon={visible6 ? <EyeInvisibleFilled /> : <EyeFilled />}
            title={visible6 ? 'Hide block #6' : 'Show block #6'}
            onClick={() => {
              setVisible6((prev) => !prev);
              if (groupIdProp && !blocksMap[6]) {
                const hide = message.loading('Creating an empty block...', 0);
                addTaskBlock({
                  name: 'Block6',
                  position: 6,
                  layout: TaskBlockLayout.LAYOUT_B,
                  group_id: groupIdProp,
                })
                  .then((resBlock) => {
                    const currentGroup = getTaskGroupInModel(groupIdProp);

                    if (currentGroup) {
                      currentGroup?.task_blocks?.push(resBlock);
                      replaceTaskGroup(currentGroup);
                    }
                  })
                  .catch((reason) => Util.error(reason))
                  .finally(() => hide());
              }
            }}
          />
        </div>
      )}
      <DragDropContext
        onDragEnd={onDragEnd}
        /* onBeforeDragStart={(initial1) => {
          console.log('onBeforeDragStart', initial1);
        }}
        onDragStart={(initial2, provided) => {
          console.log('onDragStart', initial2, provided);
        }}
        onDragUpdate={(initial3, provided) => {
          console.log('onDragUpdate', initial3, provided);
        }} */
      >
        {renderTaskGroupLayout()}
      </DragDropContext>
      <CreateForm
        modalVisible={createModalVisible}
        groupId={groupId}
        blockId={blockId}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          setTasks((prev) => {
            const newTasks = { ...prev };
            (newTasks[`${value.block_id}`] || []).splice(0, 0, value);
            return newTasks;
          });
        }}
      />
      {(isOrderOutHeadGroup || isOrderOutGroup) && (
        <CreateHeadForm
          includeLatestList
          modalVisible={createHeadModalVisible}
          handleModalVisible={handleCreateHeadModalVisible}
          onSubmit={async (value) => {
            handleCreateHeadModalVisible(false);
          }}
        />
      )}

      {groupInfo?.settings?.updateTaskLayout == UpdateTaskLayout.LAYOUT_B ? (
        <UpdateFormB
          modalVisible={updateModalVisible}
          handleModalVisible={handleUpdateModalVisible}
          initialValues={{
            ...(_.get(
              tasks,
              `[${selectedDropInfo?.droppableId}][${selectedDropInfo?.index}]`,
              {},
            ) || {}),
            group_id: groupId,
            block_id: sn(selectedDropInfo?.droppableId),
            index: selectedDropInfo?.index,
          }}
          onSubmit={async (value) => {
            if (value && selectedDropInfo?.droppableId && (selectedDropInfo?.index ?? 0) >= 0) {
              setTasks((prev) => {
                const newTasks = { ...prev };
                _.set(
                  newTasks,
                  `[${selectedDropInfo?.droppableId}][${selectedDropInfo?.index}]`,
                  value,
                );
                return newTasks;
              });
            }
          }}
          onCancel={() => {
            handleUpdateModalVisible(false);
          }}
        />
      ) : (
        <UpdateForm
          modalVisible={updateModalVisible}
          handleModalVisible={handleUpdateModalVisible}
          initialValues={{
            ...(_.get(
              tasks,
              `[${selectedDropInfo?.droppableId}][${selectedDropInfo?.index}]`,
              {},
            ) || {}),
            group_id: groupId,
            block_id: sn(selectedDropInfo?.droppableId),
            index: selectedDropInfo?.index,
          }}
          onSubmit={async (value) => {
            if (value && selectedDropInfo?.droppableId && (selectedDropInfo?.index ?? 0) >= 0) {
              setTasks((prev) => {
                const newTasks = { ...prev };
                _.set(
                  newTasks,
                  `[${selectedDropInfo?.droppableId}][${selectedDropInfo?.index}]`,
                  value,
                );
                return newTasks;
              });
            }
          }}
          onCancel={() => {
            handleUpdateModalVisible(false);
          }}
        />
      )}

      <Modal
        title={
          <>
            <SwitcherOutlined className="c-blue4" /> Sub Task Board in #{subGroup?.task_id}
          </>
        }
        style={{ top: 20 }}
        bodyStyle={{ minHeight: 400, paddingTop: 0 }}
        width={'85%'}
        open={visibleSubGroup}
        footer={false}
        onCancel={() => {
          setVisibleSubGroup(false);
          setSubGroup(undefined);
          setSubGroupBlockId(undefined);
        }}
      >
        <TaskBoard
          group_id={subGroup?.id}
          parent_block_id={subGroupBlockId}
          refreshTick={refreshTickSubGroup}
        />
        {/* {visibleSubGroup && subGroup?.id && (
          <TaskBoard
            group_id={subGroup?.id}
            parent_block_id={subGroupBlockId}
            refreshTick={refreshTickSubGroup}
          />
        )} */}
      </Modal>
      <Modal
        width="50%"
        open={openMailModal}
        onCancel={() => setOpenMailModal(false)}
        footer={false}
      >
        <ViewEmail
          email={selectedTask?.email_ref}
          task_id={selectedTask?.id}
          unlinkCallback={() => {
            if (selectedDropInfo?.droppableId && (selectedDropInfo?.index ?? 0) >= 0) {
              setTasks((prev) => {
                const newTasks = { ...prev };
                _.set(newTasks, `[${selectedDropInfo?.droppableId}][${selectedDropInfo?.index}]`, {
                  ...selectedTask,
                  email_ref: null,
                });
                return newTasks;
              });
            }
          }}
        />
      </Modal>
    </PageContainer>
  );
};

export default TaskBoard;
