import type { Dispatch, SetStateAction } from 'react';
import { useState } from 'react';
import React from 'react';
import { ProFormSelect } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { getSysTextModuleACList } from '@/services/app/Sys/text-module';

export type FormValueType = Partial<API.SysTextModule>;

export type TextSelectFormProps = {
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit: (text: string) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const TextSelectForm: React.FC<TextSelectFormProps> = (props) => {
  const [text, setText] = useState<string>('');

  return (
    <ModalForm
      title={'Select a text by number'}
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 7 }}
      wrapperCol={{ span: 16 }}
      onFinish={async (value) => {
        props.onSubmit(text);
      }}
      draggable
    >
      <ProFormSelect
        required
        rules={[
          {
            required: true,
            message: 'Number is required',
          },
        ]}
        name="number"
        label="Number"
        mode="single"
        request={(params) => getSysTextModuleACList(params)}
        showSearch
        fieldProps={{
          onChange: (value, option: any) => {
            setText(option?.text ?? '');
          },
        }}
      />
    </ModalForm>
  );
};

export default TextSelectForm;
