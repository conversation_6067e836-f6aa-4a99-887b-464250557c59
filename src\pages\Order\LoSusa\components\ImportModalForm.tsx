import type { Dispatch, SetStateAction } from 'react';
import { useEffect } from 'react';
import { useState } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormUploadButton } from '@ant-design/pro-form';
import { ProFormRadio } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { checkExistenceData, importLoSusa } from '@/services/app/LexOffice/lo-susa';
import { Alert, message, Spin } from 'antd';
import Util from '@/util';
import SDatePicker from '@/components/SDatePicker';
import { CSVFileDelimiterOptions, TransactionType, TransactionTypeOptions } from '@/constants';
import type { RcFile, UploadFile } from 'antd/lib/upload';
import SProFormDigit2 from '@/components/SProFormDigit2';

export type FormValueType = {
  date?: string;
  category?: string;

  headerRowNo?: number;
  dataStartRowNo?: number;
  files?: UploadFile[];
};

const handleImport = async (dataParam: FormValueType) => {
  const data = new FormData();
  data.set('dataStartRowNo', `${dataParam.dataStartRowNo}`);
  data.set('headerRowNo', `${dataParam.headerRowNo}`);
  // data.set('category', `${dataParam.category}`);
  data.set('date', `${dataParam.date}`);
  if (dataParam.files) {
    data.append(`file`, dataParam.files[0].originFileObj as RcFile);
  }

  const hide = message.loading('Importing Susa file...', 0);
  return importLoSusa(data)
    .then((res) => {
      return res;
    })
    .catch((err) => Util.error(err))
    .finally(() => hide());
};

export type ImportModalFormProps = {
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: FormValueType) => Promise<boolean | void>;
};

const ImportModalForm: React.FC<ImportModalFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  const [loading, setLoading] = useState<boolean>(false);
  const [existStatus, setExistStatus] = useState<boolean | null>(null);

  const loadExistStatus = (values: FormValueType) => {
    setLoading(true);
    checkExistenceData(values)
      .then((res) => {
        setExistStatus(res);
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    if (modalVisible) {
      const cachedData = Util.getSfValues('f_lo_susa', {});
      formRef.current?.setFieldsValue(cachedData);
      loadExistStatus(cachedData);
    }
  }, [modalVisible]);

  return (
    <ModalForm<FormValueType>
      title={'Import SuSa data from XLS/CSV file'}
      formRef={formRef}
      width="700px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ flex: '150px' }}
      onValuesChange={(changed, values) => {
        if (changed.date && values.date) {
          Util.setSfValues('f_lo_susa', { date: values.date });
          loadExistStatus({ date: values.date });
        } else {
          setExistStatus(null);
        }
      }}
      onFinish={async (formValue) => {
        const success = await handleImport(formValue);
        if (success) {
          if (formRef.current) formRef.current.setFieldValue('files', null);
          if (onSubmit) await onSubmit(formValue);
        }
      }}
    >
      <SDatePicker
        name="date"
        label="Date"
        fieldProps={{ picker: 'month' }}
        rules={[
          {
            required: true,
            message: 'Date is required',
          },
        ]}
        addonAfter={<Alert message="Old data on this date will be removed." type="warning" />}
      />

      <Spin spinning={loading}>
        {existStatus !== null ? (
          existStatus ? (
            <Alert message="Data exists!" type="error" style={{ marginBottom: 16 }} />
          ) : (
            <Alert message="Data does not exists!" type="success" style={{ marginBottom: 16 }} />
          )
        ) : null}
      </Spin>

      {/* <ProFormRadio.Group
        label="Import Type"
        name="category"
        rules={[
          {
            required: true,
            message: 'Import type is required',
          },
        ]}
        radioType="button"
        fieldProps={{ buttonStyle: 'solid' }}
        options={TransactionTypeOptions}
        addonAfter={
          <Spin spinning={loading}>
            {existStatus !== null ? (
              existStatus ? (
                <div className="c-red">Data exists!</div>
              ) : (
                <div className="c-green">Data does not exists!</div>
              )
            ) : null}
          </Spin>
        }
      /> */}
      {/* <SProFormDigit2
        label="Header Row No"
        name="headerRowNo"
        width={100}
        tooltip="If 0 or empty specified, we use the first name in `XLS Columns` as an Excel Column (like A, B, C, etc)"
      /> */}
      <SProFormDigit2
        label="First Data Row No"
        name="dataStartRowNo"
        width={100}
        initialValue={8}
      />
      <ProFormRadio.Group
        label="CSV file delimiter"
        name="delimiter"
        fieldProps={{ buttonStyle: 'solid' }}
        options={CSVFileDelimiterOptions}
        initialValue=";"
      />
      <ProFormUploadButton
        max={1}
        name="files"
        label="File"
        title="Select File"
        accept=".xls,.xlsx,.csv"
        required
        rules={[
          {
            required: true,
            message: 'File is required',
          },
        ]}
        fieldProps={{
          beforeUpload: () => {
            return false;
          },
        }}
      />
    </ModalForm>
  );
};

export default ImportModalForm;
