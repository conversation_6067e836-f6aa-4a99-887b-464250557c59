import Util, { sn } from '@/util';
import { ProFormSelect } from '@ant-design/pro-form';
import type { ProFormSelectProps } from '@ant-design/pro-form/lib/components/Select';

const SProFormSelect = (
  props: ProFormSelectProps & {
    isNumericValue?: boolean;
    onTabKeyCallback?: (value?: any) => void;
  },
) => {
  return (
    <ProFormSelect
      {...props}
      fieldProps={{
        ...props.fieldProps,
        showSearch: true,
        allowClear: true,
        onInputKeyDown: (e: any) => {
          if (props.mode == 'multiple') return;
          if (Util.isTabPressed(e)) {
            const dropdownWrapper = document.getElementById(e.target.getAttribute('aria-controls'));
            if (dropdownWrapper?.children.length == 1) {
              const id = e.target.getAttribute('aria-activedescendant');
              let value = document.getElementById(id)?.innerText?.trim();
              if (props.isNumericValue) {
                value = sn(value);
              }
              if (props.fieldProps?.labelInValue) {
                // ant-select-item-option-active ant-select-item-option-selected
                const node = document.getElementById(id);
                let label = '';
                if (node) {
                  const active = node.parentNode?.parentNode?.querySelectorAll(
                    '.ant-select-item-option-active',
                  )?.[0];
                  if (active) {
                    label = active.getAttribute('label') ?? '';
                    // console.log('label: ', label);
                    // console.log('data-item: ', active.getAttribute('data-item') ?? null);
                  }
                }
                props.onTabKeyCallback?.({ value: value, label: label });
              } else {
                props.onTabKeyCallback?.(value);
              }
            }
          }
        },
      }}
    />
  );
};

export default SProFormSelect;
