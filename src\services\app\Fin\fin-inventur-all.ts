/* eslint-disable */
import { RequestConfig, request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/fin/inventurAll';

/** GET /api/fin/inventurAll 
 * 
 * Get fin inventur all list  
*/
export async function getFinInventurAllList(
    params: API.PageParams & Partial<API.FinIventurAll>,
    sort?: any,
    filter?: any,
): Promise<API.PaginatedResult<API.FinIventurAll>> {
    return request<API.BaseResult>(`${urlPrefix}`, {
        method: 'GET',
        params: {
            ...params,
            perPage: params.pageSize,
            page: params.current,
            sort,
            filter,
        },
        paramsSerializer,
        withToken: true,
    }).then((res) => ({
        data: res.message.data,
        success: res.status == 'success',
        total: res.message.pagination.totalRows,
    }));
}

/** GET /api/fin/inventurAll/{no} 
 * 
 * Get fin inventur all list  
*/
export async function getFinInventurAll(Artikelnummer: number): Promise<API.FinIventurAll> {
    return request<API.ResultObject<API.FinIventurAll>>(`${urlPrefix}/${Artikelnummer}`).then((res) => res.message);
}

/** 
 * POST /api/fin/import 
 * 
 */
export async function importFinInventurAll(
    data?: FormData,
    options?: { [key: string]: any },
) {
    const config: RequestConfig = {
        method: 'POST',
        ...(options || {}),
    };

    config.body = data;

    return request<any>(`${urlPrefix}/import`, config);
}