import { getSysWorkflowStepList } from '@/services/app/Sys/sys-workflow-step';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import { Col, Modal, Row, Spin } from 'antd';
import { isArray } from 'lodash';
import { useState, type Dispatch, type SetStateAction, useRef, useEffect } from 'react';
import OrderOutWorkflowStepLogList from '../../OrderOutWorkflowStepLog';
import { checkVisibleRule } from './WorkflowStepData';
import WorkflowStepDataItem from './WorkflowStepDataItem';
import Util, { sn } from '@/util';
import _ from 'lodash';

type WorkflowSectionProps = {
  workflow_id: number;
  section_id?: number;
  position?: number;
  order_no: number;
  orderOut?: Partial<API.OrderOut>;
  setRefreshTaskCommentsList?: Dispatch<SetStateAction<number>>;
};

const WorkflowSection: React.FC<WorkflowSectionProps> = (props) => {
  const { order_no, workflow_id, section_id, orderOut, position } = props;

  const [refreshTick, setRefreshTick] = useState<number>(0); // for refreshing history table.
  const [loading, setLoading] = useState<boolean>(false);
  const [steps, setSteps] = useState<API.SysWorkflowStep[]>([]);
  const formRef = useRef<ProFormInstance>();

  // history modal
  const [currentStepId, setCurrentStepId] = useState<number>();
  const [openHistoryModal, setOpenHistoryModal] = useState<boolean>(false);

  const stepsAll = steps;

  useEffect(() => {
    if (!order_no) return;

    setLoading(true);
    getSysWorkflowStepList({
      pageSize: 500,
      with: 'orderOutWorkflowSteps,latestOrderOutWorkflowStepLog',
      order_no,
      workflow_id,
      section_id,
      section_position: position,
    })
      .then((res) => {
        setSteps(res.data ?? []);
        const initialValues: any = {};
        let existAnyWorkflowStepInfo = false;

        (res.data ?? []).forEach((step) => {
          const field_type = step.field_type;
          const definedSteps = step.order_out_workflow_steps;
          const values =
            isArray(definedSteps) && definedSteps.length
              ? step.order_out_workflow_steps?.map((x) => x.value)
              : step.default_value || [];
          const firstValue = values?.[0];

          let fieldValue = firstValue;
          if (field_type == 'switch') {
            fieldValue = fieldValue == '1' || fieldValue === true || fieldValue === 'true';
          } else if (field_type == 'multiselect' || field_type == 'checkbox') {
            fieldValue = isArray(values) ? values : [values];
          } else if (field_type == 'radio') {
          } else if (field_type == 'daterange') {
            initialValues[`${step.id}_2`] = definedSteps?.[0]?.value2;
          }
          initialValues[`${step.id}`] = fieldValue;

          if (!existAnyWorkflowStepInfo && isArray(definedSteps) && definedSteps.length) {
            existAnyWorkflowStepInfo = true;
          }
        });

        formRef.current?.setFieldsValue(initialValues);
      })
      .finally(() => setLoading(false));
  }, [order_no, workflow_id, section_id, position]);

  return orderOut?.categories?.length && orderOut.type_code ? (
    <>
      <Spin spinning={loading} style={{ minHeight: 400 }}>
        <ProForm
          formRef={formRef}
          layout="horizontal"
          labelCol={{ span: 9 }}
          labelAlign="left"
          submitter={false}
          onValuesChange={(__) => {
            setRefreshTick((prev) => prev + 1);
          }}
        >
          {stepsAll
            .filter((step) => checkVisibleRule(step, stepsAll, undefined, orderOut))
            .map((step) => (
              <Row key={step.id} gutter={8} className="workflow">
                <Col span={step?.field_type !== 'divider' ? 22 : 24}>
                  <WorkflowStepDataItem
                    step={step}
                    order_no={sn(order_no)}
                    updateCallback={(res: any) => {
                      console.log('Update cb: ', res);
                      if ('value2' in res) {
                        formRef.current?.setFieldsValue({
                          [`${res.step_id}_2`]: res.value2,
                        });
                      } else {
                        formRef.current?.setFieldsValue({
                          [`${res.step_id}`]: res.value,
                        });
                      }

                      setSteps((prev) => {
                        const newDS = [...prev];
                        const row = _.find(newDS, { id: res.step_id }) as API.SysWorkflowStep;
                        if (row) {
                          row.latest_order_out_workflow_step_log =
                            res.step.latest_order_out_workflow_step_log;
                          row.order_out_workflow_steps = res.step.order_out_workflow_steps;
                        }
                        return newDS;
                      });

                      // request to reload comment list
                      props.setRefreshTaskCommentsList?.((prev) => prev + 1);
                    }}
                  />
                </Col>
                {step?.field_type !== 'divider' && (
                  <Col span={2}>
                    {step?.latest_order_out_workflow_step_log && (
                      <div className="text-xxs c-grey">
                        <div
                          title="View history"
                          className="cursor-pointer"
                          onClick={() => {
                            setOpenHistoryModal(true);
                            setCurrentStepId(step?.id);
                          }}
                        >
                          {Util.dtToDMYHHMM(step?.latest_order_out_workflow_step_log?.created_on)}{' '}
                          {step?.latest_order_out_workflow_step_log?.user?.username}
                        </div>
                      </div>
                    )}
                  </Col>
                )}
              </Row>
            ))}
        </ProForm>
      </Spin>
      <Modal
        width="700px"
        wrapProps={{ paddingTop: 50 }}
        open={openHistoryModal}
        onCancel={() => setOpenHistoryModal(false)}
        footer={false}
      >
        {currentStepId && order_no && (
          <OrderOutWorkflowStepLogList
            hidePageContainer
            order_no={order_no}
            step_id={currentStepId}
            open={openHistoryModal}
            refreshTick={refreshTick}
          />
        )}
      </Modal>
    </>
  ) : (
    <></>
  );
};
export default WorkflowSection;
