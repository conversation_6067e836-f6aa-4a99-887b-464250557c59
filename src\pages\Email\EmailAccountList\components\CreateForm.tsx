import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { addEmailAccount } from '@/services/app/Email/email-account';
import { message } from 'antd';
import Util from '@/util';
import SDatePicker from '@/components/SDatePicker';

const handleAdd = async (fields: API.EmailAccount) => {
  const hide = message.loading('Adding...', 0);
  const data = { ...fields };
  try {
    await addEmailAccount(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error('Adding failed, please try again!', error);
    return false;
  } finally {
    hide();
  }
};

export type CreateFormProps = {
  values?: Partial<API.EmailAccount>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.EmailAccount) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={'New Email account'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.EmailAccount);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormText
        rules={[
          {
            required: true,
            message: 'Email is required',
          },
        ]}
        width="md"
        name="email"
        label="Email"
      />
      <ProFormText.Password
        rules={[
          {
            required: true,
            message: 'Password is required',
          },
        ]}
        width="md"
        name={['password']}
        label="IMAP Password"
      />
      <SDatePicker
        name={['settings', 'imapSince']}
        label="Since"
        width="md"
        help="Pop Emails since this date."
      />
    </ModalForm>
  );
};

export default CreateForm;
