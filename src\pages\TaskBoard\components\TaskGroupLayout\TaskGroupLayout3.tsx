import { Col, Row } from 'antd';
import TaskPanel from '../TaskPanel';
import type { TaskGroupLayoutProps } from './TaskGroupLayoutDefault';

/**
 * base props of task group layout-Default
 */
export type TaskGroupLayout3Props = TaskGroupLayoutProps & {
  options?: Record<string, any>;
};

const TaskGroupLayout3: React.FC<TaskGroupLayout3Props> = (props) => {
  const {
    blocksMap,
    group,
    tasks,
    setTasks,
    onTaskClick,
    onTaskPanelClick,
    searchFormRef,
    options,
    parentGroupId,
    parentBlockId,
  } = props;
  const groupId = group?.id;

  return (
    <>
      <Row className="wrap" gutter={[20, 0]}>
        {[1, 3, 5, 7, 9].map((ind) => {
          return (
            <Col key={ind} className="left-wrap" flex="0 0 20%" style={{ overflow: 'hidden' }}>
              {blocksMap[ind] &&
                blocksMap[ind].map((block: API.TaskBlock) => (
                  <TaskPanel
                    block={block}
                    key={block.id}
                    groupId={groupId}
                    droppableId={`${block.id}`}
                    tasks={tasks[block.id]}
                    setTasks={setTasks}
                    onTaskClick={onTaskClick}
                    onTaskPanelClick={onTaskPanelClick}
                    searchFormRef={searchFormRef}
                    parentGroupId={parentGroupId}
                    parentBlockId={parentBlockId}
                  />
                ))}
            </Col>
          );
        })}
        {[2, 4, 6, 8, 10].map((ind) => {
          return (
            <Col key={ind} className="left-wrap" flex="0 0 20%" style={{ overflow: 'hidden' }}>
              {blocksMap[ind] &&
                blocksMap[ind].map((block: API.TaskBlock) => (
                  <TaskPanel
                    block={block}
                    key={block.id}
                    groupId={groupId}
                    droppableId={`${block.id}`}
                    tasks={tasks[block.id]}
                    setTasks={setTasks}
                    onTaskClick={onTaskClick}
                    onTaskPanelClick={onTaskPanelClick}
                    searchFormRef={searchFormRef}
                    parentGroupId={parentGroupId}
                    parentBlockId={parentBlockId}
                  />
                ))}
            </Col>
          );
        })}
      </Row>
    </>
  );
};

export default TaskGroupLayout3;
