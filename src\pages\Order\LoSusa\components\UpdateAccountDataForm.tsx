import type { Dispatch, SetStateAction } from 'react';
import { useState } from 'react';
import { useEffect } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect, ProFormTextArea } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import type { InputRef } from 'antd';
import { Button, Divider, Input, Space, message } from 'antd';
import Util, { sn } from '@/util';
import HtmlEditor from '@/components/HtmlEditor';
import { updateLoAccount } from '@/services/app/LexOffice/lo-susa';
import LoAccountPlanningList from './LoAccountPlanningList';
import type { ActionType } from '@ant-design/pro-table';
import { PlusOutlined } from '@ant-design/icons';

export type FormValueType = API.LoAccount;

const handleAccountUpdate = async (dataParam: FormValueType) => {
  const hide = message.loading('Updating account information...', 0);
  const data = { ...dataParam };
  try {
    await updateLoAccount(sn(data.account_no), data);
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type UpdateAccountDataFormProps = {
  initialValues: API.LoAccount;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: FormValueType) => Promise<boolean | void>;
  // parent table's action ref
  parentActionRef?: React.MutableRefObject<ActionType | undefined>;

  categories: string[];
  loadAccountCategories: () => void;
};

const UpdateAccountDataForm: React.FC<UpdateAccountDataFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit, categories, loadAccountCategories } = props;
  const [planningRefreshTick, setPlanningRefreshTick] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);

  // Ref object for new category in categories dropdown.
  const newCatRef = useRef<InputRef>(null);
  const handleCreateCategory = () => {
    const newCat = newCatRef.current?.input?.value;
    if (newCat) {
      setLoading(true);
      formRef.current?.setFieldValue('category', newCat);
      const hide = message.loading('Updating category...', 0);
      updateLoAccount(sn(props.initialValues?.account_no), {
        ...formRef.current?.getFieldsValue(),
        account_no: props.initialValues.account_no,
        category: newCat,
      })
        .then((res) => {
          loadAccountCategories();
          formRef.current?.setFieldValue('category', res.category);
        })
        .catch((err) => Util.error(err))
        .finally(() => {
          hide();
          setLoading(false);
        });
    } else {
      message.error('Please fill new category.');
    }
  };

  useEffect(() => {
    if (modalVisible) {
      formRef.current?.resetFields();
      formRef.current?.setFieldsValue(props.initialValues);
      setPlanningRefreshTick((prev) => prev + 1);
    }
  }, [modalVisible, props.initialValues]);

  return (
    <ModalForm<FormValueType>
      title={'Update Account Information'}
      width="700px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelCol={{ span: 4 }}
      labelAlign="left"
      formRef={formRef}
      disabled={loading}
      onFinish={async (formValue) => {
        // Balance only available
        const success = await handleAccountUpdate({
          ...formValue,
          account_no: props.initialValues.account_no,
        });
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(formValue);
        }
      }}
      submitter={{
        render(__) {
          return [
            <Button
              key="cancel"
              onClick={() => {
                props.handleModalVisible(false);
              }}
            >
              Cancel
            </Button>,
            <Button
              key="submit"
              type="primary"
              onClick={() => {
                formRef.current?.submit();
              }}
            >
              Update
            </Button>,
          ];
        },
      }}
    >
      <ProFormSelect
        name="category"
        label="Category"
        width="sm"
        placeholder="Category"
        showSearch
        options={categories}
        fieldProps={{
          dropdownRender(menu) {
            return (
              <>
                {menu}
                <Divider style={{ margin: '8px 0' }} />
                <Space style={{ padding: '0 8px 4px' }}>
                  <Input
                    placeholder="New category"
                    ref={newCatRef}
                    onKeyDown={(e) => {
                      if (Util.isEnterKey(e)) {
                        handleCreateCategory();
                      }
                    }}
                  />
                  <Button
                    type="text"
                    icon={<PlusOutlined />}
                    onClick={() => {
                      handleCreateCategory();
                    }}
                  />
                </Space>
              </>
            );
          },
        }}
      />
      <ProFormTextArea fieldProps={{ rows: 3 }} name="desc" label="Description" />
      <ProForm.Item name={'notes'} label="Notes">
        <HtmlEditor initialValue={props.initialValues?.notes} min_height={300} />
      </ProForm.Item>
      <LoAccountPlanningList
        accountNo={sn(props.initialValues.account_no)}
        refreshTick={planningRefreshTick}
        createOrUpdateCallback={(value) => {
          if (props.parentActionRef?.current) {
            props.parentActionRef?.current.reload();
          }
        }}
      />
    </ModalForm>
  );
};

export default UpdateAccountDataForm;
