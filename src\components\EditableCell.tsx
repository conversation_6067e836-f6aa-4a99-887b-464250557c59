import Util from '@/util';
import { LoadingOutlined } from '@ant-design/icons';
import type { ProFormFieldProps } from '@ant-design/pro-form';
import { ProFormCheckbox, ProFormRadio } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import { ProFormSwitch } from '@ant-design/pro-form';
import { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { Spin } from 'antd';
import { useEffect, useRef, useState } from 'react';
import _, { isArray } from 'lodash';
import SProFormDigit from '@/components/SProFormDigit';
import type { ProFormSelectProps } from '@ant-design/pro-form/lib/components/Select';
import SProFormSelect from './SProFormSelect';
import { EditTwoTone } from '@ant-design/icons/lib/icons';

type EditableCellProps = {
  dataType: API.HtmlFieldType;
  defaultValue: any;
  children?: React.ReactNode;
  precision?: number; // Number only
  dataOptions?: any; // Select only
  isTabSelectable?: boolean; // Select only
  fieldProps?: ProFormFieldProps;
  style?: any;
  isDefaultEditing?: boolean;
  showEditIcon?: boolean;
  triggerUpdate: (value: any, cancelEdit?: () => void) => Promise<void>;
} & Partial<ProFormSelectProps> &
  Partial<ProFormFieldProps>;

const EditableCell: React.FC<EditableCellProps> = ({
  dataType,
  defaultValue,
  triggerUpdate,
  children,

  isDefaultEditing,
  precision,
  dataOptions,
  fieldProps,
  isTabSelectable,
  showEditIcon,
  ...restProps
}) => {
  const [editing, setEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [changedValue, setChangedValue] = useState<any>(defaultValue);

  const inputRef = useRef<any>(null);

  useEffect(() => {
    setEditing(!!isDefaultEditing);
  }, [isDefaultEditing]);

  useEffect(() => {
    if (editing) {
      inputRef?.current?.focus();
    }
  }, [editing]);

  const toggleEdit = () => {
    setEditing((prev) => !prev);
  };

  const cancelEdit = () => {
    setEditing(false);
    setLoading(false);
  };

  let editorNode = null;
  const defaultFormItemProps = { style: { ...restProps.style, marginBottom: 0 } };
  const defaultEditorFieldProps: any = {
    ...fieldProps,
    ref: inputRef,
    defaultValue,
    size: 'small',
    allowClear: false,
    onChange: (value: any) => {
      setChangedValue(value);
      if (dataType == 'switch' || dataType == 'select') {
        setLoading(true);
        triggerUpdate(value, cancelEdit);
      }
    },
    onKeyDown: (e: any) => {
      if (Util.isEnterKey(e)) {
        inputRef.current.blur();
      }
    },

    onBlur: (e: any) => {
      if (dataType == 'switch') {
        if (!isDefaultEditing) {
          setEditing(false);
        }
        return;
      }
      let newValue = null;
      if (dataType == 'number') {
        newValue = changedValue;
      } else if (dataType == 'select') {
        if (!isDefaultEditing) {
          setEditing(false);
        }
        return;
      } else if (dataType == 'multiselect') {
        newValue = changedValue;
      } else {
        newValue = e.target.value;
      }

      if (defaultValue == newValue) {
        if (!isDefaultEditing) {
          setEditing(false);
        }
        return;
      }
      setLoading(true);
      triggerUpdate(newValue, () => setEditing(false)).finally(() => setLoading(false));
    },
  };

  switch (dataType) {
    case 'number':
      editorNode = (
        <SProFormDigit
          fieldProps={{
            ...defaultEditorFieldProps,
            precision: precision ?? 0,
          }}
          formItemProps={defaultFormItemProps}
        />
      );
      break;
    case 'select':
    case 'multiselect':
      if (isTabSelectable) {
        editorNode = (
          <SProFormSelect
            fieldProps={{
              ...defaultEditorFieldProps,
            }}
            mode={dataType == 'multiselect' ? 'multiple' : 'single'}
            formItemProps={{ ...defaultFormItemProps }}
            options={dataOptions}
            {...restProps}
            onTabKeyCallback={(value) => {
              setChangedValue(value);
              setLoading(true);
              triggerUpdate(value, () => setEditing(false)).finally(() => setLoading(false));
            }}
          />
        );
      } else {
        editorNode = (
          <ProFormSelect
            fieldProps={{
              ...defaultEditorFieldProps,
            }}
            mode={dataType == 'multiselect' ? 'multiple' : 'single'}
            formItemProps={{ ...defaultFormItemProps }}
            options={dataOptions}
            {...restProps}
          />
        );
      }
      break;
    case 'switch':
      editorNode = (
        <ProFormSwitch
          fieldProps={{
            ...defaultEditorFieldProps,
            defaultChecked: isArray(defaultValue) ? !!defaultValue[0] : !!defaultValue,
          }}
          formItemProps={{ ...defaultFormItemProps }}
          {...restProps}
        />
      );
      break;
    case 'radio':
      editorNode = (
        <ProFormRadio.Group
          fieldProps={{
            ...defaultEditorFieldProps,
          }}
          options={dataOptions}
          formItemProps={{ ...defaultFormItemProps }}
          {...restProps}
        />
      );
      break;
    case 'checkbox':
      editorNode = (
        <ProFormCheckbox.Group
          fieldProps={{
            ...defaultEditorFieldProps,
          }}
          options={dataOptions}
          formItemProps={{ ...defaultFormItemProps }}
          {...restProps}
        />
      );
      break;
    case 'textarea':
      editorNode = (
        <ProFormTextArea
          fieldProps={{
            ...defaultEditorFieldProps,
          }}
          formItemProps={defaultFormItemProps}
        />
      );
      break;
    default:
      editorNode = (
        <ProFormText
          fieldProps={{
            ...defaultEditorFieldProps,
          }}
          formItemProps={defaultFormItemProps}
        />
      );
      break;
  }

  /* const childNode = editing ? (
    editorNode
  ) : (
    <div
      className="editable-cell-value-wrap"
      style={{ ...(restProps.style ?? {}) }}
      onClick={toggleEdit}
    >
      {children}
      {showEditIcon && <EditTwoTone style={{ marginLeft: 8, color: 'grey' }} />}
    </div>
  ); */

  const childNode = editing ? (
    <Spin spinning={loading} size="small" indicator={<LoadingOutlined />}>
      {editorNode}
    </Spin>
  ) : (
    <div
      className="editable-cell-value-wrap"
      style={{ ...(restProps.style ?? {}) }}
      onClick={toggleEdit}
    >
      {children}
      {showEditIcon && <EditTwoTone style={{ marginLeft: 8, color: 'grey' }} />}
    </div>
  );

  return childNode;
};

export default EditableCell;
