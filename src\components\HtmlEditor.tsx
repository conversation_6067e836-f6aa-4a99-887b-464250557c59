// @ts-nocheck
import React, { useEffect, useRef } from 'react';
import { Editor } from '@tinymce/tinymce-react';
import type { FormItemProps } from 'antd';

const toTinyMceValue = (htmlValue: string) => {
  let value: string = htmlValue || '';
  let nStartBody: number = -1;
  let nEndBody: number = -1;

  nStartBody = value.indexOf('<body>');
  nEndBody = value.indexOf('</body>');
  if (nStartBody !== -1) {
    value = value.substring(nStartBody + 6, nEndBody - 1);
  }

  return value;
};

type IHtmlEditorProps = {
  className?: string;
  placeholder?: string;
  height?: number;
  min_height?: number;
  onChangeHtml?: (newHtml: string) => void;
  initialFocus?: boolean;
  enableTextModule?: boolean;
  hideMenuBar?: boolean;
} & FormItemProps;

const HtmlEditor = (props: IHtmlEditorProps) => {
  // onChange, value, id are from antd.
  const {
    placeholder,
    className,
    height,
    onChangeHtml,
    initialValue,
    onChange,
    value,
    id,
    min_height,
    initialFocus,
    enableTextModule,
    hideMenuBar,
    ...rest
  } = props;

  // const ref = useRef<Editor>(null);

  const handleChange = (newTextParam: string) => {
    const newText = newTextParam;
    onChange?.(newText);
    onChangeHtml?.(newText);

    // const rawEditor = ref.current?.editor;
  };

  return (
    <Editor
      inline={false}
      tagName={`div`}
      value={value}
      initialValue={initialValue}
      tinymceScriptSrc={`${API_URL}/assets/js/tinymce-6/tinymce.min.js`}
      onInit={(evt, editor) => {
        if (initialFocus) {
          editor.focus();
        }
      }}
      init={{
        smart_paste: true,
        // file_picker_types: 'image',
        paste_data_images: false,
        // menubar: false,
        menubar: hideMenuBar ? false : 'file edit view insert format tools table help',
        toolbar_sticky: true,
        toolbar_mode: 'sliding',
        toolbar:
          'bold italic underline strikethrough | forecolor backcolor |,fontselect,fontsizeselect,formatselect,| alignleft aligncenter alignright alignjustify | bullist numlist link | code preview fullscreen',
        // toolbar: 'fontselect fontsizeselect formatselect',
        fontsize_formats: '8pt 10pt 12pt 14pt 18pt 24pt',
        branding: false,
        plugins: 'link lists advlist autoresize autolink preview fullscreen code wordcount table',
        height: height ?? 200,
        min_height: height ?? 200,
        max_height: 400,
        toolbar_location: 'top',
        content_style: 'body { font-family:Inter,Arial,sans-serif; font-size:13px }',
        skin: 'oxide',
        // icons: 'default',
      }}
      onEditorChange={handleChange}
    />
  );
};

export default HtmlEditor;
