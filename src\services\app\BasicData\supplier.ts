/* eslint-disable */
import { DefaultOptionType } from 'antd/lib/select';
import { request } from 'umi';

const urlPrefix = '/api/basic-data/supplier';

/** rule GET /api/basic-data/supplier */
export async function getSupplierList(
  params: API.PageParams,
  sort: any,
  filter: any,
): Promise<API.PaginatedResult<API.Supplier>> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: JSON.stringify(sort),
      filter_detail: JSON.stringify(filter),
    },
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

export async function getSupplierACList(
  params?: API.PageParams,
  sort?: Record<string, any>,
): Promise<(DefaultOptionType & { uid?: string, org_a?: string })[]> {
  return getSupplierList({ ...params, pageSize: params?.perPage ?? 400, current: params?.current ?? 1 }, { name: 'ascend' }, sort ?? {}).then((res) =>
    res.data?.map((x) => ({
      value: x.id,
      uid: `s${x.id}`,
      label: `${x.name}${x.description ? ` - ${x.description.substring(0, 20)}` : ''}`,
      org_a: x.org_a,
      name: x.name,
    })),
  );
}

/** put PUT /api/basic-data/supplier */
export async function updateSupplier(data: API.Supplier, options?: { [key: string]: any }) {
  return request<API.Supplier>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/basic-data/supplier */
export async function addSupplier(data: API.Supplier, options?: { [key: string]: any }) {
  return request<API.Supplier>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/basic-data/supplier/{id} */
export async function deleteSupplier(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}
