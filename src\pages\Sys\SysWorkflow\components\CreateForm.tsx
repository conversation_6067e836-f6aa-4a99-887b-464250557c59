import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { addSysWorkflow } from '@/services/app/Sys/sys-workflow';
import { message } from 'antd';
import Util from '@/util';
import { SysWorkflowTypeOptionsKvCreatable } from '@/constants';

const handleAdd = async (fields: API.SysWorkflow) => {
  const hide = message.loading('Adding a workflow...', 0);
  const data = { ...fields };
  try {
    await addSysWorkflow(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type CreateFormProps = {
  values?: Partial<API.SysWorkflow>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.SysWorkflow) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={'New Workflow'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 7 }}
      wrapperCol={{ span: 24 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.SysWorkflow);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormText
        required
        rules={[
          {
            required: true,
            message: 'Name is required',
          },
          {
            max: 255,
          },
        ]}
        width="md"
        name="name"
        label="Name"
      />
      <ProFormSelect
        required
        rules={[
          {
            required: true,
            message: 'Type is required',
          },
        ]}
        width="md"
        name="type"
        label="Type"
        valueEnum={SysWorkflowTypeOptionsKvCreatable}
      />
    </ModalForm>
  );
};

export default CreateForm;
