import { message } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useCallback } from 'react';

export type NavigationActionType = 'prev' | 'next';

export type HandleNavFuncType = (action?: NavigationActionType, loId?: string) => void;

export type SetStatesParamType = {
  setCurrentRow: Dispatch<SetStateAction<API.LoInvoiceCheck | undefined>>;
};

export default (dataSource: API.LoInvoiceCheck[], dispachers: SetStatesParamType) => {
  const handleNavigation: HandleNavFuncType = useCallback(
    (action?: NavigationActionType, lo_id?: string) => {
      const ind = dataSource.findIndex((x) => x.lo_id == lo_id);
      if (ind >= 0) {
        const dir = action == 'next' ? 1 : -1;
        const nextInd = (ind + dir + dataSource.length) % dataSource.length;

        if (action == 'next' && nextInd < ind) {
          message.info('No next record!');
        } else if (action == 'prev' && nextInd > ind) {
          message.info('No prev record!');
        } else {
          dispachers.setCurrentRow(dataSource[nextInd]);
        }
      }
    },
    [dispachers, dataSource],
  );

  return { handleNavigation };
};
