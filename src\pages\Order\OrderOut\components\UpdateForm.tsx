import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormDigit } from '@ant-design/pro-form';
import { ProFormText, ModalForm } from '@ant-design/pro-form';
import { updateOrderOut } from '@/services/app/Order/order-out';
import Util from '@/util';
import SProFormTextAreaExpanded from './SProFormTextAreaExpanded';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...');

  try {
    await updateOrderOut(fields);
    hide();
    message.success('Update is successful');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

// eslint-disable-next-line @typescript-eslint/ban-types
export type FormValueType = {} & Partial<API.OrderOut>;

export type UpdateFormProps = {
  initialValues?: Partial<API.OrderOut>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.OrderOut) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef && formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, props.initialValues]);

  return (
    <ModalForm
      title={'Update Order Out'}
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 7 }}
      wrapperCol={{ span: 16 }}
      initialValues={props.initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate({
          ...value,
          old_order_no: props.initialValues?.order_no,
        });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormDigit
        required
        rules={[
          {
            required: true,
            message: 'Order No is required',
          },
        ]}
        width="sm"
        name="order_no"
        label="Order No"
      />
      <ProFormText
        required
        rules={[
          {
            required: true,
            message: 'Customer is required',
          },
        ]}
        width="lg"
        name="customer"
        label="Customer"
      />
      <ProFormText width="lg" name="lotus_notes_id" label="Lotus Notes ID" />

      <SProFormTextAreaExpanded width="lg" name="desc" label="Description" formRef={formRef} />

      {/* <div>
        {selection[0]}, {selection[1]} = {selection[1] - selection[0]}
      </div> */}
    </ModalForm>
  );
};

export default UpdateForm;
