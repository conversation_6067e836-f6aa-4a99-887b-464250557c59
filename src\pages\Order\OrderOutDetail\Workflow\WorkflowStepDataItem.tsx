import SDatePicker from '@/components/SDatePicker';
import SProFormDateRange from '@/components/SProFormDateRange';
import SProFormDigit2 from '@/components/SProFormDigit2';
import { updateOrderOutSupWorkflowStep } from '@/services/app/Order/order-out-sup-workflow-step';
import { updateOrderOutWorkflowStep } from '@/services/app/Order/order-out-workflow-step';
import Util, { sn } from '@/util';
import type { ProFormFieldProps } from '@ant-design/pro-form';
import {
  ProFormCheckbox,
  ProFormRadio,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-form';
import type { ProFormFieldItemProps } from '@ant-design/pro-form/lib/interface';
import { Divider, message } from 'antd';
import { useRef } from 'react';

type WorkflowStepDataItemProps = {
  order_no: number;
  supplier_id?: number;
  step?: API.SysWorkflowStep;
  formItemProps?: ProFormFieldItemProps;
  fieldProps?: { precision?: number; width?: any };
  updateCallback?: (res: any) => void;
};
const WorkflowStepDataItem: React.FC<WorkflowStepDataItemProps> = (props) => {
  const { step, order_no, supplier_id, updateCallback } = props;
  const isChangedRef = useRef(false);

  const handleUpdate = async (value: any, isSecondValue?: boolean) => {
    const hide = message.loading('Updating...', 0);
    const data: any = {
      workflow_id: sn(step?.workflow_id),
      step_id: step?.id,
      order_no,
      single_update: true,
    };
    if (isSecondValue) {
      data.value2 = step?.field_type === 'switch' ? (value ? '1' : '0') : value;
    } else {
      data.value = step?.field_type === 'switch' ? (value ? '1' : '0') : value;
    }

    if (supplier_id) {
      data.supplier_id = supplier_id;

      return updateOrderOutSupWorkflowStep(data)
        .then((res) => {
          message.success('Saved successfully.');
          if (isSecondValue) {
            updateCallback?.({ step_id: step?.id, value2: value, step: res });
          } else {
            updateCallback?.({ step_id: step?.id, value: value, step: res });
          }
          return res;
        })
        .catch((e) => Util.error(e))
        .finally(() => hide());
    } else {
      return updateOrderOutWorkflowStep(data)
        .then((res) => {
          message.success('Saved successfully.');
          if (isSecondValue) {
            updateCallback?.({ step_id: step?.id, value2: value, step: res });
          } else {
            updateCallback?.({ step_id: step?.id, value: value, step: res });
          }
          return res;
        })
        .catch((e) => Util.error(e))
        .finally(() => hide());
    }
  };

  let ele = null;
  const eleProps: ProFormFieldItemProps = {
    // ...props?.formItemProps,
    label: step?.desc + (process.env.NODE_ENV === 'development' ? ` (${step?.id})` : ''),
    name: `${step?.id}`,
    colon: false,
    fieldProps: {
      ...props.fieldProps,
      size: 'small',
      // event or direct value
      onChange: (e: any) => {
        // console.log('OnChange:', step?.field_type, e);
        if (
          step?.field_type == 'text' ||
          step?.field_type == 'textarea' ||
          step?.field_type == 'number'
        ) {
          isChangedRef.current = true;
        } else {
          let changedValue = null;

          if (step?.field_type == 'date') {
            if (e) {
              changedValue = Util.dtToYMD(e);
            }
          } else if (step?.field_type == 'daterange') {
            if (e) {
              changedValue = Util.dtToYMD(e);
            }
            // } else if (step?.field_type == 'number') {
            // changedValue = e;
          } else if (step?.field_type == 'switch') {
            changedValue = e;
          } else if (step?.field_type == 'multiselect' || step?.field_type == 'checkbox') {
            changedValue = e;
          } else if (step?.field_type == 'select') {
            changedValue = e;
          } else if (step?.field_type == 'radio') {
            changedValue = e.target.value;
          } else {
            changedValue = e.target.value;
          }

          // console.log(' -->', step?.field_type, changedValue);
          handleUpdate(changedValue).then((res) => {
            if (res) {
              isChangedRef.current = false;
            }
          });
        }
      },
      onBlur: (e: any) => {
        if (
          (step?.field_type == 'text' ||
            step?.field_type == 'textarea' ||
            step?.field_type == 'number') &&
          isChangedRef.current
        ) {
          // console.log('onBlur', step?.field_type, e);
          handleUpdate(step?.field_type == 'number' ? e : e.target.value).then((res) => {
            if (res) {
              isChangedRef.current = false;
            }
          });
        }
      },
    },
    formItemProps: { style: { marginBottom: 4 /* , borderBottom: '1px solid #f4f4f4' */ } },
  };

  // For second value of workflow step value: Used in daterange.
  const eleProps2: ProFormFieldItemProps = {
    fieldProps: {
      size: 'small',
      name: `${step?.id}_2`,
      // event or direct value
      onChange: (e: any) => {
        if (step?.field_type == 'daterange') {
          let changedValue = null;
          if (step?.field_type == 'daterange') {
            if (e) {
              changedValue = Util.dtToYMD(e);
            }
          }
          handleUpdate(changedValue, true).then((res) => {
            if (res) {
              isChangedRef.current = false;
            }
          });
        }
      },
    },
    formItemProps: { style: { marginBottom: 4 /* , borderBottom: '1px solid #f4f4f4' */ } },
  };

  if (step?.field_type == 'text') {
    ele = <ProFormText {...eleProps} />;
  } else if (step?.field_type == 'number') {
    ele = <SProFormDigit2 {...eleProps} />;
  } else if (step?.field_type == 'textarea') {
    ele = <ProFormTextArea {...eleProps} />;
  } else if (step?.field_type == 'multiselect') {
    ele = <ProFormSelect {...eleProps} mode="multiple" options={step.options || []} />;
  } else if (step?.field_type == 'select') {
    ele = <ProFormSelect {...eleProps} mode="single" options={step.options || []} />;
  } else if (step?.field_type == 'switch') {
    ele = <ProFormSwitch {...eleProps} fieldProps={{ ...eleProps.fieldProps }} />;
  } else if (step?.field_type == 'radio') {
    ele = <ProFormRadio.Group {...eleProps} options={step.options || []} />;
  } else if (step?.field_type == 'checkbox') {
    ele = <ProFormCheckbox.Group {...eleProps} options={step.options || []} />;
  } else if (step?.field_type == 'date') {
    ele = <SDatePicker {...eleProps} />;
  } else if (step?.field_type == 'daterange') {
    ele = (
      <SProFormDateRange
        startDateName={`${step?.id}`}
        endDateName={`${step?.id}_2`}
        fieldItemProps1={eleProps}
        fieldItemProps2={eleProps2}
        label={eleProps.label}
      />
    );
  } else if (step?.field_type == 'divider') {
    ele = <Divider orientation="left">{step.desc}</Divider>;
  }

  return ele;
};

export default WorkflowStepDataItem;
