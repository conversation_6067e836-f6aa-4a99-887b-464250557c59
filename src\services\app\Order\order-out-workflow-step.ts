/* eslint-disable */
import { request } from 'umi';

const urlPrefix = '/api/order/order-out-workflow-step';

/**  
 * 
 * PUT /api/order/order-out-workflow-step 
 */
export async function updateOrderOutWorkflowStep(
  data: API.OrderOutWorkflowStep & {
    workflow_id?: number;
    step_info?: any;
    single_update?: boolean;
  },
  options?: { [key: string]: any },
) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** 
 * Not used yet.
 * 
 * POST /api/order/order-out-workflow-step 
 */
export async function addOrderOutWorkflowStep(
  data: Partial<API.OrderOutWorkflowStep> & { workflow_id?: number },
  options?: { [key: string]: any },
) {
  return request<API.OrderOutWorkflowStep>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}
