import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { Button, message, Popconfirm, Space } from 'antd';
import React, { useState, useRef, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import UpdateForm from './components/UpdateForm';

import Util, { sn } from '@/util';
import CreateForm from './components/CreateForm';
import { getOrderOutHeadList, deleteOrderOutHead } from '@/services/app/Order/order-out-head';

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.OrderOutHead[]) => {
  const hide = message.loading('Deleting');
  if (!selectedRows) return true;

  try {
    await deleteOrderOutHead({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error('Delete failed, please try again!', error);
    return false;
  }
};

type OrderOutHeadListProps = {
  pageSize?: number;
  refreshTick?: number;
  hidePageContainer?: boolean;
};

const OrderOutHeadList: React.FC<OrderOutHeadListProps> = (props) => {
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.OrderOutHead>();

  const columns: ProColumns<API.OrderOutHead>[] = [
    {
      title: 'Name',
      sorter: true,
      dataIndex: ['name'],
      valueType: 'text',
      search: false,
    },
    {
      title: 'Created on',
      sorter: true,
      className: 'text-sm c-grey',
      dataIndex: ['name'],
      valueType: 'text',
      search: false,
      width: 100,
      render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
    },
    {
      title: '',
      dataIndex: 'option',
      valueType: 'option',
      showSorterTooltip: false,
      width: 50,
      render: (_, record) => (
        <Space size={0}>
          <Button
            key="edit"
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => {
              handleUpdateModalVisible(true);
              setCurrentRow(record);
            }}
          />
          <Popconfirm
            key="delete"
            title={<>Are you sure you want to delete?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 300, minWidth: 300 }}
            onConfirm={async () => {
              handleRemove([record])
                .then((res) => actionRef.current?.reload())
                .catch((reason) => Util.error(reason));
            }}
          >
            <Button type="link" size="small" icon={<DeleteOutlined />} />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    if (sn(props.refreshTick) > 0) {
      actionRef.current?.reload();
    }
  }, [props.refreshTick]);

  const renderBody = () => (
    <>
      <ProTable<API.OrderOutHead, API.PageParams>
        headerTitle={'Latest Heads list'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: false, density: false, search: false, setting: false }}
        size="small"
        search={false}
        toolBarRender={() => []}
        pagination={{
          hideOnSinglePage: true,
          showSizeChanger: false,
          pageSize: props.pageSize ?? 15,
        }}
        cardProps={{
          bodyStyle: { padding: 0 },
        }}
        locale={{ emptyText: <></> }}
        request={(params, sort, filter) => {
          return getOrderOutHeadList(
            { ...params, pageSize: 15 },
            { ...sort, created_on: 'descend' },
            filter,
          ) as any;
        }}
        columns={columns}
      />
      {createModalVisible && (
        <CreateForm
          modalVisible={createModalVisible}
          handleModalVisible={handleModalVisible}
          onSubmit={async (value) => {
            handleModalVisible(false);

            if (actionRef.current) {
              actionRef.current.reload();
            }
          }}
        />
      )}
      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
      />
    </>
  );

  return props.hidePageContainer ? renderBody() : <PageContainer>{renderBody()}</PageContainer>;
};

export default OrderOutHeadList;
