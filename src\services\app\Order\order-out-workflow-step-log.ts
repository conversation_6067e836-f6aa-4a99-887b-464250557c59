/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/order/order-out-workflow-step-log';

/** orderOut GET /api/order/order-out-workflow-step-log */
export async function getOrderOutWorkflowStepLogList(
  params: API.PageParams & Partial<API.OrderOutWorkflowStepLog>,
  sort: any,
  filter: any,
): Promise<API.PaginatedResult<API.OrderOutWorkflowStepLog>> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}
