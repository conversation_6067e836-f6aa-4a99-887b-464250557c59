import type { Identifier, XYCoord } from 'dnd-core';
import type { FC } from 'react';
import { useRef } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { Tag } from 'antd';
import type { DragItem, OptionTypeExt } from './WorkflowStepOptionsCell';

export const ItemTypes = {
  TAG: 'tag',
};

const style = {
  cursor: 'move',
  margin: '2px 4px',
};

export interface WorkflowStepOptionProps {
  id: any;
  option: OptionTypeExt;
  index: number;
  moveCard: (dragIndex: number, hoverIndex: number) => void;
  dropFileCallback: (item: DragItem) => void;
}

export const WorkflowStepOption: FC<WorkflowStepOptionProps> = ({
  id,
  option,
  index,
  moveCard,
  dropFileCallback,
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const [{ handlerId }, drop] = useDrop<DragItem, void, { handlerId: Identifier | null }>({
    accept: ItemTypes.TAG,
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
    hover(item: DragItem, monitor) {
      if (!ref.current) {
        return;
      }
      const dragIndex = item.index;
      const hoverIndex = index;

      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return;
      }

      // Determine rectangle on screen
      const hoverBoundingRect = ref.current?.getBoundingClientRect();

      // Get vertical middle
      // const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2
      const hoverMiddleX = (hoverBoundingRect.right - hoverBoundingRect.left) / 2;

      // Determine mouse position
      const clientOffset = monitor.getClientOffset();

      // Get pixels to the top
      // const hoverClientY = (clientOffset as XYCoord).y - hoverBoundingRect.top
      const hoverClientX = (clientOffset as XYCoord).x - hoverBoundingRect.left;

      // Only perform the move when the mouse has crossed half of the items height
      // When dragging downwards, only move when the cursor is below 50%
      // When dragging upwards, only move when the cursor is above 50%

      // Dragging downwards
      // if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
      if (dragIndex < hoverIndex && hoverClientX < hoverMiddleX) {
        return;
      }

      // Dragging upwards
      if (dragIndex > hoverIndex && hoverClientX > hoverMiddleX) {
        return;
      }

      // Time to actually perform the action
      moveCard(dragIndex, hoverIndex);

      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex;
    },
    drop(item, monitor) {
      // console.log(' -------- dropped ----------');
      // console.log('Index', index, 'Item Index', item.index, monitor.getItem());
      dropFileCallback(monitor.getItem());
    },
  });

  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.TAG,
    item: () => {
      return { id, index, orgIndex: index, option };
    },
    collect: (monitor: any) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const opacity = isDragging ? 0 : 1;
  drag(drop(ref));

  return (
    <Tag
      ref={ref}
      style={{ ...style, opacity }}
      data-handler-id={handlerId}
      className={`image-tag`}
    >{`${option.label}`}</Tag>
  );
};
