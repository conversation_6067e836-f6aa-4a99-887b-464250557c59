/* eslint-disable @typescript-eslint/dot-notation */
import SDatePicker from '@/components/SDatePicker';
import SProFormDigit from '@/components/SProFormDigit';
import {
  createContactInLo,
  createOrderOutInvoiceInLo,
  createOrderOutOrderConfirmationInLo,
  createOrderOutQuotationInLo,
  updateOrderOut,
} from '@/services/app/Order/order-out';
import Util, { getLexOfficeLink, sn, urlLODocumentOnSystem } from '@/util';
import { DeleteFilled, PlusOutlined } from '@ant-design/icons';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormDependency, ProFormSelect } from '@ant-design/pro-form';
import { ProFormCheckbox } from '@ant-design/pro-form';
import ProForm, { ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import type { ActionType, EditableFormInstance, ProColumns } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import { Button, Card, message, Popconfirm, Space, notification, Modal } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useMemo } from 'react';
import React, { useEffect } from 'react';
import { useRef, useState } from 'react';
import _ from 'lodash';
import { useModel } from 'umi';
import STextFormFieldExtra from './STextFormFieldExtra';
import ConvertModal from './ConvertModal';
import SMentionsTextFormField from './SMentions';
import Col from 'antd/lib/grid/col';
import { DictCode, DT_FORMAT_YMD } from '@/constants';
import { getSysTextModuleList } from '@/services/app/Sys/text-module';
import useXlsFileLocal from '../FileDetail/useXlsFileLocal';
import moment from 'moment';
import Tag from 'antd/lib/tag';

type UpdatedFormDataType = Partial<API.OrderOut>;

const handleUpdateData = async (fields: UpdatedFormDataType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateOrderOut(fields);
    hide();
    message.success('Update is successful');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

const handleCreateQuotation = async (orderNo: number, fields: UpdatedFormDataType) => {
  const hide = message.loading('Creating a quotation...', 0);

  try {
    const res = await createOrderOutQuotationInLo(orderNo, fields);
    hide();
    return res;
  } catch (error) {
    hide();
    Util.error(error);
    return null;
  }
};

const handleCreateInvoice = async (orderNo: number, fields: UpdatedFormDataType) => {
  const hide = message.loading('Creating an invoice...', 0);

  try {
    const res = await createOrderOutInvoiceInLo(orderNo, fields);
    hide();
    return res;
  } catch (error) {
    hide();
    Util.error(error);
    return null;
  }
};

const handleCreateOrderConfirmation = async (orderNo: number, fields: UpdatedFormDataType) => {
  const hide = message.loading('Creating an order confirmation...', 0);

  try {
    const res = await createOrderOutOrderConfirmationInLo(orderNo, fields);
    hide();
    return res;
  } catch (error) {
    hide();
    Util.error(error);
    return null;
  }
};

type OrderOutItemEditableFormType = Partial<API.OrderOutItem> & {
  uid?: React.Key;
};

type OrderOutInvoiceProps = {
  orderOut?: API.OrderOut;
  setOrderOut?: SetStateAction<Dispatch<API.OrderOut>>;
  loadOrderOut?: (no?: number) => Promise<void>;
};

const getNewKey = (index?: number) => 'key_' + _.uniqueId();

const addNewItem = (
  type: 'child' | 'parent',
  currentRow: OrderOutItemEditableFormType,
  dataSource: OrderOutItemEditableFormType[],
  setDataSource: Dispatch<SetStateAction<OrderOutItemEditableFormType[]>>,
  cb?: (
    newItem: OrderOutItemEditableFormType,
    newDataSource: OrderOutItemEditableFormType[],
  ) => void,
) => {
  const newId = getNewKey(dataSource.length);
  const newObj: any = { id: newId, children: [] };
  if (type == 'parent') {
    const currentIndex = _.findIndex(dataSource, { id: currentRow.id });
    const newDataSource = [...dataSource];
    newDataSource.splice(currentIndex + 1, 0, newObj);
    cb?.(newObj, newDataSource);
  } else {
    const newDataSource = [...dataSource];
    // child?
    if (currentRow.parent_id) {
      const parentIndex = _.findIndex(newDataSource, {
        id: currentRow.parent_id,
      });
      const parent: OrderOutItemEditableFormType = newDataSource[parentIndex];

      const currentIndex = _.findIndex(parent.children, { id: currentRow.id });
      newObj['parent_id'] = currentRow.parent_id;
      if (!parent.children) parent.children = [];
      parent.children?.splice(currentIndex + 1, 0, newObj);
      newDataSource.splice(parentIndex, 1, parent);
      cb?.(newObj, newDataSource);
    } else {
      const parentIndex = _.findIndex(newDataSource, {
        id: currentRow.id,
      });
      const parent: OrderOutItemEditableFormType = newDataSource[parentIndex];

      const currentIndex = parent.children?.length ?? 0;
      newObj['parent_id'] = currentRow.id;
      if (!parent.children) parent.children = [];
      parent.children?.splice(currentIndex + 1, 0, newObj);
      newDataSource.splice(parentIndex, 1, parent);
      cb?.(newObj, newDataSource);
    }
  }
};

const OrderOutInvoice: React.FC<OrderOutInvoiceProps> = (props) => {
  const { orderOut } = props;

  // model
  const { taskContext } = useModel('task');
  const { getCodeValue } = useModel('app-settings');

  const formRef = useRef<ProFormInstance>();
  const [loading, setLoading] = useState(false);

  // Editable table form
  const editableFormRef = useRef<EditableFormInstance>();
  const actionRef = useRef<ActionType>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>(() => []);
  const [dataSource, setDataSource] = useState<OrderOutItemEditableFormType[]>(() => []);

  // convert modal
  const [openConvertModal, setOpenConvertModal] = useState<boolean>(false);

  // custom hooks
  const { aggregationElement } = useXlsFileLocal(props.orderOut);

  // For live saving
  const lazyLoadRequiredRef = useRef<boolean>(false);
  const isFormChangedRef = useRef<boolean>(false);
  const isTableChangedRef = useRef<boolean>(false);
  const isFormChanged = isFormChangedRef.current;

  const paymentTermOptions = useMemo(() => {
    const terms = getCodeValue(DictCode.PAYMENT_TERMS) ?? '';
    if (terms) {
      return terms.split(',').map((x: string) => ({
        value: x.trim(),
        label: x.trim(),
      }));
    }
    return [];
  }, [getCodeValue]);

  const handlePrickeKeyDown = (e: any, record: any) => {
    if (Util.isEnterKey(e) && record) {
      addNewItem('parent', record, dataSource, setDataSource, (newOne, newDataSource) => {
        setDataSource(newDataSource);
        setEditableRowKeys((prev) => [...(prev || []), newOne.id as React.Key]);
        if (!newOne.parent_id)
          setExpandedRowKeys((prev) => [...(prev || []), newOne.id as React.Key]);
      });
    }
  };

  const columns: ProColumns<OrderOutItemEditableFormType>[] = [
    {
      title: 'Qty',
      dataIndex: ['qty'],
      width: 100,
      fieldProps: {
        placeholder: 'Qty',
      },
      formItemProps: (form, { rowIndex, entity, filteredValue }) => {
        const rules = [{ required: false, message: 'Qty is required!' }];
        return {
          rules,
          hasFeedback: false,
        };
      },
      shouldCellUpdate: (prev, next) => {
        return prev.qty != next.qty;
      },
      renderFormItem: (item, { defaultRender, ...rest }, form) => {
        return (
          <SProFormDigit
            placeholder="Qty"
            fieldProps={{ controls: false, precision: 3 }}
            formItemProps={{ style: { marginBottom: 0 } }}
            zeroShow
          />
        );
      },
      onCell: (record, index) => {
        if (record.parent_id) {
          return { colSpan: 0 };
        }
        return {};
      },
    },
    {
      title: 'Unit',
      dataIndex: ['unit'],
      width: 60,
      fieldProps: {
        placeholder: 'Unit',
      },
      shouldCellUpdate: (prev, next) => prev.unit != next.unit,
      formItemProps: (form, { rowIndex, entity, filteredValue }) => {
        const rules = [{ required: false, message: 'Unit is required!' }];
        return {
          rules,
          hasFeedback: false,
          controls: false,
          size: 'small',
        };
      },
      onCell: (record, index) => {
        if (record.parent_id) {
          return { colSpan: 0 };
        }
        return {};
      },
    },
    {
      title: 'Description',
      dataIndex: ['desc'],
      width: 350,
      fieldProps: {
        placeholder: 'Description',
      },
      // shouldCellUpdate: (prev, next) => prev.desc != next.desc,
      formItemProps: (form, { rowIndex, entity, filteredValue }) => {
        const rules = [{ required: false, message: 'Description is required!' }];
        return {
          rules,
          hasFeedback: false,
          style: { marginLeft: 0 },
        };
      },
      renderFormItem: (item, { defaultRender, record, ...rest }, form) => {
        return (
          <STextFormFieldExtra
            form={form}
            record={record}
            formItemProps={{ style: { marginBottom: 0 } }}
            changeCallback={(value: string) => {
              setDataSource((prev) => {
                const newDataSource = [...prev];
                const exist = _.find(newDataSource, { id: record?.id });
                if (exist) {
                  exist.desc = value;
                }
                return newDataSource;
              });
            }}
          />
        );
      },
      onCell: (record, index) => {
        if (record.parent_id) {
          return { colSpan: 6 };
        }
        return {};
      },
    },
    {
      dataIndex: ['tmpColumn'],
      hideInForm: true,
      width: 30,
      editable: false,
      onCell: (record, index) => {
        if (record.parent_id) {
          return { colSpan: 0 };
        }
        return {};
      },
      render: (dom, record) => {
        return (
          <>
            {record && !record?.parent_id && (
              <Button
                key="addNextChild"
                size="small"
                icon={<PlusOutlined />}
                style={{ padding: '2px' }}
                title="Add an extra description"
                onClick={(e) => {
                  addNewItem(
                    'child',
                    record,
                    dataSource,
                    setDataSource,
                    (newOne, newDataSource) => {
                      setDataSource(newDataSource);
                      setEditableRowKeys((prev) => [...(prev || []), newOne.id as React.Key]);
                    },
                  );
                }}
              />
            )}
          </>
        );
      },
    },
    {
      title: 'Price',
      dataIndex: ['price'],
      width: 90,
      tooltip: 'Click to Enter key to add a new Pos line.',
      showSorterTooltip: false,
      fieldProps: {
        placeholder: 'Price',
      },
      shouldCellUpdate: (prev, next) => prev.price != next.price,
      formItemProps: (form, { rowIndex, entity, filteredValue }) => {
        const rules = [{ required: false, message: 'Price is required!' }];
        return {
          rules,
          hasFeedback: false,
        };
      },
      renderFormItem: (item, { defaultRender, record, ...rest }, form) => {
        return (
          <SProFormDigit
            placeholder="Price"
            formItemProps={{ style: { marginBottom: 0 } }}
            fieldProps={{
              controls: false,
              precision: 3,
              onKeyUp: (e) => {
                if (record?.price) {
                  handlePrickeKeyDown(e, record);
                }
              },
            }}
            zeroShow
          />
        );
      },
      onCell: (record, index) => {
        if (record.parent_id) {
          return { colSpan: 0 };
        }
        return {};
      },
    },
    {
      title: 'Total',
      dataIndex: ['price2'],
      editable: false,
      width: 70,
      fixed: 'right',
      fieldProps: {
        placeholder: 'Price',
      },
      shouldCellUpdate: (prev, next) => prev.qty != next.qty || prev.price != next.price,
      render: (dom, record) => {
        return Util.numberFormat(sn(record.price) * sn(record.qty), true, 3, false);
      },
      onCell: (record, index) => {
        if (record.parent_id) {
          return { colSpan: 0 };
        }
        return {};
      },
    },
    {
      title: '',
      valueType: 'option',
      width: 80,
      align: 'center',
      className: 'p0',
      fixed: 'right',
      render: (text, record, __, action) => null,
    },
  ];

  useEffect(() => {
    const initialValues = {
      order_no: orderOut?.order_no,
      pickup: orderOut?.pickup,
      introduction: !orderOut?.introduction
        ? `${orderOut?.order_out_head?.name == 'B041' ? 'B041 - ' : ''}AU ${orderOut?.order_no}`
        : orderOut.introduction,
      issue_date: orderOut?.issue_date,
      exp_date: orderOut?.exp_date,
      gross_weight: orderOut?.gross_weight,
      net_weight: orderOut?.net_weight,
      restriction: orderOut?.restriction,
      is_eur_statement1: orderOut?.is_eur_statement1,
      invoice_date: orderOut?.invoice_date,
      shipping_date: orderOut?.shipping_date,
      payment_term: orderOut?.payment_term,
      payment_term_duration: orderOut?.payment_term_duration,
      payment_term_str:
        orderOut?.payment_term || orderOut?.payment_term_duration
          ? `${orderOut?.payment_term} | ${orderOut?.payment_term_duration}`
          : undefined,
      tax_rate: orderOut?.tax_rate,
      remark: orderOut?.remark,
    };
    if (!orderOut?.invoice_date) {
      initialValues.invoice_date = moment().format(DT_FORMAT_YMD);
    }
    if (!initialValues?.payment_term_str && orderOut?.customer_obj?.default_payment_term) {
      initialValues.payment_term_str = orderOut?.customer_obj?.default_payment_term;
    }

    formRef.current?.setFieldsValue(initialValues);

    // invoice items
    const items = [...(orderOut?.order_out_items || [])];

    setDataSource(items);
    const ids: React.Key[] = [];
    const pIds: React.Key[] = [];
    items.forEach((element) => {
      ids.push(element.id as React.Key);
      if (!element.parent_id) pIds.push(element.id as React.Key);
      element.children?.forEach((e2) => {
        ids.push(e2.id as React.Key);
      });
    });
    setEditableRowKeys(ids);
    setExpandedRowKeys(pIds);
  }, [
    orderOut?.order_no,
    orderOut?.order_out_items,
    orderOut?.pickup,
    orderOut?.gross_weight,
    orderOut?.net_weight,
    orderOut?.restriction,
    orderOut?.shipping_date,
    orderOut?.payment_term,
    orderOut?.payment_term_duration,
    orderOut?.introduction,
    orderOut?.issue_date,
    orderOut?.exp_date,
    orderOut?.tax_rate,
    orderOut?.remark,
    orderOut?.invoice_date,
    orderOut?.order_out_head?.name,
    orderOut?.customer_obj?.default_payment_term,
    orderOut?.is_eur_statement1,
  ]);

  // predefined remark by invoiceAddress.cust_no
  useEffect(() => {
    const remark = formRef.current?.getFieldValue('remark');
    if (!remark) {
      getSysTextModuleList({ numbers: [2002, 2001] }).then((res) => {
        if (res.data?.length) {
          const custNo = _.get(_.find(orderOut?.addresses, { address_type: 'invoice' }), 'cust_no');
          const systext = _.get(
            _.find(res.data, {
              number: custNo && (custNo.endsWith('0') || custNo.endsWith('5')) ? 2002 : 2001,
            }),
            'text',
          );
          if (systext) formRef.current?.setFieldValue('remark', systext);
        }
      });
    }
  }, [orderOut?.addresses]);

  const invAddress = useMemo(() => {
    return _.find(orderOut?.addresses, { address_type: 'invoice' });
  }, [orderOut?.addresses]);

  const handleLiveFormSave = (valueParams?: any) => {
    if (!isFormChangedRef.current) return;
    isFormChangedRef.current = false;

    formRef.current?.setFieldValue('isFinal', false);
    formRef.current?.setFieldValue('action', undefined);
    formRef.current?.setFieldValue('precedingVoucherId', null);

    const values = formRef.current?.getFieldsValue();

    // Get payment_term & term_duration
    if (values.payment_term_str) {
      const arr = values.payment_term_str.split('|');
      values.payment_term = (arr?.[0] ?? '').trim();
      values.payment_term_duration = Util.safeInt((arr?.[1] ?? '').trim());
    } else {
      values.payment_term = null;
      values.payment_term_duration = null;
    }

    handleUpdateData({
      ...values,
      // order_out_items: dataSource,
      order_no: orderOut?.order_no,
    })
      .then((res) => {
        // props.loadOrderOut?.(orderOut?.order_no).finally(() => setLoading(false));
        // taskContext?.reloadInvoice();
      })
      .catch((e) => {
        Util.error(e);
      });
  };

  return (
    <>
      {aggregationElement}
      <Card style={{ marginTop: 0 }} bodyStyle={{ padding: 0 }} bordered={false}>
        <EditableProTable<OrderOutItemEditableFormType>
          editableFormRef={editableFormRef}
          actionRef={actionRef}
          rowKey="id"
          sticky
          bordered={false}
          controlled
          debounceTime={200}
          className="editable-table"
          size="small"
          cardProps={{
            style: { marginBottom: '2rem' },
            bodyStyle: { padding: 0 },
          }}
          scroll={{
            x: 700,
          }}
          style={{
            padding: 0,
          }}
          recordCreatorProps={{
            newRecordType: 'dataSource',
            position: 'bottom',
            creatorButtonText: 'Add new row',
            record: (index: number, dataSource2: OrderOutItemEditableFormType[]) => {
              return { id: getNewKey(index) };
            },
          }}
          loading={loading}
          columns={columns}
          value={dataSource}
          onChange={setDataSource}
          locale={{ emptyText: <></> }}
          expandable={{
            defaultExpandAllRows: true,
            expandedRowKeys,
            onExpandedRowsChange(expandedKeys) {
              setExpandedRowKeys([...expandedKeys]);
            },
            indentSize: 100,
            rowExpandable: (record) => {
              return !record?.parent_id;
            },
          }}
          editable={{
            type: 'multiple',
            editableKeys,
            onValuesChange(record, newDs) {
              isTableChangedRef.current = true;
            },

            actionRender: (row, config, defaultDoms) => {
              const actionButtons = [];
              if (row.parent_id) {
                actionButtons.push(
                  <Button
                    key="addNextChild"
                    size="small"
                    icon={<PlusOutlined />}
                    style={{ padding: '2px' }}
                    onClick={(e) => {
                      addNewItem(
                        'child',
                        row,
                        dataSource,
                        setDataSource,
                        (newOne, newDataSource) => {
                          setDataSource(newDataSource);
                          setEditableRowKeys((prev) => [...(prev || []), newOne.id as React.Key]);
                        },
                      );
                    }}
                  />,
                );
              } else {
                actionButtons.push(
                  <Button
                    key="addNext"
                    size="small"
                    icon={<PlusOutlined />}
                    style={{ padding: '2px', flex: '0 0 40px' }}
                    title="Add a new Pos"
                    onClick={(e) => {
                      addNewItem(
                        'parent',
                        row,
                        dataSource,
                        setDataSource,
                        (newOne, newDataSource) => {
                          setDataSource(newDataSource);
                          setEditableRowKeys((prev) => [...(prev || []), newOne.id as React.Key]);
                          if (!newOne.parent_id)
                            setExpandedRowKeys((prev) => [...(prev || []), newOne.id as React.Key]);
                        },
                      );
                    }}
                  />,
                );
              }

              return [...actionButtons, defaultDoms.delete];
            },
            onChange: setEditableRowKeys,
            deletePopconfirmMessage: 'Are you sure you want to delete?',
            onlyAddOneLineAlertMessage: 'You can only add one.',
            deleteText: <DeleteFilled style={{ marginLeft: 'auto' }} />,
          }}
        />
      </Card>
      <Card style={{ marginTop: 0 }} bodyStyle={{ padding: 0 }} bordered={false}>
        <ProForm
          formRef={formRef}
          grid
          onValuesChange={(changedValues, values) => {
            isFormChangedRef.current = true;
          }}
          onBlur={(e) => {
            console.log(e.target.name, ']:[', e.target.value);
            handleLiveFormSave();
          }}
          onFinish={async (formValues) => {
            const values = { ...formValues };

            // Get payment_term & term_duration
            if (values.payment_term_str) {
              const arr = values.payment_term_str.split('|');
              values.payment_term = (arr?.[0] ?? '').trim();
              values.payment_term_duration = Util.safeInt((arr?.[1] ?? '').trim());
            } else {
              values.payment_term = null;
              values.payment_term_duration = null;
            }

            editableFormRef.current?.validateFields().then((items) => {
              if (!values.action) {
                setLoading(true);
                handleUpdateData({
                  ...values,
                  order_out_items: dataSource,
                  order_no: orderOut?.order_no,
                })
                  .then((res) => {
                    props.loadOrderOut?.(orderOut?.order_no).finally(() => setLoading(false));
                    taskContext?.reloadInvoice();
                  })
                  .catch((e) => {
                    setLoading(false);
                    Util.error(e);
                  });
              } else if (values.action == 'invoice') {
                // console.log(formValues);
                // console.log(moment(formValues.invoice_date).isSame(moment(), 'days'));
                const createInvoice = () => {
                  setLoading(true);
                  handleCreateInvoice(sn(orderOut?.order_no), {
                    ...values,
                    order_no: orderOut?.order_no,
                    order_out_items: dataSource,
                  })
                    .then((res: any) => {
                      if (values.isFinal && res?.file_url) {
                        window.open(API_URL + res.file_url, 'blank');
                      } else {
                        notification.success({
                          message: (
                            <>
                              Invoice created successfully. <br />
                              View in Lex Office: &nbsp;
                              <a
                                href={getLexOfficeLink(values.action, res.id)}
                                target="_blank"
                                rel="noreferrer"
                              >
                                {res.record.lo_no ?? res.id.substring(0, 10) + '...'}
                              </a>
                            </>
                          ),
                          key: res.id,
                          duration: 0,
                        });
                      }
                      taskContext?.reloadInvoice();
                      props.loadOrderOut?.(orderOut?.order_no).finally(() => setLoading(false));
                    })
                    .catch((e) => {
                      setLoading(false);
                      Util.error(e);
                    });
                };

                if (moment(formValues.invoice_date).isSame(moment(), 'days')) {
                  createInvoice();
                } else {
                  Modal.confirm({
                    title: 'Invoice date is not equal to today!',
                    type: 'confirm',
                    content: (
                      <>
                        <div>Do you want to proceed?</div>
                        <div>
                          Current Date = {Util.dtToDMY(moment())}. <br />
                          Invoice Date = {Util.dtToDMY(moment(formValues.invoice_date))}.
                        </div>
                      </>
                    ),
                    onOk: (close) => {
                      createInvoice();
                    },
                  });
                }
              } else if (values.action == 'order_confirmation') {
                setLoading(true);
                handleCreateOrderConfirmation(sn(orderOut?.order_no), {
                  ...values,
                  order_no: orderOut?.order_no,
                  order_out_items: dataSource,
                })
                  .then((res: any) => {
                    if (values.isFinal && res?.file_url) {
                      window.open(API_URL + res.file_url, 'blank');
                    } else {
                      notification.success({
                        message: (
                          <>
                            Order confirmation created successfully. <br />
                            View in Lex Office: &nbsp;
                            <a
                              href={getLexOfficeLink(values.action, res.id)}
                              target="_blank"
                              rel="noreferrer"
                            >
                              {res.record.lo_no ?? res.id.substring(0, 10) + '...'}
                            </a>
                          </>
                        ),
                        key: res.id,
                        duration: 0,
                      });
                    }
                    taskContext?.reloadInvoice();
                    props.loadOrderOut?.(orderOut?.order_no).finally(() => setLoading(false));
                  })
                  .catch((e) => {
                    setLoading(false);
                    Util.error(e);
                  });
              } else if (values.action == 'quotation') {
                setLoading(true);
                handleCreateQuotation(sn(orderOut?.order_no), {
                  ...values,
                  order_no: orderOut?.order_no,
                  order_out_items: dataSource,
                })
                  .then((res: any) => {
                    if (values.isFinal && res?.file_url) {
                      window.open(API_URL + res.file_url, 'blank');
                    } else {
                      notification.success({
                        message: (
                          <>
                            Quotation created successfully. <br />
                            View in Lex Office: &nbsp;
                            <a
                              href={getLexOfficeLink(values.action, res.id)}
                              target="_blank"
                              rel="noreferrer"
                            >
                              {res.record.lo_no ?? res.id.substring(0, 10) + '...'}
                            </a>
                          </>
                        ),
                        key: res.id,
                        duration: 0,
                      });
                    }
                    taskContext?.reloadInvoice();
                    props.loadOrderOut?.(orderOut?.order_no).finally(() => setLoading(false));
                  })
                  .catch((e) => {
                    setLoading(false);
                    Util.error(e);
                  });
              }
            });
          }}
          submitter={{
            render(__) {
              return (
                <>
                  <Space size={8} wrap style={{ marginBottom: 16 }}>
                    <Button
                      key="submit"
                      type="primary"
                      size="small"
                      style={{ marginRight: 32 }}
                      onClick={() => {
                        formRef.current?.setFieldValue('isFinal', false);
                        formRef.current?.setFieldValue('action', undefined);
                        formRef.current?.setFieldValue('precedingVoucherId', null);
                        formRef.current?.submit();
                      }}
                    >
                      Save
                    </Button>
                    {invAddress && !invAddress.cust_no && !invAddress.lo_contact_id && (
                      <Button
                        key="createContact"
                        type="primary"
                        className="btn-green"
                        size="small"
                        title="Create a new contact in Lex Office"
                        onClick={() => {
                          const hide = message.loading('Creating a new contact in Lex Office', 0);
                          createContactInLo(orderOut?.order_no)
                            .then((res) => {
                              props.loadOrderOut?.(orderOut?.order_no);
                            })
                            .catch((err) => Util.error(err))
                            .finally(() => hide());
                        }}
                      >
                        Create a Contact
                      </Button>
                    )}
                    <Button
                      key="qutotation"
                      type="primary"
                      size="small"
                      onClick={() => {
                        formRef.current?.setFieldValue('isFinal', false);
                        formRef.current?.setFieldValue('action', 'quotation');
                        formRef.current?.setFieldValue('precedingVoucherId', null);
                        formRef.current?.submit();
                      }}
                    >
                      Create Quotation
                    </Button>
                    <Button
                      key="qutotationFinal"
                      type="primary"
                      size="small"
                      onClick={() => {
                        formRef.current?.setFieldValue('isFinal', true);
                        formRef.current?.setFieldValue('action', 'quotation');
                        formRef.current?.setFieldValue('precedingVoucherId', null);
                        formRef.current?.submit();
                      }}
                    >
                      Create Quotation(Final)
                    </Button>
                    <Button
                      key="orderConfirmation"
                      type="primary"
                      size="small"
                      onClick={() => {
                        formRef.current?.setFieldValue('isFinal', false);
                        formRef.current?.setFieldValue('action', 'order_confirmation');
                        formRef.current?.setFieldValue('precedingVoucherId', null);
                        formRef.current?.submit();
                      }}
                    >
                      Create Order Confirmation
                    </Button>
                    {/* <Button
                      key="orderConfirmationFinal"
                      type="primary"
                      size="small"
                      onClick={() => {
                        formRef.current?.setFieldValue('isFinal', true);
                        formRef.current?.setFieldValue('action', 'order_confirmation');
                        formRef.current?.setFieldValue('precedingVoucherId', null);
                        formRef.current?.submit();
                      }}
                    >
                      Create Order Confirmation (Final)
                    </Button> */}
                    <Button
                      key="invoic"
                      type="primary"
                      size="small"
                      onClick={() => {
                        formRef.current?.setFieldValue('isFinal', false);
                        formRef.current?.setFieldValue('action', 'invoice');
                        formRef.current?.setFieldValue('precedingVoucherId', null);
                        formRef.current?.submit();
                      }}
                    >
                      Create Invoic
                    </Button>
                    {orderOut?.lo_order_out_invoices_final_invoices?.length ? (
                      <Popconfirm
                        key="invoicFinal"
                        title={
                          <>
                            Are you sure you want to create a final invoic again?
                            {orderOut?.lo_order_out_invoices_final_invoices?.length ? (
                              <>
                                <br />
                                <br />
                                Existed invoices:{' '}
                                {orderOut?.lo_order_out_invoices_final_invoices.map((x) => (
                                  <a
                                    key={x.lo_id}
                                    // href={getLexOfficeLink(x.lo_type, x.lo_id)}
                                    href={urlLODocumentOnSystem(x.order_no, x.lo_document_file_id)}
                                    target="_blank"
                                    rel="noreferrer"
                                  >
                                    <Tag style={{ marginBottom: 4 }}>
                                      <span>{x.lo_no ?? x.lo_id?.substring(0, 6)}</span>
                                      <span> ({x.lo_status})</span>
                                    </Tag>
                                  </a>
                                ))}
                              </>
                            ) : undefined}
                          </>
                        }
                        okText="Yes"
                        cancelText="No"
                        overlayStyle={{ maxWidth: 300, minWidth: 300 }}
                        onConfirm={async () => {
                          formRef.current?.setFieldValue('isFinal', true);
                          formRef.current?.setFieldValue('action', 'invoice');
                          formRef.current?.setFieldValue('precedingVoucherId', null);
                          formRef.current?.submit();
                        }}
                      >
                        <Button key="invoicFinal" type="primary" size="small">
                          Create Invoice(Final)
                        </Button>
                      </Popconfirm>
                    ) : (
                      <Button
                        key="invoicFinal"
                        type="primary"
                        size="small"
                        onClick={() => {
                          formRef.current?.setFieldValue('isFinal', true);
                          formRef.current?.setFieldValue('action', 'invoice');
                          formRef.current?.setFieldValue('precedingVoucherId', null);
                          formRef.current?.submit();
                        }}
                      >
                        Create Invoice(Final)
                      </Button>
                    )}
                  </Space>
                  <div>
                    <Space size={8} wrap>
                      <Button
                        key="invoicFinal"
                        type="primary"
                        className="btn-green"
                        size="small"
                        onClick={() => {
                          setOpenConvertModal(true);
                        }}
                      >
                        View or Convert...
                      </Button>
                    </Space>
                  </div>
                </>
              );
            },
          }}
        >
          <div className="d-none">
            <ProFormText name="action" />
            <ProFormCheckbox name="isFinal" />
            <ProFormText name="precedingVoucherId" />
          </div>

          <ProFormText
            name="introduction"
            label="Introduction"
            placeholder="Introduction"
            colProps={{ span: 24 }}
          />

          <ProFormTextArea name="pickup" label="Pickup" placeholder="Pickup" />

          <SProFormDigit
            name="net_weight"
            label="Net weight"
            placeholder="Net weight"
            colProps={{ span: 4 }}
            fieldProps={{ precision: 3 }}
          />
          <SProFormDigit
            name="gross_weight"
            label="Gross weight"
            placeholder="Gross weight"
            colProps={{ span: 4 }}
            fieldProps={{ precision: 3 }}
          />
          <ProFormCheckbox
            label="EUR.1 Statement"
            name="is_eur_statement1"
            colProps={{ span: 4 }}
          />

          <ProFormText name="restriction" label="Restriction" placeholder="Restriction" />

          <SDatePicker
            name="invoice_date"
            label="Invoice date"
            placeholder="Shipping date"
            colProps={{ span: 4 }}
          />
          <SDatePicker
            name="shipping_date"
            label="Shipping date"
            placeholder="Shipping date"
            colProps={{ span: 4 }}
          />
          <ProFormSelect
            name="payment_term_str"
            label="Payment term"
            placeholder="Payment term"
            colProps={{ span: 8 }}
            options={paymentTermOptions}
          />
          <div className="hidden">
            <ProFormText
              name="payment_term"
              label="Payment term"
              placeholder="Payment term"
              colProps={{ span: 16 }}
            />
            <SProFormDigit
              name="payment_term_duration"
              label="Payment term duration"
              placeholder="Payment term duration"
              colProps={{ span: 4 }}
            />
          </div>

          {/* <ProFormText name="remark" label="Remark" placeholder="Remark" colProps={{ span: 24 }} /> */}
          <Col span={24}>
            <ProFormDependency name={['is_eur_statement1']}>
              {(depValues) => {
                return depValues.is_eur_statement1 == '1' ? (
                  <ProForm.Item
                    name="remark"
                    label="Remark"
                    wrapperCol={{ span: 24 }}
                    labelCol={{ span: 24 }}
                    style={{ marginBottom: 8 }}
                  >
                    {getCodeValue(DictCode.CODE_ORDER_OUT_EUR_STATEMENT1)}
                  </ProForm.Item>
                ) : (
                  <ProForm.Item
                    name="remark"
                    label="Remark"
                    tooltip={
                      'Please insert a pre-defined text by starting to type "#" and numbers. Insert by pressing "space key" or by selection.'
                    }
                    wrapperCol={{ span: 24 }}
                    labelCol={{ span: 24 }}
                  >
                    <SMentionsTextFormField
                      prefix="#"
                      onSelect={(data: any, prefix: string) => {
                        const oldValue = formRef.current?.getFieldValue('remark') || '';
                        formRef.current?.setFieldValue(
                          'remark',
                          oldValue?.replaceAll(`#${data.value}`, data?.value) ?? '',
                        );
                      }}
                    />
                  </ProForm.Item>
                );
              }}
            </ProFormDependency>
          </Col>

          <SProFormDigit
            name="tax_rate"
            label="Tax Rate"
            placeholder="Tax Rate"
            colProps={{ span: 4 }}
            fieldProps={{ precision: 0 }}
            addonAfter="%"
            zeroShow
          />
        </ProForm>
      </Card>
      {orderOut?.order_no && (
        <ConvertModal
          orderNo={orderOut?.order_no}
          parentFormRef={formRef}
          modalVisible={openConvertModal}
          handleModalVisible={setOpenConvertModal}
        />
      )}
    </>
  );
};

export default OrderOutInvoice;
