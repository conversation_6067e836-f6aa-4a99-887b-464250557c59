import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { Alert, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { ProFormText, LoginForm } from '@ant-design/pro-form';
import { history, useModel } from 'umi';
import Footer from '@/components/Footer';
import { login } from '@/services/app/login';
import styles from './index.less';
import { LS_TOKEN_NAME } from '@/constants';
import ImgLogo from '@/assets/images/favicon.png';

const LoginMessage: React.FC<{
  content: string;
}> = ({ content }) => (
  <Alert
    style={{
      marginBottom: 24,
    }}
    message={content}
    type="error"
    showIcon
  />
);

const Login: React.FC = () => {
  const [errorMsg, setErrorMsg] = useState('');
  const { initialState, setInitialState } = useModel('@@initialState');
  const token = localStorage.getItem(LS_TOKEN_NAME);

  const fetchUserInfo = async () => {
    const userInfo = await initialState?.fetchUserInfo?.();

    if (userInfo) {
      await setInitialState((s: any) => ({ ...s, currentUser: userInfo }));
    }
  };

  useEffect(() => {
    if (token) {
      history.push('/');
    }
  }, [token]);

  const handleSubmit = async (values: API.LoginParams) => {
    try {
      const res = await login({ ...values, type: 'account' });

      const defaultLoginSuccessMessage = 'Login successful!';
      message.success(defaultLoginSuccessMessage);
      localStorage.setItem(LS_TOKEN_NAME, res.Authorization || '');
      await fetchUserInfo();

      if (!history) return;
      const { query } = history.location;
      const { redirect } = query as {
        redirect: string;
      };
      history.push(redirect || '/');
      return;
    } catch (error: any) {
      console.error(error);
      setErrorMsg('Incorrect username or password');
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <LoginForm
          // logo={<img src={"%PUBLIC_URL%/images/favicon.png"} />}
          logo={<img src={ImgLogo} />}
          title="WHC Task"
          subTitle={' '}
          initialValues={{
            autoLogin: true,
          }}
          onFinish={async (values) => {
            await handleSubmit(values as API.LoginParams);
          }}
          submitter={{ searchConfig: { submitText: 'Login' } }}
        >
          {errorMsg && <LoginMessage content={errorMsg} />}

          <ProFormText
            name="username"
            fieldProps={{
              size: 'large',
              prefix: <UserOutlined className={styles.prefixIcon} />,
            }}
            placeholder={'Username'}
            rules={[
              {
                required: true,
                message: 'Please input your username!',
              },
            ]}
          />
          <ProFormText.Password
            name="password"
            fieldProps={{
              size: 'large',
              prefix: <LockOutlined className={styles.prefixIcon} />,
            }}
            placeholder={'Password'}
            rules={[
              {
                required: true,
                message: 'Please input your password!',
              },
            ]}
          />

          <div
            style={{
              marginBottom: 24,
            }}
          >
            {/* <ProFormCheckbox noStyle name="autoLogin">
              Remember me
            </ProFormCheckbox> */}
            {/*<a
              style={{
                float: 'right',
              }}
            >
              Forgot Password ?
            </a>*/}
          </div>
        </LoginForm>
      </div>
      <Footer />
    </div>
  );
};

export default Login;
