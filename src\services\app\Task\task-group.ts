/* eslint-disable */
import Util from '@/util';
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/task/group';

/** task GET /api/task/group */
export async function getTaskGroupList(
  params: Partial<API.PageParams & API.TaskGroup>,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.TaskGroup>> {
  return request<API.Result<API.TaskGroup>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort: sort ?? { sortMore: 'ascend' },
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** task GET /api/task/group/{id} */
export async function getTaskGroup(
  params: Partial<API.PageParams & API.TaskGroup>,
  options?: { [key: string]: any },
) {
  return request<API.Result<API.TaskGroup>>(`${urlPrefix}/${params.id}`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
    paramsSerializer,
    withToken: true,
  }).then((res) => res.message);
}

/** put PUT /api/task/group */
export async function updateTaskGroup(
  data: Partial<API.TaskGroup> & { _action?: 'partialUpdate' },
  options?: { [key: string]: any },
) {
  return request<API.ResultObject<API.TaskGroup>>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** put PUT /api/task/group/copy/{id} */
export async function copyBlocks(id?: number, targetId?: number, data?: any) {
  return request<API.ResultObject<API.TaskGroup>>(`${urlPrefix}/copy/${id}/${targetId}`, {
    method: 'PUT',
    data: data,
  }).then((res) => res.message);
}

/** post POST /api/task/group */
export async function addTaskGroup(data: Partial<API.TaskGroup>, options?: { [key: string]: any }) {
  return request<API.ResultObject<API.TaskGroup>>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  })
    .then((res) => res.message)
    .catch((reason) => Util.error(reason));
}

/** post POST /api/task/group/sub */
export async function addSubTaskGroup(
  data: Partial<API.TaskGroup>,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject<API.TaskGroup>>(`${urlPrefix}/sub`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  })
    .then((res) => res.message)
    .catch((reason) => { Util.error(reason); return undefined });
}

/** post POST /api/task/group/reserved */
export async function createReservedTaskGroup(
  data?: Partial<API.TaskGroup>,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject<API.TaskGroup[]>>(`${urlPrefix}/reserved`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  })
    .then((res) => res.message)
    .catch((reason) => Util.error(reason));
}

/** delete DELETE /api/task/group/{id} */
export async function deleteTaskGroup(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}

export async function getTaskGroupACList(
  params?: Partial<API.PageParams & API.TaskGroup>,
  sort?: any,
  filter?: any,
) {
  return getTaskGroupList({ ...params, pageSize: 1000 }, sort, filter).then((res) => res.data);
}
