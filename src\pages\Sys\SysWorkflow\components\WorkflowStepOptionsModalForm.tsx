import type { Dispatch, SetStateAction } from 'react';
import { useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { Button, message, Modal, Popconfirm, Space } from 'antd';
import Util from '@/util';
import { DeleteFilled, DeleteOutlined, EditOutlined, SaveOutlined } from '@ant-design/icons';
import type { ActionType, EditableFormInstance, ProColumns } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import type { DefaultOptionType } from 'antd/lib/select';
import { updateSysWorkflowStep } from '@/services/app/Sys/sys-workflow-step';

const getNewRecord = () => {
  const uid = Util.genNewKey('key_');
  const newRecord = {
    uid: uid,
    value: '',
    label: '',
  };
  return newRecord;
};

export type FormValueType = Partial<API.SysWorkflow>;

export type WorkflowStepOptionsModalFormProps = {
  initialValues: Partial<API.SysWorkflowStep>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onCancel?: (flag?: boolean, formVals?: FormValueType) => void;
  onSaveCallback?: (id?: number, data?: Partial<API.SysWorkflowStep>) => Promise<void>;
};

const WorkflowStepOptionsModalForm: React.FC<WorkflowStepOptionsModalFormProps> = (props) => {
  const { initialValues, onSaveCallback } = props;

  const actionRef = useRef<ActionType>();
  const editableFormRef = useRef<EditableFormInstance<DefaultOptionType>>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<DefaultOptionType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    if (!props.modalVisible) return;
    const options = (initialValues.options ?? []).map((x, index) => ({
      uid: Util.genNewKey('key_'),
      ...x,
    }));
    if (actionRef.current && initialValues.id) {
      actionRef.current.reload();
      setDataSource(options);
      setEditableRowKeys(options.map((x) => x.uid));
    } else {
      setEditableRowKeys([]);
    }
  }, [props.modalVisible, initialValues.id, initialValues.options]);

  const columns: ProColumns<DefaultOptionType>[] = [
    {
      title: 'Value',
      dataIndex: 'value',
      width: 280,
      tooltip: 'recommended to be unique in the list or set it same as a label',
      fieldProps: { size: 'small' },
    },
    {
      title: 'Label',
      dataIndex: 'label',
      fieldProps: { size: 'small' },
    },
    {
      title: '',
      valueType: 'option',
      width: 50,
      render: (text, record, __, action) => (
        <Space>
          <a
            key="edit"
            type="link"
            onClick={() => {
              action?.startEditable?.(`${record.value}`);
            }}
          >
            <EditOutlined />
          </a>
          <Popconfirm
            key="delete"
            className="c-red"
            title={<>Are you sure you want to delete?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ width: 300 }}
            onConfirm={async (e) => {
              setDataSource((prev) => prev.filter((x) => x.value != record.value));
            }}
          >
            <DeleteOutlined />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleSave = async (isClose?: boolean) => {
    if (!initialValues.id) {
      message.error('Step ID is invalid.');
      return;
    }
    const values = editableFormRef.current?.getRowsData?.() || [];

    setLoading(true);
    updateSysWorkflowStep(initialValues.id, { options: values })
      .then((res) => {
        message.success('Saved successfully.');
        onSaveCallback?.(initialValues.id, res);
        if (isClose) {
          props.handleModalVisible(false);
        }
      })
      .catch((e) => Util.error(e))
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <Modal
      title={'Update options'}
      width="600px"
      open={props.modalVisible}
      onCancel={() => props.handleModalVisible(false)}
      footer={
        <Space>
          <Button
            type="primary"
            onClick={() => {
              handleSave();
            }}
            icon={<SaveOutlined />}
          >
            Save
          </Button>
          <Button
            type="primary"
            onClick={() => {
              handleSave(true);
            }}
          >
            Save & Close
          </Button>
          <Button
            onClick={() => {
              props.handleModalVisible(false);
            }}
          >
            Cancel
          </Button>
        </Space>
      }
    >
      <EditableProTable<DefaultOptionType>
        loading={loading}
        rowKey="uid"
        scroll={{
          x: 400,
        }}
        style={{ padding: 0 }}
        cardProps={{ bodyStyle: { padding: 0 } }}
        actionRef={actionRef}
        editableFormRef={editableFormRef}
        size="small"
        recordCreatorProps={{
          newRecordType: 'dataSource',
          position: 'bottom',
          creatorButtonText: 'Add new row',
          record: (index: number, dataSource2: DefaultOptionType[]) => {
            return getNewRecord();
          },
        }}
        columns={columns}
        value={dataSource}
        onChange={setDataSource}
        editable={{
          type: 'multiple',
          editableKeys,
          actionRender: (row, config, defaultDoms) => {
            return [defaultDoms.delete];
          },
          onValuesChange: (record, recordList) => {
            setDataSource(recordList);
          },
          onChange: setEditableRowKeys,
          deletePopconfirmMessage: 'Are you sure you want to delete?',
          onlyAddOneLineAlertMessage: 'You can only add one.',
          deleteText: <DeleteFilled />,
        }}
        locale={{
          emptyText: <></>,
        }}
      />
    </Modal>
  );
};

export default WorkflowStepOptionsModalForm;
