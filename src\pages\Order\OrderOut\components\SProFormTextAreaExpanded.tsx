import type { ProFormInstance, ProFormItemProps } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import { Button } from 'antd';
import type { TextAreaProps } from 'antd/lib/input';
import { useRef, useState } from 'react';
import TextSelectForm from './TextSelectForm';

export type SProFormTextAreaExpandedProps = {
  name: string;
  formRef: React.RefObject<ProFormInstance<any> | undefined>;
} & ProFormItemProps<TextAreaProps, any>;

const SProFormTextAreaExpanded: React.FC<SProFormTextAreaExpandedProps> = (props) => {
  const inputRef = useRef<any>();
  const [selection, setSelection] = useState<number[]>([0, 0]);
  const [selectTextVisible, setSelectTextVisible] = useState<boolean>(false);

  const handleDescEvent = (e: any) => {
    const ele = e.target as any;
    setSelection([ele.selectionStart, ele.selectionEnd, e.clientX, e.clientY]);
  };

  return (
    <>
      <ProFormTextArea
        {...props}
        formItemProps={{ style: { marginBottom: 0 } }}
        fieldProps={{
          ref: inputRef,
          onKeyUp: (e) => {
            handleDescEvent(e);
          },
          onMouseUp: (e) => {
            handleDescEvent(e);
          },
        }}
      />
      <Button
        type="link"
        size="small"
        onClick={() => setSelectTextVisible(true)}
        style={{ display: 'flex', marginTop: 4, marginLeft: 'auto' }}
      >
        Insert a defined Text...
      </Button>
      <TextSelectForm
        modalVisible={selectTextVisible}
        handleModalVisible={setSelectTextVisible}
        onSubmit={async (value) => {
          const formValue = props.formRef.current?.getFieldValue('desc') ?? '';
          props.formRef.current?.setFieldValue(
            'desc',
            formValue.substring(0, selection[0]) + value + formValue.substring(selection[1]),
          );
          setSelectTextVisible(false);
        }}
        onCancel={() => setSelectTextVisible(false)}
      />
    </>
  );
};

export default SProFormTextAreaExpanded;
