import { DT_FORMAT_YMD } from '@/constants';
import { Card, Col, Row } from 'antd';
import moment from 'moment';
import { useEffect, useState } from 'react';
import type { ReactNode } from 'react-markdown/lib/react-markdown';
import { useModel } from 'umi';
import type { onTaskClickType } from '../..';
import Task from './Task';

const gridStyleBase: React.CSSProperties = {
  padding: '12px',
};

const gridStyle: React.CSSProperties = {
  ...gridStyleBase,
  // width: '14.28%',
  width: '16.666666%',
  padding: '12px',
  border: 'none',
};

const gridStyle2: React.CSSProperties = {
  ...gridStyleBase,
  padding: '12px 8px',
  width: '50%',
  border: 'none',
  boxShadow: 'none',
};

type WeekGridProps = {
  baseDate?: string;
  dates: string[]; // YYYY-MM-DD in a week.
  navigationComp?: ReactNode;
  onTaskClick: onTaskClickType;
};
const WeekGrid: React.FC<WeekGridProps> = ({
  baseDate,
  dates: dtRanges,
  navigationComp,
  onTaskClick,
}) => {
  const { data } = useModel('task-calendar');

  return (
    <>
      <Row gutter={32} wrap={false} className="calendar">
        <Col flex="auto">
          <Card size="small" style={{ marginBottom: 56 }}>
            {dtRanges.slice(0, 6).map((r, index) => {
              const title =
                index < 5
                  ? moment(r).format('dd DD.MM.')
                  : `${moment(r).format('dd')} & ${moment(dtRanges[6]).format('dd')} ${moment(
                      r,
                    ).format('DD.MM.')} / ${moment(dtRanges[6]).format('DD.MM.')}`;
              const list =
                index < 5
                  ? data.taskByDateKey[r] ?? []
                  : [...(data.taskByDateKey[r] ?? []), ...(data.taskByDateKey[dtRanges[6]] ?? [])];
              return (
                <>
                  <Card.Grid key={r} hoverable={false} style={gridStyle} className="calendar-date">
                    <div className="header-ym">{title}</div>
                    {list?.map((x: API.Task, ind) => {
                      return (
                        <Task
                          key={`${x.id}_${ind}`}
                          task={x}
                          index={0}
                          droppableId={''}
                          onTaskClick={onTaskClick}
                        />
                      );
                    })}
                  </Card.Grid>
                </>
              );
            })}
            <Card.Grid hoverable={false} style={gridStyle2} className="calendar-order-card">
              <Row gutter={16}>
                {data.taskInOrders
                  ?.filter(
                    (x) =>
                      x?.ui_type_key &&
                      x?.ui_type_key >= dtRanges[0] &&
                      x?.ui_type_key <= dtRanges[6],
                  )
                  ?.map((x, ind) => {
                    return (
                      <Col key={x.id} xs={24} sm={24} md={24} lg={12} xl={12}>
                        <Task
                          key={`${x.id}_${ind}`}
                          task={x}
                          index={0}
                          droppableId={''}
                          onTaskClick={onTaskClick}
                        />
                      </Col>
                    );
                  })}
              </Row>
            </Card.Grid>
            <Card.Grid hoverable={false} style={gridStyle2} className="calendar-general-card">
              <Row gutter={16}>
                {data.taskGeneral
                  ?.filter(
                    (x) =>
                      x?.ui_type_key &&
                      x?.ui_type_key >= dtRanges[0] &&
                      x?.ui_type_key <= dtRanges[6],
                  )
                  ?.map((x) => {
                    return (
                      <Col key={x.id} xs={24} sm={24} md={24} lg={12} xl={12}>
                        <Task
                          key={x.id}
                          task={x}
                          index={0}
                          droppableId={''}
                          onTaskClick={onTaskClick}
                        />
                      </Col>
                    );
                  })}
              </Row>
            </Card.Grid>
          </Card>
        </Col>
        <Col flex="0 0 150px">
          <div>Week No: {moment(baseDate).isoWeek()}</div>
          {navigationComp}
        </Col>
      </Row>
    </>
  );
};
export default WeekGrid;
