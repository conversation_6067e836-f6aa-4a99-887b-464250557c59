import React, { useEffect, useRef } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util from '@/util';
import { getOrderOutSupWorkflowStepLogList } from '@/services/app/Order/order-out-sup-workflow-step-log';
import _, { isArray } from 'lodash';
import { Tag } from 'antd';
import { YNOptionsStr } from '@/constants';

type OrderOutSupWorkflowStepLogListProps = {
  pageSize?: number;
  hidePageContainer?: boolean;
  step_id?: number;
  order_no?: number;
  supplier_id?: number;
  open?: boolean;
  refreshTick?: number;
};

const OrderOutSupWorkflowStepLogList: React.FC<OrderOutSupWorkflowStepLogListProps> = (props) => {
  const actionRef = useRef<ActionType>();

  useEffect(() => {
    if (!props.open) return;
    if (props.step_id && props.order_no && props.supplier_id) {
      actionRef.current?.reload();
    }
  }, [props.step_id, props.order_no, props.supplier_id, props.open]);

  useEffect(() => {
    if (!props.refreshTick) return;
    actionRef.current?.reload();
  }, [props.refreshTick]);

  const columns: ProColumns<API.OrderOutSupWorkflowStepLog>[] = [
    {
      title: 'Step',
      dataIndex: ['step', 'desc'],
      sorter: true,
      valueType: 'text',
      search: false,
    },
    {
      title: 'Updated value',
      sorter: true,
      dataIndex: ['new_value'],
      valueType: 'text',
      search: false,
      render: (dom, record) => {
        const value = Util.safeJsonParse(record.new_value);
        if (record.step?.field_type == 'switch') {
          return _.get(_.find(YNOptionsStr, { value: +value ? '1' : '0' }), 'label');
        }
        if (value) {
          if (record.step?.field_type == 'checkbox' || record.step?.field_type == 'multiselect') {
            return isArray(value)
              ? value.map((x, index) => (
                  // eslint-disable-next-line react/no-array-index-key
                  <Tag key={`${index}`}>
                    {_.get(_.find(record.step?.options, { value: x }), 'label')}
                  </Tag>
                ))
              : value;
          } else if (record.step?.field_type == 'radio' || record.step?.field_type == 'select') {
            return _.get(_.find(record.step?.options, { value: value }), 'label');
          } else if (record.step?.field_type == 'date') {
            return Util.dtToDMY(value);
          } else {
            return value;
          }
        }
        return undefined;
      },
    },
    {
      title: 'User',
      sorter: true,
      dataIndex: ['user', 'username'],
      valueType: 'text',
      showSorterTooltip: false,
      search: false,
    },
    {
      title: 'Updated on',
      sorter: true,
      className: 'text-sm c-grey',
      dataIndex: ['created_on'],
      valueType: 'text',
      search: false,
      showSorterTooltip: false,
      width: 100,
      render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
    },
  ];

  const renderBody = () => (
    <>
      <ProTable<API.OrderOutSupWorkflowStepLog, API.PageParams>
        headerTitle={'Latest Change History'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: false, density: false, search: false, setting: false }}
        size="small"
        search={false}
        toolBarRender={() => []}
        pagination={{
          hideOnSinglePage: true,
          showSizeChanger: false,
          pageSize: props.pageSize ?? 15,
        }}
        cardProps={{
          bodyStyle: { padding: 0, paddingTop: 12 },
        }}
        locale={{ emptyText: <></> }}
        request={(params, sort, filter) => {
          return getOrderOutSupWorkflowStepLogList(
            {
              ...params,
              pageSize: 40,
              order_no: props?.order_no,
              step_id: props?.step_id,
              supplier_id: props.supplier_id,
            },
            { ...sort, id: 'descend' },
            filter,
          ) as any;
        }}
        columns={columns}
      />
    </>
  );

  return props.hidePageContainer ? renderBody() : <PageContainer>{renderBody()}</PageContainer>;
};

export default OrderOutSupWorkflowStepLogList;
