import Util from '@/util';
import { Col, Row, Tag, Typography } from 'antd';
import TaskActions from '../Sub/TaskActions';
import type { TaskProps } from '../Task';

type TaskLayoutTProps = TaskProps;

const TaskLayoutT: React.FC<TaskLayoutTProps> = (props) => {
  const { task } = props;
  return (
    <>
      <Row wrap={false} gutter={16} className="ant-row-middle">
        <Col flex={'0 0 100px'}>
          {task.date && <Tag color="lime">{Util.dtToDMY(task.date)}</Tag>}
        </Col>
        <Col flex={'auto'}>
          <Typography.Paragraph ellipsis={true} style={{ marginBottom: 0 }}>
            {task.title}
          </Typography.Paragraph>
        </Col>
        <Col flex={'0 0 50px'} className="text-right">
          <span className="text-sm c-grey">#{task.id}</span>
        </Col>
        <TaskActions {...props} />
      </Row>
    </>
  );
};

export default TaskLayoutT;
