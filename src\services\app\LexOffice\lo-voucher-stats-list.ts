/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/lo-voucherlist';


/**  
 * Get stats
 * 
 * GET /api/lo-voucherlist/stat
 * 
 * */
export async function getLoInvoiceCheckStatList(
  params: API.PageParams & { lo_types: string[] },
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.LoInvoiceCheck>> {
  return request<API.Result<API.LoInvoiceCheck>>(`${urlPrefix}/stat`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    summary: res.message.summary,
  }));
}

/**  
 * Get stats
 * 
 * GET /api/lo-voucherlist/export-stat-xls
 * 
 * */
export async function exportLoInvoiceCheckStatList(
  params: API.PageParams & { lo_types: string[] },
  sort?: any,
  filter?: any,
) {
  return request<API.ResultDownloadable>(`${urlPrefix}/export-stat-xls`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}
