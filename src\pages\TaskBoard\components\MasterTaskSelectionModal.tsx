import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import { Button, message, Modal } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormDigit } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import { sendTaskToSubGroup } from '@/services/app/Task/task';
import Util, { sn } from '@/util';
import _ from 'lodash';
import { LinkOutlined, SelectOutlined } from '@ant-design/icons';

export type FormValueType = Partial<API.Task>;

export type MasterTaskSelectionModalProps = {
  task?: Partial<API.Task>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  reloadParent?: (newTask?: API.Task) => void;
};

const MasterTaskSelectionModal: React.FC<MasterTaskSelectionModalProps> = (props) => {
  const { task } = props;

  // form
  const formRef = useRef<ProFormInstance>();

  return (
    <Modal
      title={
        <>
          <SelectOutlined className="c-blue" /> Move selected task into subtask
        </>
      }
      width="500px"
      open={props.modalVisible}
      onCancel={() => props.handleModalVisible(false)}
      footer={
        <Button
          type="primary"
          onClick={() => {
            formRef.current?.validateFields([['target_task_id']]).then((values) => {
              const hide = message.loading('Moving the task to selected target...', 0);
              sendTaskToSubGroup(sn(task?.id), sn(values?.target_task_id), sn(task?.block_id), {})
                .then((newTask) => {
                  message.success('Moved successfully.');
                  props.handleModalVisible(false);
                  props.reloadParent?.(newTask);
                })
                .catch((e) => Util.error(e))
                .finally(() => hide());
            });
          }}
          icon={<LinkOutlined />}
        >
          Move into sub task
        </Button>
      }
    >
      <ProForm formRef={formRef} layout="vertical" submitter={false}>
        <div style={{ marginBottom: 8 }}>Source Task:</div>
        <div style={{ marginBottom: 32 }}>{`#${task?.id} - ${task?.title}`}</div>

        <ProFormDigit
          name="target_task_id"
          label="Target Task ID"
          required
          rules={[
            {
              required: true,
              message: 'Target task ID is required',
            },
          ]}
        />
        {/* <ProFormSelect
          name="target_task_id"
          label="Target Task"
          required
          showSearch
          mode="single"
          debounceTime={200}
          rules={[
            {
              required: true,
              message: 'Target task is required',
            },
          ]}
          request={async (params) => {
            const newParams = {
              ...params,
              pageSize: 200,
            };
            return getTaskList(newParams).then((res) =>
              res.data.map((email) => ({
                value: email.id,
                label: `${email.subject} - ${email.text_plain}`,
              })),
            );
          }}
        /> */}
      </ProForm>
    </Modal>
  );
};

export default MasterTaskSelectionModal;
