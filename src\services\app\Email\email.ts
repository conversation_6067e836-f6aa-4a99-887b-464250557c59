/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/email';

/** rule GET /api/email */
export async function getEmailList(
  params: API.PageParams & Partial<API.Email>,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.Email>> {
  return request<API.Result<API.Email>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}
export async function getEmail(id?: number, params?: Record<string, any>): Promise<API.Email> {
  return request<API.ResultObject<API.Email>>(`${urlPrefix}`, {
    method: 'GET',
    params,
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}

/** put PUT /api/email */
export async function updateEmail(data: Partial<API.Email>, options?: { [key: string]: any }) {
  return request<API.Email>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/email */
export async function addEmail(data: API.Email, options?: { [key: string]: any }) {
  return request<API.Email>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/email/ds */
export async function dsPullEmail(data: API.Email, options?: { [key: string]: any }) {
  return request<API.Email>(`${urlPrefix}/ds`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/email/{id} */
export async function deleteEmail(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** 
 * Move email attachments
 * 
 * PUT /api/email 
 */
export async function moveEmailAttachments(id: number) {
  return request<API.ResultObject<boolean>>(`${urlPrefix}/${id}/move`, {
    method: 'PUT',
    data: { id },
  });
}
