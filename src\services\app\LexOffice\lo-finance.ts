/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/lo-finance';

export type LoFinanceStatType = {
  book_max_ym: null | string; // e.g. 2022-12-31 or 2023-02-28
  book_max_ym_last_month: null | string; // e.g. 2022-12-31 or 2023-02-28

  book_last_balance: number;

  // New values
  inv_new_sum: number;
  creditnote_new_sum: number;
  payment_new_sum: number;
  allocation_new_sum: number;

  // double check
  inv_sum: number;
  creditnote_sum: number;
  payment_sum: number;
  allocation_sum: number;

  // double check with checked
  inv_sum_checked: number;
  inv_sum_unchecked: number;
  creditnote_sum_checked: number;
  creditnote_sum_unchecked: number;
  payment_sum_checked: number;
  payment_sum_unchecked: number;
  allocation_sum_checked: number;
  allocation_sum_unchecked: number;
} & { sqls?: any[] }

/** 
 * GET /api/lo-finance/stat 
 * 
 * Get financial stats summary
*/
export async function getLoFinanceStat(params?: {
  customerId?: number;
  supplierId?: number;

  // extra filter
  start_date?: string;
  end_date?: string;
}): Promise<LoFinanceStatType> {
  return request<API.ResultObject<LoFinanceStatType>>(`${urlPrefix}/stat`, {
    method: 'GET',
    params,
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}

/** 
 * GET /api/lo-finance/export-stat 
 * 
 * Get financial stats summary
*/
export async function exportLoFinanceStat(params?: {
  customerId?: number;
  supplierId?: number;

  // extra filter
  start_date?: string;
  end_date?: string;
}): Promise<API.Downloadable> {
  return request<API.ResultDownloadable>(`${urlPrefix}/export-stat`, {
    method: 'GET',
    params,
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}



export type CSAType = Partial<(API.Customer | API.Supplier) & { uid: string, value: string, label: string }>;
export type ParsedCSAType = {
  id?: number;
  customerId?: number;
  supplierId?: number;
  account_no?: number | string;
};

/** 
 * GET /api/lo-finance/customer-supplier-ac 
 * 
 * Get next customer or supplier by current uid and direction.
 * e.g. uid distinguished by 's' or 'c': "s100,999" or "c200,888":  999 / 888 is account no 
*/
export async function getCustomerOrSupplierAccountNoACList(params?: any): Promise<CSAType[]> {
  return request<API.ResultObject<any>>(`${urlPrefix}/customer-supplier-ac`, {
    method: 'GET',
    params,
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}

/**
 * Get object from UID formed in "customer/supplier/accountNo"
 * @param uid 
 * @returns 
 */
export function parseCSAItem(uid: string): ParsedCSAType {
  const arr = uid.split(',');
  const parsedObject: ParsedCSAType = {};
  if (arr[0][0] == 's') {
    parsedObject.supplierId = +arr[0].substring(1);
    parsedObject.id = parsedObject.supplierId;
  } else {
    parsedObject.customerId = +arr[0].substring(1);
    parsedObject.id = parsedObject.customerId;
  }
  parsedObject.account_no = arr[1];

  return parsedObject;
}

/** 
 * GET /api/lo-finance/next-customer 
 * 
 * Get next customer or supplier by current uid and direction.
 * e.g. uid distinguished by 's' or 'c': "s100" or "c200" 
*/
export async function getNextCustomerOrSupplier(dir: number, currentUid?: string): Promise<CSAType> {
  return request<API.ResultObject<any>>(`${urlPrefix}/next-customer`, {
    method: 'GET',
    params: {
      dir,
      uid: currentUid,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}
