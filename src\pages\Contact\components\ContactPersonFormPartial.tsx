import type { ContactType } from '@/constants';
import { ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { Col, Row } from 'antd';

type ContactPersonFormPartialProps = {
  contact_type?: ContactType;
};
const ContactPersonFormPartial: React.FC<ContactPersonFormPartialProps> = (props) => {
  return (
    <>
      <Row gutter={16}>
        <Col span={14}>
          <ProFormText width="md" name="first_name" label="Name" placeholder="First name" />
        </Col>
        <Col span={10}>
          <ProFormText width="md" name="last_name" label="" placeholder="Last name" />
        </Col>
      </Row>
      <ProFormText name="position" label="Position" placeholder="Position" />
      <Row gutter={16}>
        <Col span={12}>
          <ProFormText name="telephone" label="Telephone" placeholder="Telephone" />
        </Col>
        <Col span={12}>
          <ProFormText width="md" name="mobile" label="Mobile" placeholder="Mobile" />
        </Col>
      </Row>
      <ProFormText width="md" name="fax" label="Fax" placeholder="Fax" />
      <ProFormText name="email" dataFormat="email" label="Email" placeholder="Email" />
      <ProFormText name="email_alt" label="Additional Email(s)" placeholder="Additional Email(s)" />
      <ProFormText name="language" label="Language" placeholder="Language" />
      <ProFormTextArea name="misc" label="Misc" placeholder="Misc" />
    </>
  );
};
export default ContactPersonFormPartial;
