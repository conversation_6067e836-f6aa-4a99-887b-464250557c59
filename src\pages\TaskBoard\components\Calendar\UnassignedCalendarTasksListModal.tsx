import { LinkOutlined } from '@ant-design/icons';
import { Row, Col, Typography, Modal } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect } from 'react';
import React, { useRef } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import { urlFull } from '@/util';
import { DEFAULT_PER_PAGE_PAGINATION, DictCode } from '@/constants';
import { getUnassignedCalendarTaskList } from '@/services/app/Task/task';
import { useModel } from 'umi';

type UnassignedCalendarTasksListModalPropsType = {
  visible: boolean;
  setVisible: Dispatch<SetStateAction<boolean>>;
};

const UnassignedCalendarTasksListModal: React.FC<UnassignedCalendarTasksListModalPropsType> = ({
  visible,
  setVisible,
}) => {
  const { getCodeValue } = useModel('app-settings');

  const actionRef = useRef<ActionType>();

  const columns: ProColumns<API.Task>[] = [
    {
      title: 'Order No',
      dataIndex: ['ref', 'order_no'],
      sorter: true,
      hideInSearch: false,
      width: 80,
      render: (dom, entity) => {
        return dom;
      },
    },
    {
      title: 'Customer',
      dataIndex: ['ref', 'customer'],
      sorter: true,
      hideInForm: true,
    },
    {
      title: 'Lotus Notes ID',
      dataIndex: ['ref', 'lotus_notes_id'],
      sorter: true,
      hideInForm: true,
      ellipsis: true,
      render: (dom, record) =>
        record.ref?.lotus_notes_id ? (
          <Row wrap={false}>
            <Col flex={'auto'}>
              <Typography.Text ellipsis> {record.ref?.lotus_notes_id}</Typography.Text>
            </Col>
            <Col flex="0 0 40px">
              <a
                href={(getCodeValue(DictCode.LOTUS_PATH) ?? '').replace(
                  '{lotusNotesId}',
                  record.ref?.lotus_notes_id,
                )}
                title="Open Lotus link"
              >
                L
              </a>
              <a
                href={urlFull('/order-out-detail/lotus/' + record.ref?.lotus_notes_id)}
                target="_blank"
                rel="noreferrer"
                title="Open order out detail"
                style={{ marginLeft: 8 }}
              >
                <LinkOutlined />
              </a>
            </Col>
          </Row>
        ) : undefined,
    },
    {
      title: 'Description',
      dataIndex: ['ref', 'desc'],
      sorter: true,
      hideInForm: true,
      ellipsis: true,
    },
    {
      title: 'Latest comment',
      dataIndex: ['latest_task_comment', 'comment'],
      search: false,
      width: 250,
      className: '',
    },
  ];

  useEffect(() => {
    if (visible) {
      actionRef.current?.reload();
    }
  }, [visible]);

  return (
    <Modal
      open={visible}
      onCancel={(e) => setVisible(false)}
      width={1000}
      title={'Unassigned Order Out list'}
      footer={false}
      bodyStyle={{ paddingTop: 0 }}
    >
      <ProTable<API.Task, API.PageParams>
        // headerTitle={'Order Out list'}
        actionRef={actionRef}
        rowKey="order_no"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        cardProps={{ bodyStyle: { padding: 0 } }}
        search={false}
        pagination={{
          showSizeChanger: true,
          pageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        params={{ with: '' }}
        request={getUnassignedCalendarTaskList}
        columns={columns}
        columnEmptyText=""
      />
    </Modal>
  );
};

export default UnassignedCalendarTasksListModal;
