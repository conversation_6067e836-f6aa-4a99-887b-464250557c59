/* eslint-disable react/no-array-index-key */
import {
  CheckOutlined,
  EditOutlined,
  ImportOutlined,
  PlusOutlined,
  WarningFilled,
} from '@ant-design/icons';
import { Button, message, Card, Tag } from 'antd';
import React, { useState, useRef, useEffect } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import UpdateForm from './components/UpdateForm';

import Util from '@/util';
import CreateForm from './components/CreateForm';
import {
  getSysXlsColNameMapList,
  deleteSysXlsColNameMap,
  importSysXlsColNameMap,
} from '@/services/app/Sys/sys-xls-col-name-map';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import { getMappableDBTables } from '@/services/app/api';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import SFooterToolbarExtra from '@/components/Table/SFooterToolbarExtra';
import BatchDeleteAction from '@/components/Table/BatchDeleteAction';
import type { DefaultOptionType } from 'antd/lib/select';

export type MappableTableType = {
  name: string;
  desc?: string;
  columnDeletable?: boolean;
};

export const getMappableDBTablesOptions = (
  tables?: Record<string, MappableTableType>,
): DefaultOptionType[] => {
  return tables
    ? Object.keys(tables).map((x) => ({
        value: tables[x].name,
        label: `${tables[x].desc} (${tables[x].name})`,
      }))
    : [];
};

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.SysXlsColNameMap[], isAlterDB?: boolean) => {
  const hide = message.loading('Deleting...', 0);
  if (!selectedRows) return true;

  try {
    await deleteSysXlsColNameMap({
      id: selectedRows.map((row) => row.id),
      isAlterDB,
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error('Delete failed, please try again!');
    return false;
  }
};

const SysXlsColNameMapList: React.FC = () => {
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();
  const [currentRow, setCurrentRow] = useState<API.SysXlsColNameMap>();
  const [selectedRowsState, setSelectedRows] = useState<API.SysXlsColNameMap[]>([]);

  const [tables, setTables] = useState<Record<string, MappableTableType>>({});

  const toolbarFormRef = useRef<ProFormInstance>();

  const columns: ProColumns<API.SysXlsColNameMap>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      sorter: true,
      width: 50,
    },
    {
      title: 'Table Name',
      dataIndex: 'table_name',
      sorter: true,
      width: 250,
    },

    {
      title: 'DB Field',
      dataIndex: 'db_field',
      sorter: true,
      ellipsis: true,
      width: 150,
    },
    {
      title: 'DB Field Type',
      dataIndex: 'db_field_type',
      sorter: true,
      ellipsis: true,
      width: 120,
    },
    {
      title: 'DB Field Comment',
      dataIndex: 'db_field_comment',
      sorter: true,
      ellipsis: true,
      width: 200,
    },
    {
      title: 'Max Length',
      dataIndex: 'db_field_length',
      sorter: true,
      ellipsis: true,
      align: 'right',
      width: 100,
    },
    {
      title: 'Status',
      dataIndex: 'db_field_status2',
      sorter: true,
      width: 100,
      align: 'center',
      render(dom, entity, index, action, schema) {
        const isDeletable = tables[`${entity.table_name}`]?.columnDeletable;
        let color = '';
        if (isDeletable) {
          color = '#f9a409';
        }
        return isDeletable ? (
          <WarningFilled
            style={{ color: color }}
            title={isDeletable ? 'Attention: column is alterable in DB.' : ''}
          />
        ) : null;
      },
    },

    {
      title: 'XLS Columns',
      dataIndex: 'xls_column',
      ellipsis: true,
      tooltip:
        'Text in XLS header cells. Not A1, A2, Z1! If an XLS columns are specified like A, B, C, etc, XLS data will be imported since the first row.',
      render(dom, record, index, action, schema) {
        return record.xls_column?.map((x, i: number) => <Tag key={i}>{x}</Tag>);
      },
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 80,
      fixed: 'right',
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          <EditOutlined />
        </a>,
      ],
    },
  ];

  useEffect(() => {
    getMappableDBTables()
      .then((res) => {
        setTables(res);
      })
      .catch((e) => Util.error(e));
  }, []);

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_xls_column', {
            table_name: '',
          })}
          submitter={{
            submitButtonProps: { htmlType: 'submit' },
            onSubmit: (values) => actionRef.current?.reload(),
            onReset: (values) => actionRef.current?.reload(),
            render: (form, dom) => {
              return [...dom];
            },
          }}
        >
          <ProFormSelect
            name="table_name"
            label="Table Name"
            placeholder="Please select table name"
            options={getMappableDBTablesOptions(tables)}
            width="md"
            fieldProps={{
              onChange(value, option) {
                actionRef.current?.reload();
              },
            }}
          />
        </ProForm>
      </Card>
      <ProTable<API.SysXlsColNameMap, API.PageParams>
        headerTitle={'XLS Column Names Mapping'}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        scroll={{ x: 800 }}
        search={false}
        toolBarRender={() => [
          <ProForm key="toolbar-form" formRef={toolbarFormRef} layout="inline" submitter={false}>
            <ProFormSelect
              name="table_name"
              label="Table Name"
              placeholder="Please select table name"
              options={getMappableDBTablesOptions(tables)}
              formItemProps={{ style: { marginBottom: 0 } }}
              width="sm"
              rules={[
                {
                  required: true,
                  message: 'Table Name is required',
                },
              ]}
            />
          </ProForm>,
          <Button
            type="primary"
            key="import"
            onClick={() => {
              toolbarFormRef.current?.validateFields().then((value) => {
                const tblName = value.table_name;
                console.log(tblName);
                if (!tblName) {
                  message.info('Please select table.');
                  return;
                }
                importSysXlsColNameMap(tblName)
                  .then((res) => {
                    if (res) {
                      message.success('Imported non-existed fields successfully.');
                      actionRef.current?.reload();
                    }
                  })
                  .catch((e) => Util.error(e));
              });
            }}
          >
            <ImportOutlined /> Import DB Fields
          </Button>,
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        tableAlertRender={false}
        pagination={{
          showSizeChanger: true,
          pageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        request={(params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_xls_column', searchFormValues);

          return getSysXlsColNameMapList(
            {
              ...params,
              ...searchFormValues,
            },
            sort,
            filter,
          ).then((res) => {
            return res;
          });
        }}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        columnEmptyText=""
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <SFooterToolbarExtra
              title="mapping"
              actionRef={actionRef}
              selectedRowsState={selectedRowsState}
            />
          }
        >
          <BatchDeleteAction
            title="mapping"
            onConfirm={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          />
          <BatchDeleteAction
            title="mapping"
            btnType="primary"
            btnTitle="Batch DB Column deletion"
            onConfirm={async () => {
              await handleRemove(selectedRowsState, true);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          />
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        tables={tables}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        tables={tables}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
      />
    </PageContainer>
  );
};

export default SysXlsColNameMapList;
