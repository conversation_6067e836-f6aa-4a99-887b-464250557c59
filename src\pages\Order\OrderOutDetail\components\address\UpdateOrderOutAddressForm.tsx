import type { Dispatch, SetStateAction } from 'react';
import { useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { <PERSON><PERSON>, Card, Col, Divider, message, Row } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSwitch } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProFormText, ModalForm } from '@ant-design/pro-form';
import { getAddressACList, updateOrderAddress } from '@/services/app/Sys/address';
import Util from '@/util';
import { useModel } from 'umi';
import ContactInfo from '@/pages/Sys/AddressList/components/ContactInfo';
import AddressContactList from '@/pages/Contact/AddressContactList';
import { AddressLoTaxTypeOptions, ADDRESS_LO_TAX_TYPE_NONE, ContactType } from '@/constants';
import HtmlEditor from '@/components/HtmlEditor';
import { SyncOutlined } from '@ant-design/icons';
import { handleOrderOutAddressByCustomerAddress } from '@/pages/Sys/AddressList/variant';

const handleUpdate = async (order_no: number, fields: FormValueType) => {
  const hide = message.loading('Updating OrderOut address...', 0);

  try {
    await updateOrderAddress(order_no, fields);
    hide();
    message.success('Update is successful');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

const handleExport = async (order_no: number, fields: FormValueType) => {
  const hide = message.loading('Updating OrderOut address & export to Lex Office...', 0);

  try {
    await updateOrderAddress(order_no, fields);
    hide();
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.Address>;

export type UpdateOrderOutAddressFormProps = {
  title: React.ReactNode | string;
  initialValues?: Partial<API.Address>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Address) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;

  type: 'OrderIn' | 'OrderOut';
  address_type: 'shipping' | 'invoice' | 'loading';
  order_no: number;
  customer_id?: number;
  supplier_id?: number;
  addressList?: API.Address[]; // selectable address List
};

const UpdateOrderOutAddressForm: React.FC<UpdateOrderOutAddressFormProps> = (props) => {
  const { countries } = useModel('sys-country');
  // const [country, setCountry] = useState<string | undefined>('DE');
  // const [regionList, setRegionList] = useState<DefaultOptionType[]>([]);
  // const [loading, setLoading] = useState(false);

  const formRef = useRef<ProFormInstance>();
  const [prefillAddressList, setPrefillAddressList] = useState<API.Address[]>([]);

  // We disable address editing
  // const readonly = props.supplier_id && props.address_type == 'loading';
  const readonly = true;

  useEffect(() => {
    if (formRef.current && props.modalVisible) {
      const newValues = { ...(props.initialValues || { country_code: 'DE' }) };
      if (newValues.address_type == 'invoice' && !newValues.lo_tax_type) {
        newValues.lo_tax_type = ADDRESS_LO_TAX_TYPE_NONE;
      }
      formRef.current.setFieldsValue(newValues);
      // setCountry(props.initialValues?.country_code);
    }
  }, [props.initialValues, props.modalVisible]);

  useEffect(() => {
    if (props.address_type && props.type && props.modalVisible) {
      if (props.supplier_id) {
        getAddressACList({ supplier_id: props.supplier_id }).then((res) =>
          setPrefillAddressList(res),
        );
      } else if (props.customer_id) {
        setPrefillAddressList(props.addressList || []);
      }
    }
  }, [
    props.address_type,
    props.customer_id,
    props.supplier_id,
    props.type,
    props.modalVisible,
    props.addressList,
  ]);

  /* useEffect(() => {
    setLoading(true);
    getCountryRegionList({ country_code: country, pageSize: 500 }, {}, {})
      .then((res) => {
        setRegionList(res.data.map((x) => ({ value: x.id, label: x.default_name })));
        formRef.current?.setFieldsValue({ region_id: res.data?.[0]?.id });
      })
      .finally(() => setLoading(false));
  }, [country]); */

  return (
    <ModalForm
      title={props.title}
      width="1000px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ flex: '0 0 120px' }}
      initialValues={props.initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const data = {
          ...value,
          id: props.initialValues?.id,
          order_no: props.order_no,
          type: props.type,
          address_type: props.address_type,
        };
        if (value.action == 'exportToLo') {
          const success = await handleExport(props.order_no, data);
          if (success) {
            if (props.onSubmit) props.onSubmit(data);
          }
        } else {
          if (readonly) delete data.by_address_id;

          const success = await handleUpdate(props.order_no, data);
          if (success) {
            props.handleModalVisible(false);
            if (props.onSubmit) props.onSubmit(data);
          }
        }
      }}
    >
      <Row gutter={32}>
        <Col span={12}>
          {readonly ? (
            <>
              <Card size="default" style={{ marginBottom: 24 }}>
                {props.initialValues?.company && <div>{props.initialValues.company}</div>}
                {props.initialValues?.full ?? '-'}
                {props.initialValues?.opening_hours && (
                  <div style={{ marginTop: 4, fontSize: 9 }}>
                    {props.initialValues.opening_hours}
                  </div>
                )}
              </Card>
              <ProFormSelect
                name={['by_address_id']}
                label="Address"
                tooltip="Change loading address."
                showSearch
                initialValue={props.initialValues?.id}
                fieldProps={{
                  dropdownMatchSelectWidth: false,
                  onChange: (value) => {
                    // const defaultAddress = prefillAddressList?.find((x) => x.id == value);
                    // if (defaultAddress) formRef.current?.setFieldsValue({ ...defaultAddress });
                    handleOrderOutAddressByCustomerAddress(
                      props.address_type,
                      props.order_no,
                      value,
                    ).then((res) => {
                      if (props.onSubmit) props.onSubmit({});
                    });
                  },
                }}
                options={prefillAddressList?.map?.((x) => ({
                  value: x.id,
                  label: `${x.company ?? '-'} | ${x.full}`,
                }))}
              />
            </>
          ) : (
            <>
              {props.address_type != 'loading' && (
                <>
                  <ProFormSelect
                    name={['default_address_id']}
                    label="Pre-fill address"
                    showSearch
                    fieldProps={{
                      dropdownMatchSelectWidth: false,
                      onChange: (value) => {
                        const defaultAddress = prefillAddressList?.find((x) => x.id == value);
                        if (defaultAddress) formRef.current?.setFieldsValue({ ...defaultAddress });
                      },
                    }}
                    options={prefillAddressList?.map?.((x) => ({
                      value: x.id,
                      label: `${x.company ?? '-'} | ${x.full}`,
                    }))}
                  />
                  <Divider />
                </>
              )}
              <ContactInfo isSupplier={!!props.supplier_id} />
              <Divider />

              <ProFormText width="md" name="street" label="Street 1" placeholder="Street 1" />
              <ProFormText width="md" name="street2" label="Street 2" placeholder="Street 2" />
              <ProFormText width="sm" name="postcode" label="Zip" placeholder="Zip" />
              <ProFormText width="sm" name="city" label="City" placeholder="City" />

              <ProFormSelect
                name={['country_code']}
                label="Country"
                showSearch
                options={countries.map((x) => ({ value: x.code, label: x.name }))}
              />

              {/* <ProFormSelect
        name={['region_id']}
        label="Region"
        mode="single"
        showSearch
        fieldProps={{ loading: loading }}
        options={regionList}
      />

      <ProFormDependency name={['region_id']}>
        {(depValues) => {
          return depValues.region_id ? (
            <></>
          ) : (
            <>
              <Col style={{ paddingLeft: 120 }}>
                <Divider>or</Divider>
              </Col>
              <ProFormText
                width="md"
                name="region"
                tooltip="Fill regison in case of non-existence"
                label={null}
                wrapperCol={{ style: { marginLeft: 120 } }}
              />
            </>
          );
        }}
      </ProFormDependency> */}

              {props.address_type == 'loading' && (
                <>
                  <Divider />
                  <ProFormText name={['opening_hours']} label="Opening hours" />
                </>
              )}
              {props.address_type == 'invoice' && (
                <>
                  <Divider />
                  <ProFormSelect
                    name={['lo_tax_type']}
                    label="TaxFree Invoices"
                    showSearch
                    options={AddressLoTaxTypeOptions}
                  />
                </>
              )}
            </>
          )}
          <Divider />
          {props.initialValues?.id && (
            <AddressContactList
              type={props.type}
              contact_type={
                props.address_type == 'loading'
                  ? ContactType.SUPPLIER_ADDRESS_CONTACT
                  : ContactType.CUSTOMER_ADDRESS_CONTACT
              }
              supplier_id={props.address_type == 'loading' ? props.supplier_id : undefined}
              customer_id={props.address_type == 'loading' ? undefined : props.customer_id}
              address_id={props.initialValues?.id}
            />
          )}

          {props.address_type == 'invoice' && props.initialValues?.id && (
            <ProFormSwitch label="Update shipping?" name={'update_shipping'} />
          )}

          {props.address_type == 'invoice' && props.initialValues?.id && (
            <>
              <div className="d-none">
                <ProFormText name="action" />
              </div>
              <Button
                type="primary"
                icon={<SyncOutlined />}
                onClick={() => {
                  formRef.current?.setFieldValue('action', 'exportToLo');
                  formRef.current?.submit();
                }}
              >
                Save & Export to LexOffice
              </Button>
            </>
          )}
        </Col>
        <Col span={12}>
          <div style={{ marginBottom: 12 }}>Detail: </div>
          <ProForm.Item name={'detail'} labelCol={{ span: 0 }}>
            <HtmlEditor
              initialValue={props.initialValues?.detail}
              min_height={600}
              id={`detail-${props.address_type}`}
            />
          </ProForm.Item>
        </Col>
      </Row>
    </ModalForm>
  );
};

export default UpdateOrderOutAddressForm;
