import { TaskBlockLayout } from '@/constants';
import { useCallback, useEffect, useMemo, useState } from 'react';
import _ from 'lodash';
import { useModel } from 'umi';
import { updateUserPartial } from '@/services/app/user';
import update from 'immutability-helper';

export default () => {
  const { initialState, setInitialState } = useModel('@@initialState');
  const { currentUser } = initialState ?? {};

  const [taskGroups, setTaskGroups] = useState<API.TaskGroup[]>([]);
  const [filters, setFilters] = useState<{
    [x: string]: undefined | number | any;
    status?: number;
    userIds?: Record<number, boolean>;
  }>();

  // blockID => block
  const blockInfoMap: Record<number, Partial<API.TaskBlock>> = useMemo(() => {
    const res: any = {};
    if (!taskGroups) return res;
    for (const group of taskGroups) {
      for (const block of group?.task_blocks || []) {
        res[block.id] = {
          ...block,
        };
      }
    }

    return res;
  }, [taskGroups]);

  const getBlockLayout = useCallback<(blockId?: number) => TaskBlockLayout>(
    (blockId?: number) => blockInfoMap[blockId || 0]?.layout || TaskBlockLayout.LAYOUT_C,
    [blockInfoMap],
  );

  const getBlock = useCallback<(blockId?: number) => Partial<API.TaskBlock>>(
    (blockId?: number) => blockInfoMap[blockId || 0],
    [blockInfoMap],
  );

  const replaceTaskGroup = useCallback<(newGroup: API.TaskGroup) => Promise<void>>(
    async (newGroup) => {
      setTaskGroups((prev) => {
        const newGroups = [...prev];
        const index = _.findIndex(newGroups, { id: newGroup.id });

        if (index >= 0) {
          newGroups[index] = newGroup;
        } else {
          newGroups.push(newGroup);
        }
        return newGroups;
      });
    },
    [],
  );

  const getTaskGroupInModel = useCallback<(id?: number) => API.TaskGroup | undefined>(
    (id) => {
      return id ? _.find(taskGroups, { id }) : undefined;
    },
    [taskGroups],
  );

  const isOrderOutGroup = useCallback<(id?: number) => boolean>(
    (id) => _.findIndex(taskGroups, { id, code: 'OrderOut' }) >= 0,
    [taskGroups],
  );

  const isOrderOutHeadGroup = useCallback<(id?: number) => boolean>(
    (id) => _.findIndex(taskGroups, { id, code: 'OrderOutHead' }) >= 0,
    [taskGroups],
  );

  const saveUserTaskFilters: any = useCallback<(filters?: any) => Promise<API.CurrentUser>>(
    async (newFilters) => {
      return updateUserPartial(currentUser?.user_id, {
        settings: { ...currentUser?.settings, taskFilters: newFilters },
      }).then((resUser) => {
        setInitialState((prev) =>
          update(prev, { currentUser: { setttings: { $set: resUser.settings } } }),
        );
        return resUser;
      });
    },
    [currentUser?.settings, currentUser?.user_id, setInitialState],
  );

  useEffect(() => {
    if (currentUser?.user_id) {
      setFilters(currentUser?.settings?.taskFilters ?? {});
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentUser?.user_id]);

  return {
    taskGroups,
    setTaskGroups,
    blockInfoMap,
    getBlockLayout,
    getBlock,
    filters,
    setFilters,
    replaceTaskGroup,
    getTaskGroupInModel,
    isOrderOutGroup,
    isOrderOutHeadGroup,

    saveUserTaskFilters,
  };
};
