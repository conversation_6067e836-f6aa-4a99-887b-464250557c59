/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/sys/xlsColNameMap';

/** rule GET /api/sys/xlsColNameMap */
export async function getSysXlsColNameMapList(
  params: API.PageParams & Partial<API.SysXlsColNameMap>,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.SysXlsColNameMap>> {
  return request<API.Result<API.SysXlsColNameMap>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

export async function getSysXlsColNameMapByProfile(
  params: Partial<API.SysXlsColNameMap>,
  sort?: any,
): Promise<API.SysXlsColNameMap[]> {
  return request<API.Result<API.SysXlsColNameMap>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: 1000,
      sort,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message.data);
}

/** put PUT /api/sys/xlsColNameMap */
export async function updateSysXlsColNameMap(
  id?: number,
  data?: Partial<API.SysXlsColNameMap>,
  options?: { [key: string]: any },
) {
  return request<API.SysXlsColNameMap>(`${urlPrefix}/` + id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/sys/xlsColNameMap */
export async function addSysXlsColNameMap(
  data: API.SysXlsColNameMap,
  options?: { [key: string]: any },
) {
  return request<API.SysXlsColNameMap>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/sys/xlsColNameMap */
export async function importSysXlsColNameMap(table_name: string, options?: { [key: string]: any }) {
  return request<API.ResultObject<boolean>>(`${urlPrefix}/import`, {
    method: 'POST',
    data: {
      table_name,
    },
    ...(options || {}),
  }).then((res) => res.message);
}

/** delete DELETE /api/sys/xlsColNameMap/{id} */
export async function deleteSysXlsColNameMap(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    params: {
      ...(options || {}),
    },
    ...(options || {}),
  });
}
