/* eslint-disable */
import { request } from 'umi';

const urlPrefix = '/api/task/task-workflow-step';

/** put PUT /api/task/task-workflow-step */
export async function updateTaskWorkflowStep(
  data: API.TaskWorkflowStep & {
    workflow_id?: number;
    step_info?: any;
    single_update?: boolean;
  },
  options?: { [key: string]: any },
) {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** post POST /api/task/task-workflow-step */
export async function addTaskWorkflowStep(
  data: Partial<API.TaskWorkflowStep> & { workflow_id?: number },
  options?: { [key: string]: any },
) {
  return request<API.TaskWorkflowStep>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}
