import { EditOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, message, Drawer, Tag } from 'antd';
import React, { useState, useRef } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import UpdateForm from './components/UpdateForm';

import Util, { sn } from '@/util';
import CreateForm from './components/CreateForm';
import { getSysWorkflowList, deleteSysWorkflow } from '@/services/app/Sys/sys-workflow';
import { DEFAULT_PER_PAGE_PAGINATION, DictCode } from '@/constants';
import { useModel } from 'umi';
import WorkflowStepList from './components/WorkflowStepList';
import WorkflowSectionList from './components/WorkflowSectionList';

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.SysWorkflow[]) => {
  const hide = message.loading('Deleting');
  if (!selectedRows) return true;

  try {
    await deleteSysWorkflow({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error('Delete failed, please try again!', error);
    return false;
  }
};

const SysWorkflowList: React.FC = () => {
  const { getCode } = useModel('app-settings');

  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.SysWorkflow>();
  const [selectedRowsState, setSelectedRows] = useState<API.SysWorkflow[]>([]);

  const [showSectionsList, setShowSectionsList] = useState<boolean>(false);

  const columns: ProColumns<API.SysWorkflow>[] = [
    {
      title: 'Name',
      dataIndex: 'name',
      sorter: true,
      hideInSearch: true,
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            setShowDetail(true);
            setCurrentRow(record);
          }}
        >
          {record.name}
        </a>,
      ],
    },
    {
      title: 'Usage',
      dataIndex: 'tmp',
      hideInSearch: true,
      align: 'center',
      width: 200,
      render: (dom, record) => {
        let text = null;
        if (getCode(DictCode.CODE_ORDER_OUT_WORKFLOW_ID)?.value == `${record?.id}`)
          text = 'Order Out';
        else if (getCode(DictCode.CODE_ORDER_IN_WORKFLOW_ID)?.value == `${record?.id}`)
          text = 'Order In';
        else text = record.type;
        return text ? <Tag>{text}</Tag> : undefined;
      },
    },
    {
      title: 'ID',
      dataIndex: 'id',
      sorter: true,
      hideInSearch: true,
      width: 100,
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 100,
      fixed: 'right',
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          <EditOutlined />
        </a>,
        <a
          key="sections"
          onClick={() => {
            setShowSectionsList(true);
            setCurrentRow(record);
          }}
        >
          Sections
        </a>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.SysWorkflow, API.PageParams>
        headerTitle={'Workflows list'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        scroll={{ x: 800 }}
        search={false}
        /* search={{
          labelWidth: 'auto',
          searchText: 'Search',
          span: 6,
          filterType: 'query',
        }} */
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        pagination={{
          showSizeChanger: true,
          pageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        request={getSysWorkflowList}
        columns={columns}
        /* rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }} */
        rowSelection={false}
        tableAlertOptionRender={false}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              chosen{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              SysWorkflow &nbsp;&nbsp;
            </div>
          }
        >
          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            Bacth deletion
          </Button>
          {/* <Button type="primary">batch approval</Button> */}
        </FooterToolbar>
      )}

      {/* <WorkflowStepRuleGroupsForm
        initialValues={{ id: 4, workflow_id: 1, section_id: 1, desc: 'TEXT', field_type: 'text' }}
        modalVisible={true}
        handleModalVisible={function (value: React.SetStateAction<boolean>): void {
          throw new Error('Function not implemented.');
        }}
      /> */}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async () => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async () => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <Drawer
        width={1400}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        <WorkflowStepList
          workflow_id={sn(currentRow?.id)}
          open={showDetail}
          workflow_name={currentRow?.name}
          workflow_settings={currentRow?.settings}
        />
      </Drawer>

      <Drawer
        width={1200}
        open={showSectionsList}
        onClose={() => {
          setCurrentRow(undefined);
          setShowSectionsList(false);
        }}
        closable={false}
      >
        {<WorkflowSectionList workflow_id={sn(currentRow?.id)} />}
      </Drawer>
    </PageContainer>
  );
};

export default SysWorkflowList;
