import { getSupplierFieldACList } from '@/services/app/Order/order-in';
import Util from '@/util';
import type { DefaultOptionType } from 'antd/lib/select';
import { useCallback, useState } from 'react';

/**
 * Auto completion list of supplier in order_in table
 */
export const useOrderInSupplierFieldOptions = (defaultParams?: Record<string, any>) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [supplierOptions, setSupplierOptions] = useState<DefaultOptionType[]>([]);

  const searchSupplierOptions = useCallback(
    async (params?: Record<string, any>, sort?: any) => {
      setLoading(true);
      return getSupplierFieldACList({ ...defaultParams, ...params }, sort)
        .then((res) => {
          setSupplierOptions(res);
          return res;
        })
        .catch(Util.error)
        .finally(() => {
          setLoading(false);
        });
    },
    [defaultParams],
  );

  return { supplierOptions, searchSupplierOptions, loading };
};
