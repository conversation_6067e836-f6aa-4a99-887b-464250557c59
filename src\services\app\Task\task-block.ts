/* eslint-disable */
import Util from '@/util';
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/task/block';

/** task GET /api/task/block */
export async function getTaskBlockList(
  params: Partial<API.PageParams & API.TaskBlock>,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.TaskBlock>> {
  return request<API.Result<API.TaskBlock>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** put PUT /api/task/block */
export async function updateTaskBlock(
  data: Partial<API.TaskBlock> & { returnGroup?: boolean },
  options?: { [key: string]: any },
) {
  return request<API.ResultObject<API.TaskBlock>>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}
/** put PUT /api/task/block */
export async function updateTaskBlockPartial(
  data: Partial<API.TaskBlock> & { returnGroup?: boolean },
  options?: { [key: string]: any },
) {
  return request<API.ResultObject<API.TaskBlock>>(`${urlPrefix}/` + data.id + `/partial`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** post POST /api/task/block */
export async function addTaskBlock(data: Partial<API.TaskBlock>, options?: { [key: string]: any }) {
  return request<API.ResultObject<API.TaskBlock>>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  })
    .then((res) => res.message);
}

/** post POST /api/task/block/add-or-update */
export async function addOrUpdateTaskBlock(data: API.TaskBlock, options?: { [key: string]: any }) {
  return request<API.ResultObject<API.TaskBlock>>(`${urlPrefix}/add-or-update`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  })
    .then((res) => res.message)
    .catch((reason) => Util.error(reason));
}

/** delete DELETE /api/task/block/{id} */
export async function deleteTaskBlock(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  }).catch((reason) => Util.error(reason));
}
