.ant-table-cell .editable-cell-value-wrap {
  min-height: 36px;
  margin: 0 -5px;
  padding: 3px 5px;
}

.ant-table-tbody > tr.ant-table-row:hover > td,
.ant-table-tbody > tr > td.ant-table-cell-row-hover {
  background: unset;
}

/*
Table customization
-------------------------------------------------------------------
*/
.editable-table-xs {
  .ant-table.ant-table-small .ant-table-title,
  .ant-table.ant-table-small .ant-table-footer,
  .ant-table.ant-table-small .ant-table-thead > tr > th,
  .ant-table.ant-table-small .ant-table-tbody > tr.ant-table-row > td,
  .ant-table.ant-table-small tfoot > tr > th,
  .ant-table.ant-table-small tfoot > tr > td {
    padding: 2px 5px !important;
    font-size: 11px;
    input,
    textarea {
      font-size: 11px;
    }
    .ant-form-item-control-input {
      min-height: 24px;
    }
    .ant-input-affix-wrapper > input.ant-input {
      font-size: 11px;
    }
  }
  .ant-pro-table td.ant-table-cell > a {
    font-size: 13px !important;
    line-height: 20px;
  }
  .ant-radio-wrapper {
    font-size: 11px;
  }
}

/*
Dropdown customization
-------------------------------------------------------------------
*/
.ant-select.size-xs {
  font-size: 11px;
}

.ant-select-dropdown {
  .ant-select-item-wrap-xs .ant-select-item {
    min-height: 24px;
    font-size: 12px;
    line-height: 16px;
  }
}
