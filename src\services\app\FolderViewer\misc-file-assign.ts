/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/fileBrowser';

/** 
 * 
 * GET /api/fileBrowser/getFilesInMiscPath */
export async function getFilesInMiscPath(
    params: Partial<API.PageParams> & { id?: string },
    sort?: any,
    filter?: any,
) {
    return request<API.ResultObject<API.FmFile[]>>(`${urlPrefix}/getFilesInMiscPath`, {
        method: 'GET',
        params: {
            ...params,
            perPage: params.pageSize,
            page: params.current,
            sort,
            filter,
        },
        paramsSerializer,
        withToken: true,
    }).then((res) => res.message);
}
