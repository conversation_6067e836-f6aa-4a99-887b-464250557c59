import Util from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import { Button, Card, message, Spin } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import _ from 'lodash';
import {
  exportOrderOutDetailCrmAndAddress,
  getOrderOutDetailCrmAuthors,
  getOrderOutDetailCrmList,
  updateOrderOutDetailCmr,
} from '@/services/app/Order/order-out-detail-crm';
import type { ActionType, EditableFormInstance, ProColumns } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import SProFormDigit from '@/components/SProFormDigit';
import { DeleteFilled, ExportOutlined } from '@ant-design/icons';
import type { DefaultOptionType } from 'antd/lib/select';
import XlsTplSelectionModal from './XlsTplSelectionModal';
import useXlsFileLocal from '../FileDetail/useXlsFileLocal';
import CmrEmailTemplateModal from './CmrEmailTemplateModal';

type EditableRowType = Omit<API.OrderOutDetailCrmItem, 'id'> & { id: React.Key };

const getDefaultOrderOutDetailCrmItems = () =>
  Array.from({ length: 7 }).map(
    (x) =>
      ({
        id: Util.genNewKey('key_'),
        desc: '',
      } as EditableRowType),
  );

type CrmDataProps = {
  order_no: number;
  detail_file_id?: number; // for useXlsFileLocal
};

const CrmData: React.FC<CrmDataProps> = (props) => {
  const { order_no } = props;

  const [loading, setLoading] = useState<boolean>(false);
  const formRef = useRef<ProFormInstance>();

  // CRM data
  const [crmData, setCrmData] = useState<API.OrderOutDetailCrm>({});

  // Editable table form
  const editableFormRef = useRef<EditableFormInstance>();
  const actionRef = useRef<ActionType>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);
  const [dataSource, setDataSource] = useState<EditableRowType[]>(() => []);

  const [authors, setAuthors] = useState<DefaultOptionType[]>(() => []);

  // XLS export modal
  const [openAddressTplSelectionModal, setOpenAddressTplSelectionModal] = useState<boolean>(false);
  // Email template content modal
  const [openEmailTemplateModal, setOpenEmailTemplateModal] = useState<boolean>(false);

  // custom hooks
  const { aggregationElement } = useXlsFileLocal(props);

  const columns: ProColumns<EditableRowType>[] = [
    {
      title: 'ID',
      dataIndex: ['id'],
      width: 50,
      hideInTable: true,
      hideInForm: true,
      hideInSetting: true,
    },
    {
      title: 'Description',
      dataIndex: ['desc'],
      fieldProps: {
        placeholder: 'Description',
        size: 'small',
      },
      formItemProps: (form, { rowIndex, entity, filteredValue }) => {
        // const rules = [{ required: false, message: 'Qty is required!' }];
        return {
          // rules,
          // hasFeedback: false,
        };
      },
    },
    {
      title: 'HS Code',
      dataIndex: ['hs_code'],
      width: 200,
      fieldProps: {
        placeholder: 'HS Code',
        size: 'small',
      },
    },
    {
      title: 'Weight',
      dataIndex: ['weight'],
      width: 120,
      renderFormItem: (item, { defaultRender, record, ...rest }, form) => {
        return (
          <SProFormDigit
            formItemProps={{ style: { marginBottom: 0 } }}
            fieldProps={{ placeholder: 'Weight', precision: 4, size: 'small' }}
          />
        );
      },
    },
  ];

  useEffect(() => {
    getOrderOutDetailCrmAuthors()
      .then((res) => setAuthors(res))
      .catch((e) => Util.error(e));
  }, []);

  useEffect(() => {
    if (!order_no) return;

    setLoading(true);
    getOrderOutDetailCrmList({
      pageSize: 500,
      order_no,
      with: 'items',
    })
      .then((res) => {
        const obj = res.data?.[0] ?? {};
        setCrmData(obj);
      })
      .finally(() => setLoading(false));
  }, [order_no]);

  useEffect(() => {
    formRef.current?.setFieldsValue({
      order_no: crmData.order_no,
      author: crmData.author,
    });
  }, [crmData?.order_no, crmData?.author]);

  useEffect(() => {
    const ds = crmData?.order_out_detail_crm_items ?? getDefaultOrderOutDetailCrmItems();
    setEditableRowKeys(ds.map((x) => x.id as React.Key));
    setDataSource([...ds] as EditableRowType[]);
  }, [crmData?.order_out_detail_crm_items]);

  // email template rendering
  const mailButtonClickedRef = useRef<boolean>(false);
  const [saved, setSaved] = useState<boolean>(false);

  useEffect(() => {
    if (!openEmailTemplateModal) {
      mailButtonClickedRef.current = false;
    }
  }, [openEmailTemplateModal]);

  useEffect(() => {
    console.log('Hook: ', mailButtonClickedRef.current, 'saved? ', saved);
    if (mailButtonClickedRef.current && saved) {
      console.log('Hook2');
      setOpenEmailTemplateModal(true);
    }
  }, [saved]);

  return (
    <>
      <Spin spinning={loading}>
        {props.children}
        {aggregationElement}
        <Card style={{ marginTop: 0 }} bodyStyle={{ padding: 0 }} bordered={false}>
          <EditableProTable<EditableRowType>
            editableFormRef={editableFormRef}
            actionRef={actionRef}
            rowKey="id"
            sticky
            bordered={false}
            controlled
            debounceTime={200}
            className="editable-table"
            size="small"
            cardProps={{
              style: { marginBottom: '2rem' },
              bodyStyle: { padding: 0 },
            }}
            scroll={{
              x: 700,
            }}
            style={{
              padding: 0,
            }}
            recordCreatorProps={false}
            loading={loading}
            columns={columns}
            value={dataSource}
            onChange={setDataSource}
            locale={{ emptyText: <></> }}
            editable={{
              type: 'multiple',
              editableKeys,
              onChange: setEditableRowKeys,
              deletePopconfirmMessage: 'Are you sure you want to delete?',
              onlyAddOneLineAlertMessage: 'You can only add one.',
              deleteText: <DeleteFilled style={{ marginLeft: 'auto' }} />,
            }}
          />
        </Card>
        <Card style={{ marginTop: 0 }} bodyStyle={{ padding: 0 }} bordered={false}>
          <ProForm
            formRef={formRef}
            layout="horizontal"
            grid
            onFinish={async (values) => {
              editableFormRef.current?.validateFields().then((items) => {
                const hide = message.loading('Saving...', 0);
                const updateData = {
                  order_out_detail_crm_items: dataSource,
                  order_no,
                  ...values,
                  id: crmData?.id,
                };

                updateOrderOutDetailCmr(updateData)
                  .then((res) => {
                    message.success('Saved successfully.');
                    setSaved(true);
                    setCrmData(res);
                  })
                  .catch((e) => {
                    Util.error(e);
                  })
                  .finally(() => hide());
              });
            }}
            submitter={{
              render(__) {
                return [
                  <Button
                    key="submit"
                    type="primary"
                    size="small"
                    onClick={() => {
                      formRef.current?.submit();
                    }}
                  >
                    Save
                  </Button>,
                  crmData.id ? (
                    <Button
                      key="export"
                      type="primary"
                      size="small"
                      onClick={() => {
                        setOpenAddressTplSelectionModal(true);
                      }}
                      icon={<ExportOutlined />}
                    >
                      Export
                    </Button>
                  ) : undefined,
                  <Button
                    key="saveAndEmail"
                    type="primary"
                    size="small"
                    onClick={() => {
                      mailButtonClickedRef.current = true;
                      setSaved(false);
                      formRef.current?.submit();
                      // setOpenEmailTemplateModal(true);
                    }}
                  >
                    Mail
                  </Button>,
                ];
              },
            }}
          >
            <div className="d-none">
              <ProFormText name="action" />
            </div>
            <ProFormSelect
              name="author"
              options={authors}
              label="Author"
              placeholder="Author"
              width="sm"
              fieldProps={{ size: 'small' }}
            />
          </ProForm>
        </Card>

        {order_no && crmData.id && (
          <XlsTplSelectionModal
            modalVisible={openAddressTplSelectionModal}
            handleModalVisible={setOpenAddressTplSelectionModal}
            onSubmit={async (value: any) => {
              console.log('On Submit...', value);
              if (!crmData.id) {
                message.error('Please save first.');
                return;
              }

              if (!order_no) {
                message.error('OrderNo is invalid!');
                return;
              }

              const hide = message.loading('Exporting XLS file...', 0);
              exportOrderOutDetailCrmAndAddress({
                tplId: value.tplId,
                crmId: crmData.id,
                orderNo: order_no,
              })
                .then((res) => {
                  message.success('Exported successfully.');
                  window.location.href = `${API_URL}${res.file_url}`;
                })
                .catch((err) => Util.error(err))
                .finally(() => hide());
            }}
          />
        )}
        {!!order_no && crmData.id && (
          <CmrEmailTemplateModal
            modalVisible={openEmailTemplateModal}
            handleModalVisible={setOpenEmailTemplateModal}
            crmId={crmData.id}
            orderNo={order_no}
          />
        )}
      </Spin>
    </>
  );
};

export default CrmData;
