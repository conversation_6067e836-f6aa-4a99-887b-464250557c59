/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/task/task-workflow-step-log';

/** orderOut GET /api/task/task-workflow-step-log */
export async function getTaskWorkflowStepLogList(
  params: API.PageParams & Partial<API.TaskWorkflowStepLog>,
  sort: any,
  filter: any,
): Promise<API.PaginatedResult<API.TaskWorkflowStepLog>> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}
