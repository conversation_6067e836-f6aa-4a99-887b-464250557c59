// https://umijs.org/config/
import { defineConfig } from 'umi';

export default defineConfig({
  base: '/whc_task/',
  publicPath: 'https://192.168.1.138/whc_task/',
  favicon: '/whc_task/images/favicon.png',
  layout: {
    logo: '/whc_task/images/favicon.png',
  },
  define: {
    API_URL: 'https://192.168.1.138:444',
  },

  // By emulated SC's ENV.
  /* base: '/whc_task/',
  publicPath: 'http://192.168.110.44/whc_task/',
  favicon: '/whc_task/images/favicon.png',
  define: {
    API_URL: 'http://192.168.110.44/whc-order-backend/public',
  },
  layout: {
    logo: '/whc_task/images/favicon.png',
  }, */

  // By serving
  /* base: '/',
  publicPath: 'http://192.168.110.44:8001/',
  favicon: '/images/favicon.png',
  define: {
    API_URL: 'http://192.168.110.44/whc-order-backend/public',
  },
  layout: {
    logo: '/images/favicon.png',
  }, */
});
