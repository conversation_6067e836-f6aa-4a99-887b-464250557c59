/* eslint-disable */
import { RequestConfig, request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/fin/detail';

/** GET /api/fin/detail 
 * 
 * Get fin detail list  
*/
export async function getFinDetailList(
  params: API.PageParams & { order_no?: number } & Partial<API.FinDetail>,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.FinDetail>> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

export async function getFinDetailListWithImportable(
  params: API.PageParams & { order_no?: number } & Partial<API.FinDetail>,
  sort?: any,
  filter?: any,
): Promise<{ data: API.FinDetail[] }> {
  return request<API.BaseResult>(`${urlPrefix}/listWithImportable`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => res.message);
}

export async function updateFinDetailListWithImportable(
  params: { order_no?: number, lo_id?: string, lo_no?: string, rows?: Partial<API.FinDetail>[] }
): Promise<{ data: API.FinDetail[] }> {
  return request<API.BaseResult>(`${urlPrefix}/updateFinDetails`, {
    method: 'PUT',
    data: params
  }).then((res) => res.message);
}

/** delete DELETE /api/fin/detail/deleteAll */
export async function deleteFinDetailList(params: { order_no?: number, lo_id?: string, lo_no?: string }, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/deleteAll`, {
    method: 'DELETE',
    params,
    ...(options || {}),
  });
}



/** 
 * POST /api/fin/detail/importDraftFromXls 
 * 
 * import draft data into frontend table from Xls file. (not saving backend...)  
*/
export async function importFinDetailDraftList(
  data: (API.OrderOut & { file?: File }) | FormData,
  options?: { [key: string]: any },
): Promise<API.File> {
  const config: RequestConfig = {
    method: 'POST',
    ...(options || {}),
  };
  if (data instanceof FormData) {
    config.body = data;
  } else {
    config.data = data;
  }

  return request<API.ResultObject<API.File>>(`${urlPrefix}/importDraftFromXls`, config).then(
    (res) => res.message,
  );
}

/** 
 * POST /api/fin/importFromXls 
 * 
 */
export async function importFinDetailFromXls(
  data?: FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'POST',
    ...(options || {}),
  };

  config.body = data;

  return request<any>(`${urlPrefix}/importFromXls`, config);
}