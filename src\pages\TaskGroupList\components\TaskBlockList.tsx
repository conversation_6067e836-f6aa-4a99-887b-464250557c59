import {
  TaskBlockLayout,
  TaskBlockLayoutOptions,
  TaskBlockPositionMap,
  TaskGroupLayout,
} from '@/constants';
import {
  addOrUpdateTaskBlock,
  deleteTaskBlock,
  getTaskBlockList,
} from '@/services/app/Task/task-block';
import { copyBlocks } from '@/services/app/Task/task-group';
import Util from '@/util';
import {
  CloseOutlined,
  DeleteOutlined,
  EditOutlined,
  ImportOutlined,
  PlusOutlined,
  ReloadOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import type { ActionType, EditableFormInstance, ProColumns } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import { Button, Form, message, Modal, Popconfirm, Space, Tag, Typography } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useModel } from 'umi';
import TaskBlockSettingModalForm from './TaskBlockSettingModal';

type TaskBlockListProps = {
  group_id?: number;
  task_blocks?: API.TaskBlock[];
  group_layout?: TaskGroupLayout;
};

export const getBlockLayoutKV = (groupLayout: TaskGroupLayout) => {
  return TaskBlockLayoutOptions.reduce(
    (prev: any, cur: any) => ((prev[cur.value] = { text: cur.label }), prev),
    {},
  );
};

export const getBlockPositionInGroupKV = (groupLayout: TaskGroupLayout) => {
  if (groupLayout == TaskGroupLayout.LAYOUT_2) {
    return {
      1: 'Block #1',
      2: 'Block #2',
      3: 'Block #3',
      4: 'Block #4',
      5: 'Block #5',
      6: 'Block #6',
      7: 'Block #7',
      8: 'Block #8',
    };
  } else if (groupLayout == TaskGroupLayout.LAYOUT_3) {
    return {
      1: 'Block #1',
      2: 'Block #2',
      3: 'Block #3',
      4: 'Block #4',
      5: 'Block #5',
      6: 'Block #6',
      7: 'Block #7',
      8: 'Block #8',
      9: 'Block #9',
      10: 'Block #10',
    };
  } else {
    return TaskBlockPositionMap;
  }
};

const TaskBlockList: React.FC<TaskBlockListProps> = (props) => {
  const { taskGroups } = useModel('task-group');

  const actionRef = useRef<ActionType>();
  const editableFormRef = useRef<EditableFormInstance<API.TaskBlock>>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<API.TaskBlock[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  const [form] = Form.useForm();
  const [formConfirm] = Form.useForm();

  // edit setting modal
  const [openBlockSettingModal, setOpenBlockSettingModal] = useState<boolean>(false);
  const [currentBlock, setCurrentBlock] = useState<API.TaskBlock>();

  useEffect(() => {
    if (actionRef.current && props.group_id) {
      actionRef.current.reload();
      setDataSource([]);
      setEditableRowKeys([]);
    } else {
      setEditableRowKeys([]);
    }
  }, [props.group_id]);

  const columns: ProColumns<API.TaskBlock>[] = [
    {
      title: 'Name',
      dataIndex: 'name',
      formItemProps: {
        rules: [
          {
            required: true,
            message: 'Name is required.',
          },
        ],
      },
    },
    {
      title: 'UI Position',
      key: 'position',
      dataIndex: 'position',
      valueType: 'select',
      valueEnum: getBlockPositionInGroupKV(props?.group_layout ?? TaskGroupLayout.LAYOUT_DEFAULT),
      formItemProps: { getValueProps: (value) => ({ value: `${value}` }) },
      width: 120,
    },
    {
      title: 'Layout',
      key: 'layout',
      dataIndex: 'layout',
      valueType: 'select',
      width: 100,
      initialValue: TaskBlockLayout.LAYOUT_C,
      valueEnum: getBlockLayoutKV(props?.group_layout ?? TaskGroupLayout.LAYOUT_DEFAULT),
    },
    {
      title: 'Settings',
      key: 'layout',
      dataIndex: 'settings',
      width: 150,
      tooltip: 'Click to edit settings',
      className: 'cursor-pointer',
      hideInForm: true,
      render(dom, record, index, action, schema) {
        return (
          <>
            {record.settings?.panelMenus?.map((x) => (
              <Tag key={x.id} style={{ width: 120, wordBreak: 'break-all' }}>
                <Typography.Text ellipsis>
                  {x.label} Group: #{x.destGroupId} Block: #{x.destBlockId}{' '}
                </Typography.Text>
              </Tag>
            ))}
          </>
        );
      },
      renderFormItem(schema, config, __, action) {
        return undefined;
      },
      onCell: (record, index) => {
        return {
          onClick: () => {
            setCurrentBlock(record);
            setOpenBlockSettingModal(true);
          },
        };
      },
    },
    {
      title: 'ID',
      dataIndex: 'id',
      valueType: 'text',
      width: 60,
      editable: false,
    },
    {
      title: '',
      valueType: 'option',
      width: 50,
      render: (text, record, _, action) => (
        <Space>
          <a
            key="edit"
            type="link"
            onClick={() => {
              action?.startEditable?.(record.id);
            }}
          >
            <EditOutlined />
          </a>
          <Popconfirm
            key="delete"
            className="c-red"
            title={<>Are you sure you want to delete?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ width: 300 }}
            onConfirm={async (e) => {
              deleteTaskBlock({ id: record.id }).then((res) => {
                message.success('Deleted succesfully.');
                setDataSource((prev) => prev.filter((x) => x.id != record.id));
              });
            }}
          >
            <DeleteOutlined />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <EditableProTable<API.TaskBlock>
        loading={loading}
        rowKey="id"
        scroll={{
          x: 400,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="new"
            size="small"
            icon={<PlusOutlined />}
            onClick={() => {
              // handleModalVisible(true);
              actionRef.current?.addEditRecord?.({
                id: 'k' + (Math.random() * 1000000).toFixed(0),
                name: '',
                position: 1,
              });
            }}
          >
            New
          </Button>,
          <Button
            type="primary"
            key="copy"
            title="Copy from another group."
            size="small"
            icon={<ImportOutlined />}
            onClick={() => {
              Modal.confirm({
                title: 'Copying blocks. Please select source task Group',
                content: (
                  <ProForm form={formConfirm} submitter={false}>
                    <ProFormSelect
                      name="group_id"
                      required
                      rules={[{ required: true, message: 'required.' }]}
                      placeholder="Select group"
                      options={taskGroups
                        ?.filter((x: any) => x.id != props.group_id)
                        ?.map((x: API.TaskGroup) => ({
                          value: x.id,
                          label: `${x.name} (${x.code})`,
                        }))}
                      formItemProps={{ style: { width: 240, marginBottom: 0 } }}
                    />
                  </ProForm>
                ),
                onOk() {
                  const selectedGroup = formConfirm.getFieldValue('group_id');
                  if (selectedGroup) {
                    setLoading(true);
                    copyBlocks(selectedGroup, props.group_id)
                      .then((res) => {
                        message.success('Copied successfully.');
                        actionRef.current?.reload();
                      })
                      .catch((reason) => {
                        Util.error(reason);
                      })
                      .finally(() => setLoading(false));
                  }
                },
              });
            }}
          >
            Copy from...
          </Button>,
          <Button
            key="reload"
            type="link"
            size="small"
            icon={<ReloadOutlined />}
            onClick={() => actionRef.current?.reload()}
          />,
        ]}
        cardProps={{ bodyStyle: { padding: 0 } }}
        actionRef={actionRef}
        editableFormRef={editableFormRef}
        headerTitle={
          'Task Blocks ' + `${dataSource.length > 0 ? `(${dataSource.length} blocks)` : ''}`
        }
        // maxLength={5}
        recordCreatorProps={false}
        columns={columns}
        request={async (params, sort, filter) => {
          setLoading(true);
          const newParams: any = { ...params, group_id: props.group_id };

          return getTaskBlockList(newParams, sort, filter).finally(() => setLoading(false));
        }}
        value={dataSource}
        onChange={setDataSource}
        editable={{
          form,
          editableKeys,
          onSave: async (key, record) => {
            setLoading(true);
            await addOrUpdateTaskBlock({ ...record, group_id: props.group_id })
              .then((res) => {
                message.success('Successfully saved.');
              })
              .finally(() => setLoading(false));
          },
          onChange: setEditableRowKeys,
          actionRender: (row, config, dom) => [dom.save, dom.cancel],
          deletePopconfirmMessage: 'Are you sure you want to delete?',
          onlyAddOneLineAlertMessage: 'You can only add one line.',
          onlyOneLineEditorAlertMessage: 'You can only edit a record at once.',
          saveText: <SaveOutlined />,
          cancelText: <CloseOutlined />,
        }}
      />
      <TaskBlockSettingModalForm
        initialValues={currentBlock ?? {}}
        modalVisible={openBlockSettingModal}
        handleModalVisible={setOpenBlockSettingModal}
        onSaveCallback={(id, newData) => {
          actionRef.current?.reload();
          return Promise.resolve();
        }}
      />
    </>
  );
};

export default TaskBlockList;
