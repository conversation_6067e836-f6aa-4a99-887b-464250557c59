/* eslint-disable */
import { ContactType } from '@/constants';
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/contact';

/** rule GET /api/contact */
export async function getContactList(
  params: API.PageParams &
    Partial<API.Contact> & {
      customer_id?: number;
      supplier_id?: number;
      address_id?: number;
      order_no?: number;
      contact_type?: ContactType;
    },
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.Contact>> {
  return request<API.Result<API.Contact>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** put PUT /api/contact */
export async function updateContact(data: Partial<API.Contact>, options?: { [key: string]: any }) {
  return request<API.Contact>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** put PUT /api/contact/order-address/{order_no} */
export async function updateOrderContact(
  order_no?: number,
  data?: Partial<API.Contact> & { by_address_id?: number; type?: string },
  options?: { [key: string]: any },
) {
  return request<API.Contact>(`${urlPrefix}/order-address/${order_no}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/contact */
export async function addContact(data: API.Contact, options?: { [key: string]: any }) {
  return request<API.Contact>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/contact/{id} */
export async function deleteContact(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}
