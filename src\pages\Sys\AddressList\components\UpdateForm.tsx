import type { Dispatch, SetStateAction } from 'react';
import { useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { Col, Divider, message, Row } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import { ProFormText, ModalForm } from '@ant-design/pro-form';
import { updateAddress } from '@/services/app/Sys/address';
import Util from '@/util';
import { useModel } from 'umi';
import ContactInfo from './ContactInfo';
import AddressContactList from '@/pages/Contact/AddressContactList';
import { AddressLoTaxTypeOptions, ContactType } from '@/constants';
import HtmlEditor from '@/components/HtmlEditor';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...');

  try {
    await updateAddress(fields);
    hide();
    message.success('Update is successful');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

// eslint-disable-next-line @typescript-eslint/ban-types
export type FormValueType = {} & Partial<API.Address>;

export type UpdateFormProps = {
  initialValues?: Partial<API.Address>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Address) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;

  customer_id?: number;
  supplier_id?: number;
  isSupplier?: boolean;
  order_type: 'OrderIn' | 'OrderOut';
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { countries } = useModel('sys-country');
  // const [country, setCountry] = useState<string | undefined>('DE');
  // const [regionList, setRegionList] = useState<DefaultOptionType[]>([]);
  const [refreshTick, setRefreshTick] = useState<number>(0);

  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef && formRef.current) {
      const newValues = { ...(props.initialValues || { country_code: 'DE' }) };
      formRef.current.setFieldsValue(newValues);
      // setCountry(props.initialValues?.country_code);
      setRefreshTick((prev) => prev + 1);
    }
  }, [formRef, props.initialValues]);

  /* useEffect(() => {
    setLoading(true);
    getCountryRegionList({ country_code: country, pageSize: 500 }, {}, {})
      .then((res) => {
        setRegionList(res.data.map((x) => ({ value: x.id, label: x.default_name })));
        formRef.current?.setFieldsValue({ region_id: res.data?.[0]?.id });
      })
      .finally(() => setLoading(false));
  }, [country]); */

  return (
    <ModalForm
      title={'Update address'}
      width="1000px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ flex: '0 0 120px' }}
      initialValues={props.initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate({ ...value, id: props.initialValues?.id });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <Row gutter={32}>
        <Col span={12}>
          <ContactInfo isSupplier={props.isSupplier} />
          <Divider />
          <ProFormText width="md" name="street" label="Street 1" placeholder="Street 1" />
          <ProFormText width="md" name="street2" label="Street 2" placeholder="Street 2" />
          <ProFormText width="sm" name="postcode" label="Zip" placeholder="Zip" />
          <ProFormText width="sm" name="city" label="City" placeholder="City" />

          <ProFormSelect
            name={['country_code']}
            label="Country"
            showSearch
            // fieldProps={{ onChange: (value) => setCountry(value) }}
            options={countries.map((x) => ({ value: x.code, label: x.name }))}
          />

          {props.isSupplier && (
            <>
              <Divider />
              <ProFormText name={['opening_hours']} label="Opening hours" />
            </>
          )}

          {!props.isSupplier && (
            <>
              <Divider />
              <ProFormSelect
                name={['lo_tax_type']}
                label="TaxFree Invoices"
                showSearch
                options={AddressLoTaxTypeOptions}
              />
            </>
          )}

          {/* <ProFormSelect
        name={['region_id']}
        label="Region"
        mode="single"
        showSearch
        fieldProps={{ loading: loading }}
        options={regionList}
      />

      <ProFormDependency name={['region_id']}>
        {(depValues) => {
          return depValues.region_id ? (
            <></>
          ) : (
            <>
              <Col style={{ paddingLeft: 120 }}>
                <Divider>or</Divider>
              </Col>
              <ProFormText
                width="md"
                name="region"
                tooltip="Fill regison in case of non-existence"
                label={null}
                wrapperCol={{ style: { marginLeft: 120 } }}
              />
            </>
          );
        }}
      </ProFormDependency> */}

          <Divider />
          {props.initialValues?.id && (
            <AddressContactList
              type={props.order_type}
              contact_type={
                props.isSupplier
                  ? ContactType.SUPPLIER_ADDRESS_CONTACT
                  : ContactType.CUSTOMER_ADDRESS_CONTACT
              }
              supplier_id={props.isSupplier ? props.supplier_id : undefined}
              customer_id={props.isSupplier ? undefined : props.customer_id}
              address_id={props.initialValues?.id}
              refreshTick={refreshTick}
            />
          )}
        </Col>
        <Col span={12}>
          <div style={{ marginBottom: 12 }}>Detail: </div>
          <ProForm.Item
            name={'detail'}
            labelCol={{ span: 0 }}
            id={`detail-${props.supplier_id}-${props.customer_id}`}
          >
            <HtmlEditor initialValue={props.initialValues?.detail} min_height={600} />
          </ProForm.Item>
        </Col>
      </Row>
    </ModalForm>
  );
};

export default UpdateForm;
