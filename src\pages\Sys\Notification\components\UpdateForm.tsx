import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import { ProFormInstance, ProFormSelect } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import { ProFormText, ModalForm } from '@ant-design/pro-form';
import { updateSysNotification } from '@/services/app/Sys/notification';
import Util from '@/util';
import { getTaskGroupACList } from '@/services/app/Task/task-group';
import SDatePicker from '@/components/SDatePicker';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...');

  try {
    await updateSysNotification(fields);
    hide();
    message.success('Update is successful');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

// eslint-disable-next-line @typescript-eslint/ban-types
export type FormValueType = {} & Partial<API.SysNotification>;

export type UpdateFormProps = {
  initialValues?: Partial<API.SysNotification>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.SysNotification) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef && formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, props.initialValues]);

  return (
    <ModalForm
      title={'Update system notification'}
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      initialValues={props.initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate({ ...value, id: props.initialValues?.id });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormText
        required
        rules={[
          {
            required: true,
            message: 'Title is required',
          },
        ]}
        width="md"
        name="title"
        label="Title"
      />
      <ProFormTextArea width="md" name="desc" label="Description" />
      <ProFormSelect
        name="type"
        label="Type"
        required
        options={[{ value: 'board', label: 'Board' }]}
        initialValue={'board'}
      />
      <ProFormSelect
        name="status"
        label="Status"
        required
        options={[
          { value: 1, label: 'Enabled' },
          { value: 0, label: 'Disabled' },
        ]}
      />
      <ProFormSelect
        name="ref_id"
        label="Task group"
        request={(params) =>
          getTaskGroupACList(params).then((res) =>
            res.map((x: API.TaskGroup) => ({ value: x.id, label: x.name })),
          )
        }
      />
      <SDatePicker name="start_date" label="Start date" />
      <SDatePicker name="end_date" label="End date" />
    </ModalForm>
  );
};

export default UpdateForm;
