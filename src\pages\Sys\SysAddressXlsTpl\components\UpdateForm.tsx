import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import ProForm, { ProFormText, ProFormUploadButton } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { updateSysAddressXlsTpl } from '@/services/app/Sys/sys-address-xls-tpl';
import Util from '@/util';
import type { RcFile } from 'antd/lib/upload';
import { XlsTemplateType, XlsTemplateTypeOptions } from '@/constants';

const handleUpdate = async (id?: number, fields?: FormValueType | FormData) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateSysAddressXlsTpl(id, fields);
    hide();
    message.success('Update is successful');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.SysAddressXlsTpl>;

export type UpdateFormProps = {
  initialValues?: Partial<API.SysAddressXlsTpl>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.SysAddressXlsTpl) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { onSubmit } = props;
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef && formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, props.initialValues]);

  return (
    <ModalForm
      title={'Update XLS Template File'}
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 7 }}
      wrapperCol={{ span: 16 }}
      initialValues={props.initialValues || {}}
      formRef={formRef}
      onFinish={async (values) => {
        const data = new FormData();
        data.set('name', `${values.name}`);
        data.set('type', `${values.type}`);
        if (values?.files) {
          data.append(`file`, values?.files[0].originFileObj as RcFile);
        }

        const success = await handleUpdate(props.initialValues?.id, data);
        if (success) {
          if (onSubmit) await onSubmit(values);
        }
      }}
    >
      <ProFormText
        required
        rules={[
          {
            required: true,
            message: 'Name is required',
          },
        ]}
        width="md"
        name="name"
        label="Name"
      />
      <ProFormSelect
        width="md"
        name="type"
        label="Type"
        rules={[
          {
            required: true,
            message: 'Type is required',
          },
        ]}
        options={XlsTemplateTypeOptions}
        initialValue={XlsTemplateType.ADDRESS}
      />
      <ProForm.Item label="Current File">
        <a
          key="file_path"
          onClick={() => {
            window.open(`${API_URL}/uploads${props.initialValues?.file_path}`, '_blank');
          }}
        >
          {props.initialValues?.file_name}
        </a>
      </ProForm.Item>
      <ProFormUploadButton
        max={1}
        name="files"
        label="New File"
        title="Select File"
        accept=".xls,.xlsx"
        required
        fieldProps={{
          beforeUpload: () => {
            return false;
          },
        }}
      />
    </ModalForm>
  );
};

export default UpdateForm;
