import type { Dispatch, SetStateAction } from 'react';
import { useMemo, useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { Button, message, Modal, Typography } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormGroup } from '@ant-design/pro-form';
import { ProFormDigit, ProFormSelect } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import Util, { sn } from '@/util';
import type { ActionType, ColumnsState, ProColumnType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { CloseSquareOutlined, ImportOutlined } from '@ant-design/icons';
import _ from 'lodash';
import {
  getOrderOutDetailFileFromXls,
  importOrderOutDetailFileFromXls,
} from '@/services/app/Order/order-out';
import type { DefaultOptionType } from 'antd/lib/select';

export const ColumnCloseComponent = ({
  setColStates,
  name,
}: {
  name: string;
  setColStates: Dispatch<SetStateAction<Record<string, ColumnsState>>>;
}) => {
  return (
    <CloseSquareOutlined
      className="cursor-pointer text-small c-gray"
      title="Close column"
      style={{ position: 'absolute', right: 4, top: 4 }}
      onClick={() => {
        setColStates((prev) => ({ ...prev, [name]: { show: false } }));
      }}
    />
  );
};

export type XlsImportSettingFormValueType = Partial<{
  col2field: any;
  headerRowNo?: number;
  dataStartRowNo?: number;
}>;

export const DefaultXlsImportSettingFormValue: XlsImportSettingFormValueType = {
  col2field: {},
  headerRowNo: 1,
  dataStartRowNo: 2,
};

export type XlsFilePreviewModalProps = {
  orderOut: Partial<API.OrderOut>;
  setOrderOut: SetStateAction<Dispatch<API.OrderOut>>;
  dbField2NamesList: API.SysXlsColNameMap[];

  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.OrderOut) => Promise<boolean | void>;
};

const XlsFilePreviewModal: React.FC<XlsFilePreviewModalProps> = (props) => {
  const { orderOut, setOrderOut, dbField2NamesList } = props;
  const { detail_file } = orderOut;

  const [__, setRefreshTick] = useState(0);
  const formRef = useRef<ProFormInstance>();

  const [loading, setLoading] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();

  const [xlsMeta, setXlsMeta] = useState<API.ExcelData>({});
  const [columns, setColumns] = useState<ProColumnType[]>([]);

  // column state managements
  const [colStates, setColStates] = useState<Record<string, ColumnsState>>({});

  const dbField2NamesListOptions = useMemo<DefaultOptionType[]>(() => {
    return dbField2NamesList.map((x) => ({
      value: x.id,
      label: `${x.db_field_comment ?? x.db_field} (${x.db_field_type})`,
    }));
  }, [dbField2NamesList]);

  useEffect(() => {
    if (props.modalVisible) {
      actionRef.current?.reload();
    }
  }, [props.modalVisible]);

  useEffect(() => {
    setColumns(() => {
      const newColumns: ProColumnType[] = [];

      newColumns.push({
        dataIndex: `${xlsMeta.cols?.length}`,
        title: 'No',
        width: 80,
        fixed: 'left',
        align: 'center',
        ellipsis: true,
      });

      xlsMeta.cols?.forEach((x, index) => {
        newColumns.push({
          dataIndex: index,
          title: (
            <>
              <ColumnCloseComponent name={`${index}`} setColStates={setColStates} />
              <Typography.Paragraph ellipsis>
                <span className="c-green">{x}</span> {xlsMeta?.header?.[index]}
              </Typography.Paragraph>
              <ProFormSelect
                name={['col2field', `${x}`]}
                options={dbField2NamesListOptions}
                fieldProps={{
                  style: { minWidth: 30 },
                  dropdownMatchSelectWidth: false,
                }}
                lightProps={{
                  style: {
                    minWidth: 150,
                  },
                }}
                formItemProps={{
                  style: { margin: 0 },
                }}
              />
              {/* {settings?.header?.[ind] && <Divider style={{ marginBottom: 4, marginTop: 4 }} />}
              {settings?.header?.[ind] && (
                <div className="c-gray" style={{ fontSize: '90%' }}>
                  {settings?.header?.[ind]}
                </div>
              )} */}
            </>
          ),
          width: (xlsMeta?.col2meta?.[x]?.width ?? 150) + 10,
          ellipsis: true,
          align: xlsMeta?.col2meta?.[x]?.align as any,
          render: (dom, record: any) => {
            const value = record[index];

            return <span>{value}</span>;
          },
          onCell: (record: any) => {
            const meta = xlsMeta?.col2meta?.[x] ?? null;
            const style: any = {};
            if (meta) {
              style.color = meta.color;
            }

            if (
              record[`${xlsMeta.cols?.length}`] == formRef.current?.getFieldValue('headerRowNo') ||
              record[`${xlsMeta.cols?.length}`] == formRef.current?.getFieldValue('dataStartRowNo')
            ) {
            } else {
              if (meta) {
                style.backgroundColor = meta.fillColor;
              }
            }
            return {
              style: {
                ...style,
              },
            };
          },
        });
      });

      return newColumns;
    });
  }, [dbField2NamesListOptions, xlsMeta]);

  useEffect(() => {
    if (!props.modalVisible) return;

    if (detail_file?.file_setting?.settings) {
      formRef.current?.setFieldsValue(detail_file?.file_setting?.settings);
    } else {
      formRef.current?.setFieldsValue(DefaultXlsImportSettingFormValue);
    }
  }, [detail_file?.file_setting?.settings, props.modalVisible]);

  const handleImport = () => {
    const settings = formRef.current?.getFieldsValue();
    if (!settings.col2field || Object.keys(settings.col2field).length < 1) {
      message.info('No mapping defined.');
      return;
    }

    setLoading(true);
    return importOrderOutDetailFileFromXls(orderOut.detail_file_id, {
      order_no: orderOut.order_no,
      settings: { ...settings, xlsMeta: xlsMeta },
    })
      .then((res: API.File) => {
        if (res) {
          message.success('Imported successfully.');
          setOrderOut((prev) => ({
            ...prev,
            detail_file: { ...prev.detail_file, file_setting: res.file_setting },
          }));
        }
      })
      .catch((err) => Util.error(err))
      .finally(() => setLoading(false));
  };

  const handleAutoMatch = () => {
    if (!xlsMeta.cols || !xlsMeta.cols?.length) return;
    if (!xlsMeta.header || !xlsMeta.header?.length) return;
    if (!dbField2NamesList || !dbField2NamesList?.length) return;

    const selectValues = formRef.current?.getFieldValue('col2field');

    xlsMeta.cols?.forEach((col, index) => {
      // Is not set
      if (!selectValues[col]) {
        const xlsHeaderText = xlsMeta.header?.[index];
        if (xlsHeaderText) {
          const dbField = _.find(
            dbField2NamesList,
            (x) => _.findIndex(x.xls_column, (v) => v == xlsHeaderText) >= 0,
          );
          if (dbField) {
            formRef.current?.setFieldValue(['col2field', col], dbField?.id);
          }
        }
      }
    });
  };

  return (
    <Modal
      title={'XLS File Preview'}
      width="80%"
      open={props.modalVisible}
      onCancel={() => props.handleModalVisible(false)}
      footer={false}
    >
      <ProForm<XlsImportSettingFormValueType>
        formRef={formRef}
        layout="horizontal"
        submitter={false}
      >
        <ProFormGroup>
          <ProFormDigit
            label="Header Row No"
            width="xs"
            name="headerRowNo"
            min={1}
            fieldProps={{
              onChange: (value) => {
                formRef.current?.setFieldValue('dataStartRowNo', Util.safeInt(value) + 1);
                actionRef.current?.reload();
              },
            }}
            required
            rules={[{ required: true, message: 'Header Row No is required.' }]}
          />
          <ProFormDigit
            label="Data Start Row"
            width="xs"
            min={1}
            name="dataStartRowNo"
            fieldProps={{
              onChange: () => {
                setRefreshTick((prev) => prev + 1);
              },
            }}
            required
            rules={[{ required: true, message: 'Start row is required.' }]}
          />
          <Button type="primary" ghost onClick={handleAutoMatch}>
            Auto match
          </Button>
          <Button type="primary" loading={loading} onClick={handleImport} icon={<ImportOutlined />}>
            Import
          </Button>
        </ProFormGroup>
        <ProTable<any, API.PageParams>
          headerTitle={'XLS Data List'}
          rowKey={`${xlsMeta.cols?.length}`}
          /* rowKey={(record) => {
            return record
          }} */
          revalidateOnFocus={false}
          actionRef={actionRef}
          options={{
            fullScreen: true,
          }}
          scroll={{ x: 800, y: 600 }}
          size="small"
          bordered
          sticky
          pagination={{
            showSizeChanger: true,
            pageSize: 20,
          }}
          rowClassName={(record) => {
            const xlsRowNo = record[sn(xlsMeta.cols?.length)];

            if (xlsRowNo == formRef.current?.getFieldValue('headerRowNo')) return 'bg-green2';
            else if (xlsRowNo == formRef.current?.getFieldValue('dataStartRowNo'))
              return 'bg-light-pink2';
            else return '';
          }}
          cardProps={{ bodyStyle: { padding: 0 } }}
          search={false}
          request={(params, sort, filter) => {
            setLoading(true);
            return getOrderOutDetailFileFromXls(
              {
                ...params,
                ...searchFormRef.current?.getFieldsValue(),
                fileId: orderOut.detail_file_id,
                order_no: orderOut.order_no,
                settings: { ...formRef.current?.getFieldsValue(), isAll: 1 },
              },
              sort,
              filter,
            )
              .then((res) => {
                setXlsMeta(res?.xlsMeta ?? {});

                /* if (res?.data?.length) {
                  setSelectedRows((prevRows) => {
                    const newRows: any[] = [];
                    prevRows.forEach((element) => {
                      const found: any = _.find(res.data, { id: element.id });
                      if (found) {
                        newRows.push({ ...found });
                      }
                    });
                    return newRows;
                  });
                } else {
                  setSelectedRows([]);
                } */
                return res;
              })
              .finally(() => setLoading(false));
          }}
          columns={columns}
          /* rowSelection={{
            onChange: (__, selectedRows) => {
              setSelectedRows(selectedRows);
            },
          }} */
          rowSelection={false}
          columnsState={{
            value: colStates,
            onChange(map) {
              setColStates(map);
            },
          }}
          columnEmptyText=""
        />
      </ProForm>
    </Modal>
  );
};

export default XlsFilePreviewModal;
