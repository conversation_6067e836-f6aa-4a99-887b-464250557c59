/**
 * Ant Design Pro v4 use `@ant-design/pro-layout` to handle Layout.
 *
 * @see You can view component api by: https://github.com/ant-design/ant-design-pro-layout
 */
import type {
  MenuDataItem,
  BasicLayoutProps as ProLayoutProps,
  Settings,
} from '@ant-design/pro-layout';
import ProLayout from '@ant-design/pro-layout';
import React, { useEffect } from 'react';
import { useModel } from 'umi';
import Footer from '@/components/Footer';
import { getAppSettings } from '@/services/app/api';
import Util from '@/util';

export type OrderLayoutProps = {
  breadcrumbNameMap: Record<string, MenuDataItem>;
  route: ProLayoutProps['route'] & {
    authority: string[];
  };
  settings: Settings;
} & ProLayoutProps;

export type OrderLayoutContext = { [K in 'location']: OrderLayoutProps[K] } & {
  breadcrumbNameMap: Record<string, MenuDataItem>;
};

const menuDataRender = (menuList: MenuDataItem[]): MenuDataItem[] =>
  menuList.map((item) => {
    return {
      ...item,
      children: item.children ? menuDataRender(item.children) : undefined,
    };
  });

const OrderLayout: React.FC<OrderLayoutProps> = (props) => {
  const { children } = props;

  const { initialState } = useModel('@@initialState');
  const { setAppSettings } = useModel('app-settings');

  useEffect(() => {
    if (initialState?.currentUser) {
      getAppSettings()
        .then((res) => setAppSettings(res))
        .catch(() => Util.error('Failed to fetch app settings. Please try to reload a page!'));
    }
  }, [initialState?.currentUser, setAppSettings]);

  // const { formatMessage } = useIntl();

  return (
    <ProLayout
      locale={undefined}
      logo={<></>}
      // formatMessage={formatMessage}
      {...props}
      //onCollapse={handleMenuCollapse}
      //onMenuHeaderClick={() => history.push('/')}
      hide
      hidden
      headerRender={false}
      /* menuItemRender={(menuItemProps, defaultDom) => {
        if (
          menuItemProps.isUrl ||
          !menuItemProps.path ||
          location.pathname === menuItemProps.path
        ) {
          return defaultDom;
        }
        return <Link to={menuItemProps.path}>{defaultDom as any}</Link>;
      }}
      breadcrumbRender={(routers = []) => [
        {
          path: '/',
          breadcrumbName: formatMessage({ id: 'menu.home' }),
        },
        ...routers,
      ]}
      itemRender={(route, params, routes, paths) => {
        const first = routes.indexOf(route) === 0;
        return first ? (
          <Link to={paths.join('/')}>{route.breadcrumbName}</Link>
        ) : (
          <span>{route.breadcrumbName}</span>
        );
      }} */
      hasSiderMenu={false}
      footerRender={() => <Footer />}
      //   menuDataRender={menuDataRender}
      //rightContentRender={() => <RightContent />}
    >
      {children}
    </ProLayout>
  );
};

export default OrderLayout;
