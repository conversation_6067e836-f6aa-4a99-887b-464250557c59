import { useModel } from 'umi';
import { DictCode, TaskBlockLayout } from '@/constants';

import { sn, urlFull } from '@/util';
import TaskLayoutT from '../TaskLayout/TaskLayoutT';
import TaskLayoutOrderA from '../TaskLayoutOrder/TaskLayoutOrderA';
import TaskLayoutA from '../TaskLayout/TaskLayoutA';
import TaskLayoutB from '../TaskLayout/TaskLayoutB';
import TaskLayoutC from '../TaskLayout/TaskLayoutC';
import type { onTaskClickType } from '../..';
import { TaskClickAction } from '../..';

export type TaskProps = {
  index: number; // index no in list.
  task: API.Task;
  onTaskClick?: onTaskClickType;

  groupId?: number;
  droppableId: string; // block ID in DnD
  block?: API.TaskBlock;
};

export const Task: React.FC<TaskProps> = (props) => {
  const { task, index } = props;

  const { initialState } = useModel('@@initialState');
  const { getBlockLayout } = useModel('task-group');
  const { getCodeValue } = useModel('app-settings');
  const layout = getBlockLayout(task.block_id);

  let innerDom = null;
  if (task.ui_type) {
    if (task.ref_type == 'OrderIn' || task.ref_type == 'OrderOut')
      innerDom = <TaskLayoutOrderA {...props} />;
    else {
      innerDom = <TaskLayoutT {...props} />;
    }
  } else {
    if (layout == TaskBlockLayout.LAYOUT_T_DATE) {
      innerDom = <TaskLayoutT {...props} />;
    } else if (layout == TaskBlockLayout.LAYOUT_A) {
      if (task.ref_type == 'OrderIn' || task.ref_type == 'OrderOut')
        innerDom = <TaskLayoutOrderA {...props} />;
      else innerDom = <TaskLayoutA {...props} />;
    } else if (layout == TaskBlockLayout.LAYOUT_B) {
      innerDom = <TaskLayoutB {...props} />;
    } else if (layout == TaskBlockLayout.LAYOUT_ORDER_C) {
      innerDom = <TaskLayoutOrderA {...props} />;
    } else {
      innerDom = <TaskLayoutC {...props} />;
    }
  }

  const styles: React.CSSProperties = {};
  if (sn(task.sub_task_groups_count) > 0) {
    styles.backgroundColor = getCodeValue(DictCode.PARENT_TASK_BG) ?? undefined;
  }

  if (task.users?.length && task.users?.[0].initials == initialState?.currentUser?.initials) {
    styles.backgroundColor = '#fff7e6';
  }

  // If assigners exist, we override background as orange.
  if (task.settings?.bgColor) {
    styles.backgroundColor = task.settings?.bgColor;
  }

  return (
    <div
      className={`task`}
      onClick={(e) => {
        e.stopPropagation();
        if (task.ref_type == 'OrderOut') {
          window.open(
            urlFull('/order-out-detail/' + task.ref_id + '?task_id=' + task.id),
            '_blank',
          );
        } else {
          props.onTaskClick?.(task, props.droppableId, index, e, TaskClickAction.update);
        }
      }}
      style={styles}
    >
      {innerDom}
    </div>
  );
};

export default Task;
