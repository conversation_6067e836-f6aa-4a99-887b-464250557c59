.container {
  :global {
    background-color: white;

    .ant-pro-page-container-children-content {
      margin-top: 0;
    }
    .wrap {
      align-items: stretch;
      .middle-wrap .panel {
        min-height: 100%;
      }
    }

    .left-wrap,
    .right-wrap {
      /* flex: 0 0 300px;
      max-width: 300px; */
      .panel .task-list {
        height: calc(50vh - 150px);
        &:last-child {
          margin-bottom: 24px;
        }
      }
    }

    .panel {
      .panel-header {
        padding: 8px 8px 0 12px;
        font-weight: bold;
      }

      .task-list {
        width: 100%;
        max-width: 100%;
        min-height: 100%;
        padding: 8px 8px 8px;
        overflow-y: auto;
        background-color: rgb(235, 236, 240);
        border-color: rgba(23, 43, 77, 0.6);
        border-radius: 5px;
        box-shadow: var(
          rgba(23, 43, 77, 0.2),
          0 1px 1px rgba(23, 43, 77, 0.2),
          0 0 1px rgba(23, 43, 77, 0.2)
        );

        &.full {
          height: auto;
          min-height: calc(50vh - 150px);
          overflow-y: initial;
        }

        &.middle {
          height: calc(100vh - 240px);
          min-height: calc(100vh - 230px);
          &.full {
            height: initial;
            overflow-y: initial;
          }
        }

        &.dragging {
          background-color: #e6f7ff;
        }

        .task {
          /* display: flex; */
          box-sizing: border-box;
          min-height: 40px;
          margin-bottom: 8px;
          padding: 8px;
          background-color: rgb(255, 255, 255);
          border: 1px solid transparent;
          border-radius: 4px;
          box-shadow: var(
            rgba(23, 43, 77, 0.2),
            0 1px 1px rgba(23, 43, 77, 0.2),
            0 0 1px rgba(23, 43, 77, 0.2)
          );
          cursor: grab;
          user-select: none;

          &.t-dragging {
            background-color: rgb(226, 232, 244);
            border-color: rgb(74, 101, 149);
          }

          .ant-tag-small {
            &.fixed {
              width: 20px;
            }
            &.empty {
              opacity: 0.6;
              &:hover {
                opacity: 1;
              }
            }
          }
        }
      }
    }

    .panel.narrow {
      .task-list {
        .task {
          min-height: 20px;
          margin-bottom: 2px;
          padding: 0 1px 0 4px;
        }
      }
    }

    .float-wrap {
      position: absolute;
      top: 100px;
      left: 50%;
      z-index: 10;
      width: 400px;
      height: 500px;
      margin-left: -200px;
      background: white;
      border: 1px solid #eee;
      border-radius: 2px;
      box-shadow: none;
      box-shadow: var(
        rgba(23, 43, 77, 0.2),
        0 1px 1px rgba(23, 43, 77, 0.2),
        0 0 1px rgba(23, 43, 77, 0.2)
      );
      & > div,
      & > div > div {
        height: 100%;
      }
      .panel,
      .task-list {
        height: 100%;
        min-height: 100%;
      }
    }

    .calendar {
      .task {
        box-sizing: border-box;
        min-height: 30px;
        margin-bottom: 8px;
        padding: 2px 8px;
        background-color: rgb(255, 255, 255);
        border: 1px solid transparent;
        border-radius: 4px;
        box-shadow: var(
          rgba(23, 43, 77, 0.2),
          0 1px 3px rgba(23, 43, 77, 0.2),
          0 0 1px rgba(23, 43, 77, 0.2),
          0 -1px 0 rgba(23, 43, 77, 0.1)
        );
        user-select: none;

        .ant-tag-small {
          &.fixed {
            width: 20px;
          }
          &.empty {
            opacity: 0.6;
            &:hover {
              opacity: 1;
            }
          }
        }
      }
      .calendar-date {
        .header-ym {
          margin-bottom: 12px;
          font-weight: bold;
        }
      }
      .calendar-order-card,
      .calendar-general-card {
        .task {
          //tmp
          padding: 3px 8px;
        }
      }
    }
  }
}
