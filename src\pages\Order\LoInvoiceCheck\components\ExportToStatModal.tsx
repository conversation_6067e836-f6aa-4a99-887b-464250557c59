import SProFormDigit from '@/components/SProFormDigit';
import Util, { getLexOfficeLink, isSnDiff, nf2, sn, toRound, urlFull } from '@/util';
import {
  DeleteFilled,
  EditOutlined,
  ImportOutlined,
  SaveOutlined,
  LoadingOutlined,
  CheckSquareOutlined,
  DeleteOutlined,
  CalculatorOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import type { ActionType, ColumnsState } from '@ant-design/pro-table';
import ProTable, {
  EditableProTable,
  type EditableFormInstance,
  type ProColumns,
} from '@ant-design/pro-table';
import { Button, Card, Col, Divider, Modal, Popconfirm, Row, Space, message } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import './ExportToStatModal.less';
import moment from 'moment';
import { DT_FORMAT_YMD, DictCode, EURO2, LS_TOKEN_NAME } from '@/constants';
import {
  deleteFinDetailList,
  getFinDetailListWithImportable,
  updateFinDetailListWithImportable,
} from '@/services/app/Fin/fin-detail';
import OrderOutInvoiceLineItems from './OrderOutInvoiceLineItems';
import { getFinInventurAll } from '@/services/app/Fin/fin-inventur-all';
import {
  ProFormCheckbox,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormUploadButton,
} from '@ant-design/pro-form';
import { getSupplierACList } from '@/services/app/BasicData/supplier';
import type { DefaultOptionType } from 'antd/lib/select';
import SDatePicker from '@/components/SDatePicker';
import {
  deleteOrderOutToLoInvoiceCheck,
  updateLoInvoiceCheck,
} from '@/services/app/LexOffice/lo-voucher-list';
import { useModel } from 'umi';
import OrderOutSelectionModal from './OrderOutSelectionModal';
import type { HandleNavFuncType } from '../hooks/useModalNavigation';
import ModalNavigation from '../hooks/ModalNavigation';
import { getOrderOutList } from '@/services/app/Order/order-out';

moment.locale('de', {
  week: {
    dow: 1, /// Date offset
  },
});

export type WarningType = {
  hs_code?: any;
  net_weight?: any;
  Artikelnummer?: any;
  EK_Summe?: any;
  Summe?: any;
  Supplier?: any;
  SummeDiff?: any;
  EkSummeDiff?: any;
};

export const getWarningText = (x: string) => {
  switch (x) {
    case 'hs_code':
      return 'without HS Code';
    case 'net_weight':
      return 'without Net Weight';
    case 'Artikelnummer':
      return 'without  Item No';
    case 'EK_Summe':
      return 'without EK Summe';
    case 'Summe':
      return 'without Summe';
    case 'Supplier':
      return 'without Supplier';
    case 'SummeDiff':
      return 'Summe & Sum_VKKg different';
    case 'EkSummeDiff':
      return 'EK Summe & Sum_EKKg different';
  }
  return '';
};

const defaultDisabledFormItemProp = {
  placeholder: '',
  disabled: true,
  bordered: false,
  style: {
    background: 'transparent',
    color: 'black',
    paddingLeft: 0,
  },
};

export type EditableRowType = Partial<API.FinDetail> & {
  uid?: React.Key;
};

const defaultTotalRow: EditableRowType = {
  Gepackt: 0,
  Order: 0,
  Summe: 0,
  EK_Summe: 0,
  Ertrag: 0,
  ek_kg_sum: 0,
  vk_kg_sum: 0,
  net_weight: 0,
};

const validateRowClassname = (row: EditableRowType) => {
  // UI classname
  let rowCls = '';
  if (!row.Artikelnummer) {
    rowCls += ` bg-light-red`;
    return rowCls;
  }

  if (sn(row.Artikelnummer) >= 10 && (!row.hs_code || !row.net_weight)) {
    if (row.Gepackt) rowCls += ` bg-yellow`;
  }

  if (sn(row.Artikelnummer) >= 10 && row.supplier_name == null) {
    if (row.Gepackt) rowCls += ` bg-light-orange2`;
  }

  /* if (
    sn(row.Artikelnummer) >= 10 &&
    (Util.isZero(sn(row.Summe)) || Util.isZero(sn(row.EK_Summe)))
  ) {
    rowCls += ` bg-light-red`;
  } */
  if (row.Gepackt && row.net_unit_weight) {
    if (Math.round(toRound(sn(row.Summe), 2) - toRound(sn(row.vk_kg_sum), 2)) > 0.05) {
      rowCls += ` bg-light-red`;
    }
    if (Math.round(toRound(sn(row.EK_Summe), 2) - toRound(sn(row.ek_kg_sum), 2)) > 0.05) {
      rowCls += ` bg-light-red`;
    }
  }

  return rowCls;
};

export type ExportToStatModalProps = {
  invoiceCheck: Partial<API.LoInvoiceCheck>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.OrderOutDetail) => Promise<boolean | void>;
  updateParentDs?: (ds: API.LoInvoiceCheck) => void;
  reloadParentTable?: () => void;

  handleNavigation?: HandleNavFuncType;
};

const ExportToStatModal: React.FC<ExportToStatModalProps> = (props) => {
  const { invoiceCheck, handleNavigation } = props;
  const { lo_id, lo_no, order_out } = invoiceCheck;
  const { getCodeValue } = useModel('app-settings');

  const [loading, setLoading] = useState(false);
  const uploadingRef = useRef<any>();

  const parentReloadRequired = useRef<boolean>(false);

  // basic data
  const [suppliers, setSuppliers] = useState<
    (Partial<API.Supplier> & { uid?: string } & DefaultOptionType)[]
  >([]);

  // Editable table form
  const actionRef = useRef<ActionType>();
  const editableFormRef = useRef<EditableFormInstance>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);
  const [dataSource, setDataSource] = useState<EditableRowType[]>(() => []);
  const [___, setTick] = useState<number>(0); // Used to refresh purpose.

  // UX for live editable
  const csRef = useRef<Record<string, boolean>>({}); // Store changed status of cell.
  const [loadings, setLoadings] = useState<Record<string, boolean>>({}); // loading status per editable cell.

  // column state managements
  const [colStates, setColStates] = useState<Record<string, ColumnsState>>({
    Field1: { show: false },
    Re_Nr: { show: false },
    Kunde: { show: false },
    Datum: { show: false },
    vk_kg_price: { show: true },
    vk_kg_sum: { show: true },
    ek_kg_price: { show: true },
    ek_kg_sum: { show: true },
  });

  const calcTotal = useCallback((data: readonly EditableRowType[]) => {
    const newTotalRow: EditableRowType = { ...defaultTotalRow };
    data.forEach((x) => {
      newTotalRow.Gepackt += sn(x.Gepackt);
      newTotalRow.Order += sn(x.Order);
      newTotalRow.Summe += sn(x.Summe);
      newTotalRow.EK_Summe += sn(x.EK_Summe);
      newTotalRow.Ertrag += sn(x.Ertrag);
      newTotalRow.ek_kg_sum += sn(x.ek_kg_sum);
      newTotalRow.vk_kg_sum += sn(x.vk_kg_sum);
      newTotalRow.net_weight += sn(x.net_weight);
    });
    return newTotalRow;
  }, []);

  /**
   * Get/Set changed status in editable cells.
   * @param dataIndex
   * @param uid
   * @param newStatus
   * @returns
   */
  const cellChangeStatus = (dataIndex: any, uid: any, newStatus?: boolean) => {
    const cellKey = `${JSON.stringify(dataIndex)}^${uid}`;
    if (newStatus === undefined) return !!csRef.current[cellKey];
    csRef.current = {
      ...csRef.current,
      [cellKey]: newStatus,
    };
    return newStatus;
  };

  const getMergedDataSource = (prev: EditableRowType[]): EditableRowType[] => {
    const newDs = [...prev];
    const rows = editableFormRef.current?.getFieldsValue?.();
    newDs.forEach((x, ind) => {
      if (rows[`${x.uid}`]) {
        newDs[ind] = { ...x, ...rows[`${x.uid}`] };
      }
    });
    return newDs;
  };

  // validation of editable tabels
  const [warnings, setWarnings] = useState<WarningType>({});

  /**
   * dsParam: dataSource
   */
  const validatePreviewTableData = useCallback((dsParam: EditableRowType[]) => {
    const rows = getMergedDataSource(dsParam);
    const newWarnings: WarningType = {};

    for (const row of rows) {
      if (sn(row.Artikelnummer) >= 10) {
        if (row.hs_code == null) newWarnings.hs_code = sn(newWarnings.hs_code) + 1;
        if (row.net_weight == null) newWarnings.net_weight = sn(newWarnings.net_weight) + 1;
        if (row.EK_Summe == null) newWarnings.EK_Summe = sn(newWarnings.EK_Summe) + 1;
        if (row.Summe == null) newWarnings.Summe = sn(newWarnings.Summe) + 1;
        if (row.supplier_name == null) newWarnings.Supplier = sn(newWarnings.Supplier) + 1;
      }

      if (!row.Artikelnummer) newWarnings.Artikelnummer = sn(newWarnings.Artikelnummer) + 1;

      // summe diff
      if (row.Gepackt && row.net_unit_weight) {
        if (Math.abs(toRound(sn(row.Summe), 2) - toRound(sn(row.vk_kg_sum), 2)) > 0.05) {
          newWarnings.SummeDiff = sn(newWarnings.SummeDiff) + 1;
        }
        if (Math.abs(toRound(sn(row.EK_Summe), 2) - toRound(sn(row.ek_kg_sum), 2)) > 0.05) {
          newWarnings.EkSummeDiff = sn(newWarnings.EkSummeDiff) + 1;
        }
      }
    }
    setWarnings(newWarnings);
  }, []);

  const handleCellChange = (dataIndex: any, uid: any, data?: any) => {
    const isChanged = cellChangeStatus(dataIndex, uid);
    // Cell value changed. So we need to biz logic
    if (isChanged) {
      const cellKey = (dataIndex as any)?.[0] ?? '';
      console.log('cell changed started.', cellKey);
      const rowData: EditableRowType = editableFormRef.current?.getRowData?.(uid) ?? {};
      let rerenderRequired = false;
      const qty = sn(rowData.Gepackt);
      const net_weight = qty * sn(rowData.net_unit_weight);

      let updateData: Partial<EditableRowType> = {};
      switch (cellKey) {
        case 'Gepackt':
          updateData = {
            Summe: qty * sn(rowData.Preis),
            EK_Summe: qty * sn(rowData.EK),
            Ertrag: qty * (sn(rowData.Preis) - sn(rowData.EK)),
            net_weight: net_weight,
            ek_kg_sum: net_weight * sn(rowData.ek_kg_price),
            vk_kg_sum: net_weight * sn(rowData.vk_kg_price),
          };
          if (sn(rowData.Artikelnummer) < 10) {
            updateData.Ertrag = 0;
          }
          break;
        case 'Preis':
          updateData = {
            Summe: qty * sn(rowData.Preis),
            Ertrag: qty * (sn(rowData.Preis) - sn(rowData.EK)),
          };
          if (!rowData.net_unit_weight) {
            updateData.vk_kg_price = 0;
          } else {
            updateData.vk_kg_price = sn(rowData.Preis) / sn(rowData.net_unit_weight);
          }
          updateData.net_weight = net_weight;
          updateData.vk_kg_sum = net_weight * sn(updateData.vk_kg_price);
          if (sn(rowData.Artikelnummer) < 10) {
            updateData.Ertrag = 0;
          }
          break;
        case 'EK':
          updateData = {
            EK_Summe: qty * sn(rowData.EK),
            Ertrag: qty * (sn(rowData.Preis) - sn(rowData.EK)),
          };
          if (!rowData.net_unit_weight) {
            updateData.ek_kg_price = 0;
          } else {
            updateData.ek_kg_price = sn(rowData.EK) / sn(rowData.net_unit_weight);
          }
          updateData.net_weight = net_weight;
          updateData.ek_kg_sum = net_weight * sn(updateData.ek_kg_price);
          if (sn(rowData.Artikelnummer) < 10) {
            updateData.Ertrag = 0;
          }
          break;
        case 'Summe':
          updateData = {
            Preis: sn(rowData.Summe) / qty,
            Ertrag: sn(rowData.Summe) - qty * sn(rowData.EK),
          };
          if (sn(rowData.Artikelnummer) < 10) {
            updateData.Ertrag = 0;
          }
          break;
        case 'net_unit_weight':
          updateData = {
            net_weight: net_weight,
          };
          break;
        case 'ek_kg_price':
          updateData = {
            ek_kg_sum: net_weight * sn(rowData.ek_kg_price),
          };
          break;
        case 'vk_kg_price':
          updateData = {
            vk_kg_sum: net_weight * sn(rowData.vk_kg_price),
          };
          break;
        case 'supplier_name':
          updateData = {
            supplier_org_a: data?.org_a || '',
          };
          setDataSource((prev) => {
            const newDs = [...prev];
            const found = newDs.find((x) => x.uid == uid);
            if (found) {
              found.supplier_org_a = data?.org_a || '';
            }
            return newDs;
          });
          rerenderRequired = true;
          break;
        case 'Artikelnummer':
          // If description is empty, we need to get it from fin_inventual_all
          if (rowData.Artikelnummer) {
            setLoadings((prev) => ({
              ...prev,
              [`${cellKey}^${uid}`]: true,
              [`Beschreibung^${uid}`]: true,
              [`Kategorie^${uid}`]: true,
            }));

            getFinInventurAll(rowData.Artikelnummer)
              .then((res) => {
                const newKategory =
                  sn(rowData.Artikelnummer) < 100 ? res.Bezeichnung : res.Kategorie;

                updateData = {
                  Kategorie: newKategory,
                };
                if (!rowData.Beschreibung) {
                  updateData.Beschreibung = res.Bezeichnung;
                }

                // EK updates
                if (!rowData.EK) {
                  updateData.EK = sn(res.EK);
                  updateData.EK_Summe = qty * sn(updateData.EK);

                  updateData.Ertrag = updateData.EK
                    ? qty * (sn(rowData.Preis) - sn(updateData.EK))
                    : 0;

                  if (!rowData.net_unit_weight) {
                    updateData.ek_kg_price = 0;
                  } else {
                    updateData.ek_kg_price = sn(updateData.EK) / sn(rowData.net_unit_weight);
                  }
                  updateData.net_weight = net_weight;
                  updateData.ek_kg_sum = net_weight * sn(updateData.ek_kg_price);
                }

                // Override
                if (sn(rowData.Artikelnummer) < 10) {
                  if (!updateData.EK) {
                    updateData.EK = 0;
                    updateData.EK_Summe = 0;
                  }

                  updateData.supplier_name = null;
                  updateData.supplier_org_a = null;
                }
                if (!updateData.EK && sn(rowData.Artikelnummer) >= 100) {
                  updateData.Ertrag = 0;
                } else if (sn(rowData.Artikelnummer) >= 10) {
                  updateData.Ertrag = qty * (sn(rowData.Preis) - sn(rowData.EK));
                }

                editableFormRef.current?.setRowData?.(uid, updateData);
                setDataSource((prev) => {
                  const newDs = [...prev];
                  const found = newDs.find((x) => x.uid == uid);
                  if (found) {
                    Object.assign(found, updateData);
                  }
                  return newDs;
                });
              })
              .catch((err) => {
                if (err?.data?.code === 404) {
                  updateData = {
                    Kategorie: '',
                  };
                  editableFormRef.current?.setRowData?.(uid, updateData);
                  setDataSource((prev) => {
                    const newDs = [...prev];
                    const found = newDs.find((x) => x.uid == uid);
                    if (found) {
                      Object.assign(found, updateData);
                    }
                    return newDs;
                  });
                } else {
                  Util.error(err);
                }
              })
              .finally(() => {
                setLoadings((prev) => ({
                  ...prev,
                  [`${cellKey}^${uid}`]: false,
                  [`Beschreibung^${uid}`]: false,
                  [`Kategorie^${uid}`]: false,
                }));
              });
          }
          rerenderRequired = true;
          break;
      }

      editableFormRef.current?.setRowData?.(uid, updateData);

      // Re-render of Table Summary row is needed.
      if (!rerenderRequired) {
        setTick((prev) => prev + 1);
      }
    }
    // reset cell's changed status.
    cellChangeStatus(dataIndex, uid, false);
    validatePreviewTableData(dataSource);
  };

  const reCalcCol = (cellKey: string) => {
    const rows = getMergedDataSource(dataSource);
    // const newWarnings: WarningType = {};

    for (const rowData of rows) {
      /* if (sn(row.Artikelnummer) >= 10) {
        if (row.hs_code == null) newWarnings.hs_code = sn(newWarnings.hs_code) + 1;
        if (row.net_weight == null) newWarnings.net_weight = sn(newWarnings.net_weight) + 1;
        if (row.EK_Summe == null) newWarnings.EK_Summe = sn(newWarnings.EK_Summe) + 1;
        if (row.Summe == null) newWarnings.Summe = sn(newWarnings.Summe) + 1;
        if (row.supplier_name == null) newWarnings.Supplier = sn(newWarnings.Supplier) + 1;
      }

      if (!row.Artikelnummer) newWarnings.Artikelnummer = sn(newWarnings.Artikelnummer) + 1;

      // summe diff
      if (row.Gepackt && row.net_unit_weight) {
        if (toRound(sn(row.Summe), 2) != toRound(sn(row.vk_kg_sum), 2)) {
          newWarnings.SummeDiff = sn(newWarnings.SummeDiff) + 1;
        }
        if (toRound(sn(row.EK_Summe), 2) != toRound(sn(row.ek_kg_sum), 2)) {
          newWarnings.EkSummeDiff = sn(newWarnings.EkSummeDiff) + 1;
        }
      } */

      const qty = sn(rowData.Gepackt);
      const net_unit_weight = sn(rowData.net_unit_weight);
      const net_weight = qty * net_unit_weight;
      // let updateData: Partial<EditableRowType> = {};

      switch (cellKey) {
        case 'Preis':
          if (sn(rowData.Artikelnummer) >= 10) {
            rowData.Preis = (sn(rowData.vk_kg_price) * net_weight) / qty;
            rowData.Summe = qty * rowData.Preis;
            rowData.Ertrag = qty * rowData.Preis - sn(rowData.EK);
          }
          break;
        case 'EK':
          rowData.EK = (sn(rowData.ek_kg_price) * net_weight) / qty;
          rowData.EK_Summe = qty * rowData.EK;
          rowData.Ertrag = qty * (sn(rowData.Preis) - sn(rowData.EK));
          break;
        case 'ek_kg_price':
          rowData.ek_kg_price = sn(rowData.EK_Summe) / net_weight;
          rowData.ek_kg_sum = rowData.ek_kg_price * net_weight;
          break;
        case 'vk_kg_price':
          rowData.vk_kg_price = sn(rowData.Summe) / net_weight;
          rowData.vk_kg_sum = rowData.vk_kg_price * net_weight;
          break;
      }

      editableFormRef.current?.setRowData?.(`${rowData.uid}`, rowData);
    }

    setDataSource(rows);
    validatePreviewTableData(dataSource);
  };

  const defaultDigitColumProps: Partial<ProColumns<EditableRowType>> = {
    renderFormItem: (item, { record, recordKey }, form, actions) => {
      const cellKey = (item.dataIndex as any)[0] ?? '';
      let isReadOnly = false;
      if (cellKey == 'Artikelnummer') {
        isReadOnly = loadings[`${cellKey}^${record?.uid}`] ?? false;
      }
      const formFieldId = `${recordKey}_${(item?.dataIndex as any)?.['0']}`;

      return (
        <SProFormDigit
          formItemProps={{ ...item.formItemProps, style: { marginBottom: 5, marginTop: 5 } }}
          fieldProps={{
            id: formFieldId,
            controls: false,
            onChange(value) {
              cellChangeStatus(item.dataIndex, record?.uid, true);
            },
            onBlur(e) {
              handleCellChange(item.dataIndex, record?.uid);
            },
            onKeyDown(e) {
              if (Util.isTabPressed(e)) {
                if ((item.dataIndex as any)[0] == 'Gepackt') {
                  e.preventDefault();
                  (document.getElementById(`${recordKey}_Preis`) as any)?.focus();
                  (document.getElementById(`${recordKey}_Preis`) as any)?.select();
                } else if ((item.dataIndex as any)[0] == 'Preis') {
                  e.preventDefault();
                  (document.getElementById(`${recordKey}_EK`) as any)?.focus();
                  (document.getElementById(`${recordKey}_EK`) as any)?.select();
                }
              }
            },
            size: 'small',
            disabled: isReadOnly,
            ...(item.fieldProps as any),
          }}
        />
      );
    },
  };

  const columns: ProColumns<EditableRowType>[] = [
    {
      title: 'Field1',
      dataIndex: ['Field1'],
      width: 60,
      fieldProps: { placeholder: 'Field1', size: 'small' },
    },
    {
      title: 'Re Nr',
      dataIndex: ['Re_Nr'],
      valueType: 'text',
      width: 70,
      fieldProps: { placeholder: 'Re Nr', size: 'small' },
    },
    {
      title: 'Kunde',
      dataIndex: ['Kunde'],
      width: 120,
      fieldProps: { placeholder: 'Kunde', size: 'small' },
    },
    {
      title: 'Datum',
      dataIndex: ['Datum'],
      valueType: 'date',
      width: 120,
      renderFormItem: (item, { record }) => {
        return (
          <SDatePicker
            fieldProps={{
              ...(item.fieldProps as any),
            }}
            formItemProps={{ ...item.formItemProps, style: { marginBottom: 0, width: '100%' } }}
          />
        );
      },
    },
    {
      title: 'Artikelnummer',
      dataIndex: ['Artikelnummer'],
      valueType: 'text',
      width: 50,
      fieldProps: {
        placeholder: 'Artikelnummer',
        size: 'small',
      },
      renderFormItem: (item, { record, recordKey, value }, form) => {
        const cellKey = (item.dataIndex as any)[0] ?? '';
        const isReadOnly = loadings[`${cellKey}^${record?.uid}`] ?? false;

        return (
          <SProFormDigit
            noFormatting
            fieldProps={{
              controls: false,
              onChangeCapture(e) {
                const changedValue = (e.target as any).value;
                if (changedValue == 'fd') {
                  (e.target as any).value = 20;
                  /* form?.setFieldValue([recordKey as string, 'Artikelnummer'], 20);
                  editableFormRef.current?.setRowData?.(record?.uid as string, {
                    Artikelnummer: 20,
                  }); */
                  cellChangeStatus(item.dataIndex, record?.uid, true);
                }
              },
              onChange(changedValue) {
                cellChangeStatus(item.dataIndex, record?.uid, true);
              },
              onBlur(e) {
                /* const fieldValue = (e.target as any).value;
                if (fieldValue == 'fd') {
                  console.log(
                    'fd captured',
                    form,
                    form.getFieldValue(['Artikelnummer']),
                    form.getFieldsValue(),
                  );
                  form?.setFieldValue([recordKey as string, 'Artikelnummer'], 20);
                } */
                handleCellChange(item.dataIndex, record?.uid);
              },
              onKeyDown(e) {
                if (Util.isTabPressed(e)) {
                  e.preventDefault();
                  (document.getElementById(`${recordKey}_Gepackt`) as any)?.focus();
                  (document.getElementById(`${recordKey}_Gepackt`) as any)?.select();
                } else {
                  // const fieldValue = (e.target as any).value;
                  /* const fieldValue = editableFormRef.current?.getFieldValue([
                    recordKey as string,
                    'Artikelnummer',
                  ]);
                  console.log('onKeyDown', fieldValue);
                  if (fieldValue == 'fd') {
                    console.log('onKeyDown fd capture', fieldValue);
                    e.preventDefault();
                    form?.setFieldValue([recordKey as string, 'Artikelnummer'], 20);
                    // editableFormRef.current?.setRowData(item.index, { Artikelnummer: 20 });
                  } */
                }
              },
              size: 'small',
              disabled: isReadOnly,
              ...(item.fieldProps as any),
            }}
            formItemProps={{ ...item.formItemProps, style: { marginBottom: 0 } }}
          />
        );
      },
    },
    {
      title: 'Beschreibung',
      dataIndex: ['Beschreibung'],
      width: 160,
      fieldProps: { placeholder: 'Beschreibung', size: 'small' },
      tooltip: 'Will be shortened to 100 chars',
      renderFormItem: (item, { record }) => {
        const isReadOnly = loadings[`Beschreibung^${record?.uid}`] ?? false;
        return (
          <ProFormText
            fieldProps={{
              readOnly: isReadOnly,
              ...(item.fieldProps as any),
              suffix: isReadOnly ? <LoadingOutlined /> : undefined,
            }}
            formItemProps={{ ...item.formItemProps, style: { marginBottom: 0, width: '100%' } }}
          />
        );
      },
    },
    {
      title: 'Gepackt',
      dataIndex: ['Gepackt'],
      width: 60,
      fieldProps: { placeholder: 'Gepackt', precision: 4 },
      ...defaultDigitColumProps,
    },
    {
      title: 'Net kg / pcs',
      dataIndex: ['net_unit_weight'],
      width: 50,
      fieldProps: { placeholder: 'Net kg / pcs', precision: 4 },
      ...defaultDigitColumProps,
      onCell: () => {
        return {};
      },
    },
    {
      title: 'Total Net kg',
      dataIndex: ['net_weight'],
      width: 65,
      fieldProps: {
        precision: 3,
        ...defaultDisabledFormItemProp,
      },
      ...defaultDigitColumProps,
      onCell: () => {
        return {};
      },
    },
    {
      title: (
        <Row style={{ display: 'flex' }} gutter={6}>
          <Col>Preis</Col>
          <Col>
            <CalculatorOutlined
              className="cursor-pointer"
              title="Recalc Preis & Summe: VK/KG * TotalNetKG / Gepackt"
              onClick={() => reCalcCol('Preis')}
            />
          </Col>
        </Row>
      ),
      dataIndex: ['Preis'],
      fieldProps: { placeholder: 'Preis', precision: 4 },
      width: 60,
      ...defaultDigitColumProps,
    },
    {
      title: (
        <Row style={{ display: 'flex' }} gutter={6}>
          <Col>VK/kg</Col>
          <Col>
            <CalculatorOutlined
              className="cursor-pointer"
              title="Recalc VK/KG: Summe / TotelNetKG"
              onClick={() => reCalcCol('vk_kg_price')}
            />
          </Col>
        </Row>
      ),
      dataIndex: ['vk_kg_price'],
      fieldProps: { placeholder: 'VK/kg', precision: 4 },
      width: 60,
      ...defaultDigitColumProps,
    },
    {
      title: 'Summe',
      dataIndex: ['Summe'],
      width: 70,
      fieldProps: { placeholder: 'Summe', precision: 2 },
      ...defaultDigitColumProps,
    },
    {
      title: 'Sum_VKKg ',
      dataIndex: ['vk_kg_sum'],
      fieldProps: { ...defaultDisabledFormItemProp, precision: 2 },
      width: 70,
      ...defaultDigitColumProps,
    },

    {
      title: (
        <Row style={{ display: 'flex' }} gutter={6}>
          <Col>EK</Col>
          <Col>
            <CalculatorOutlined
              className="cursor-pointer"
              title="Recalc Preis & Summe: EK/KG * TotalNetKG / Gepackt."
              onClick={() => reCalcCol('EK')}
            />
          </Col>
        </Row>
      ),
      dataIndex: ['EK'],
      width: 55,
      fieldProps: { placeholder: 'EK', precision: 4 },
      ...defaultDigitColumProps,
    },
    {
      title: (
        <Row style={{ display: 'flex' }} gutter={6}>
          <Col>EK/kg</Col>
          <Col>
            <CalculatorOutlined
              className="cursor-pointer"
              title="Recalc EK/KG: EKSumme / TotelNetKG"
              onClick={() => reCalcCol('ek_kg_price')}
            />
          </Col>
        </Row>
      ),
      dataIndex: ['ek_kg_price'],
      fieldProps: { placeholder: 'EK/kg', precision: 4 },
      width: 55,
      ...defaultDigitColumProps,
    },
    {
      title: 'EK_Summe',
      dataIndex: ['EK_Summe'],
      width: 70,
      fieldProps: { placeholder: 'EK_Summe', precision: 2 },
      ...defaultDigitColumProps,
    },
    {
      title: 'Sum_EKKg ',
      dataIndex: ['ek_kg_sum'],
      fieldProps: { ...defaultDisabledFormItemProp, precision: 2 },
      width: 70,
      ...defaultDigitColumProps,
    },
    {
      title: 'Ertrag',
      dataIndex: ['Ertrag'],
      width: 70,
      fieldProps: { ...defaultDisabledFormItemProp, precision: 2 },
      ...defaultDigitColumProps,
    },
    {
      title: 'Kategorie',
      dataIndex: ['Kategorie'],
      width: 140,
      editable: false,
      ellipsis: true,
      render(dom, record) {
        const isReadOnlyByLoading = loadings[`Kategorie^${record?.uid}`] ?? false;
        return isReadOnlyByLoading ? <LoadingOutlined /> : dom;
      },
    },
    {
      title: 'Supplier',
      dataIndex: ['supplier_name'],
      width: 120,
      fieldProps: { placeholder: 'Supplier', size: 'small' },
      renderFormItem: (item, { record }) => {
        return (
          <ProFormSelect
            className="size-xs"
            options={suppliers.map((x) => ({ ...x, value: x.name }))}
            showSearch
            fieldProps={{
              ...(item.fieldProps as any),
              onChange(value: string, option: any) {
                cellChangeStatus(item.dataIndex, record?.uid, true);
                handleCellChange(item.dataIndex, record?.uid, option);
              },
              dropdownMatchSelectWidth: false,
              dropdownRender: (menu) => {
                return <div className="ant-select-item-wrap-xs">{menu}</div>;
              },
            }}
            formItemProps={{
              ...item.formItemProps,
              style: { marginBottom: 0, width: '100%' },
              className: 'size-xs size-xs2',
            }}
          />
        );
      },
    },
    {
      title: 'OrgA',
      dataIndex: ['supplier_org_a'],
      fieldProps: { placeholder: 'OrgA', size: 'small' },
      width: 25,
      editable: false,
    },
    {
      title: 'HS code',
      dataIndex: ['hs_code'],
      width: 70,
      fieldProps: {
        placeholder: 'HS code',
        size: 'small',
        onChange(e: any) {
          validatePreviewTableData(dataSource);
        },
      },
    },
    {
      title: '',
      valueType: 'option',
      width: 30,
      align: 'center',
      className: 'p0',
      fixed: 'right',
      render: (__) => null,
    },
  ];

  /**
   * Load finnaical detail list
   */
  const loadFinDetailList = useCallback(
    (params?: any) => {
      setLoading(true);
      getFinDetailListWithImportable({
        ...params,
        order_no: order_out?.order_no,
        lo_id: lo_id,
      })
        .then((res) => {
          setDataSource(res.data);
          validatePreviewTableData(res.data);
          setEditableRowKeys(res.data.map((x) => `${x?.uid}`));
        })
        .catch((err) => Util.error(err))
        .finally(() => {
          setLoading(false);
        });
    },
    [order_out?.order_no, lo_id, validatePreviewTableData],
  );

  const handleDraftListImported = (data: EditableRowType[]) => {
    if (data?.length) {
      for (const rowData of data) {
        rowData.EK_Summe = sn(rowData.Gepackt) * sn(rowData.EK);
        rowData.Ertrag = rowData.EK
          ? sn(rowData.Gepackt) * (sn(rowData.Preis) - sn(rowData.EK))
          : 0;
        rowData.net_weight = sn(rowData.Gepackt) * sn(rowData.net_unit_weight);
      }

      setDataSource((prev) => [...getMergedDataSource(prev), ...data]);
      setEditableRowKeys((prev) => [...prev, ...data.map((x) => x.uid as React.Key)]);
    }
  };

  const handleSaveFinDetailList = (successCallback?: any) => {
    parentReloadRequired.current = true;
    console.log('dataSource', dataSource);

    /* const rowsData = editableFormRef.current?.getRowsData?.() ?? [];
    console.log('new DS: ', rowsData);

    const fieldsValue = editableFormRef.current?.getFieldsValue?.() ?? [];
    console.log('new DS getFieldsValue: ', fieldsValue); */

    const rows = getMergedDataSource(dataSource);
    rows.forEach((x: any, ind) => {
      if ((x.Datum as any)?.isValid?.()) {
        rows[ind].Datum = (x.Datum as any).format(DT_FORMAT_YMD);
      }
    });

    const hide = message.loading('Saving data...', 0);
    updateFinDetailListWithImportable({
      lo_id,
      lo_no,
      order_no: order_out?.order_no,
      rows,
    })
      .then((res) => {
        message.success('Saved successfully.');
        setDataSource(res.data);
        setEditableRowKeys(res.data.map((x) => `${x?.uid}`));
        console.log('callback call.');
        successCallback?.();
      })
      .catch((err) => Util.error(err))
      .finally(() => {
        hide();
      });
  };

  const addFromOrderOutLineItem = useCallback((data: EditableRowType) => {
    console.log(data);
    // actionRef.current?.addEditRecord({ ...data, uid: Util.genNewKey() });
    const uid = Util.genNewKey();
    setDataSource((prev) => [{ ...data, uid: uid }, ...prev]);
    setEditableRowKeys((prev) => [...prev, uid]);
  }, []);

  useEffect(() => {
    if (props.modalVisible) {
      loadFinDetailList();
      parentReloadRequired.current = false;
    }
  }, [props.modalVisible, loadFinDetailList]);

  useEffect(() => {
    getSupplierACList().then((res) => setSuppliers(res));
  }, []);

  // Getting sum total
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const formData = getMergedDataSource(dataSource);
  const totalRow = useMemo(() => calcTotal(formData), [calcTotal, formData]);

  const itemsSumme = useMemo(() => {
    return formData
      .filter((x) => sn(x.Artikelnummer) >= 10)
      .reduce((prev, current) => prev + sn(current.Summe), 0);
  }, [formData]);

  const itemsVkKg = useMemo(() => {
    return formData
      .filter((x) => sn(x.Artikelnummer) >= 10)
      .reduce((prev, current) => prev + sn(current.vk_kg_sum), 0);
  }, [formData]);

  // Order Out selection modal
  const [openOrderOutSelectionModal, setOpenOrderOutSelectionModal] = useState<boolean>(false);
  const [orderOutNos, setOrderOutNos] = useState<number[]>(invoiceCheck.order_out_nos || []);
  const [orderOutNosMap, setOrderOutNosMap] = useState<Record<string, API.OrderOut>>({});

  useEffect(() => {
    setOrderOutNos(invoiceCheck.order_out_nos || []);
    parentReloadRequired.current = true;
  }, [invoiceCheck.order_out_nos]);

  useEffect(() => {
    if (orderOutNos?.length) {
      getOrderOutList({ order_nos: orderOutNos || [], with: '', pageSize: 100 }, {}, {})
        .then((res) => {
          setOrderOutNosMap(
            (res.data || []).reduce((prev: any, current: API.OrderOut) => {
              console.log(prev, current);
              prev[`${current.order_no}`] = current;
              return prev;
            }, {}),
          );
        })
        .catch((err) => Util.error(err));
    }
  }, [orderOutNos]);

  return (
    <Modal
      title={
        <div>
          <Row gutter={16}>
            <Col>Export to Stat</Col>
            <Col>
              <ModalNavigation loId={invoiceCheck.lo_id} handleNavigation={handleNavigation} />
            </Col>
            <Col style={{ fontSize: 12, fontWeight: 'normal' }}>
              <span
                className="cursor-pointer"
                onClick={() => {
                  const hide = message.loading('Updating checked status', 0);
                  const updatedData = {
                    is_stat_checked: invoiceCheck.is_stat_checked == 1 ? 0 : 1,
                  };
                  updateLoInvoiceCheck(`${invoiceCheck.lo_id}`, updatedData)
                    .then((res) => {
                      message.success('Updated successfully.');
                      props.updateParentDs?.({
                        lo_id: invoiceCheck.lo_id,
                        is_stat_checked: res.is_stat_checked,
                      });
                    })
                    .catch((err) => Util.error(err))
                    .finally(() => hide());
                }}
              >
                {invoiceCheck.is_stat_checked != 1 ? (
                  <CheckSquareOutlined style={{ color: 'lightgrey' }} title="Make checked." />
                ) : (
                  <CheckSquareOutlined style={{ color: '#07c807' }} title="Make unchecked." />
                )}
              </span>
            </Col>
            <Col style={{ fontSize: 12, fontWeight: 'normal' }}>
              <a
                href={urlFull('/order-out-detail/' + order_out?.order_no)}
                target="_blank"
                rel="noreferrer"
                style={{ marginLeft: 8 }}
              >
                WA {order_out?.order_no}
              </a>
              {order_out?.order_out_head && (
                <a
                  href={urlFull('/order-out-detail/' + order_out?.order_out_head?.name)}
                  target="_blank"
                  rel="noreferrer"
                  style={{ marginLeft: 8 }}
                  title="OrderOut head name"
                >
                  {order_out?.order_out_head?.name}
                </a>
              )}
            </Col>
            <Col style={{ fontSize: 12, fontWeight: 'normal' }}>
              <Button
                type="link"
                size="small"
                icon={<PlusOutlined />}
                title="Add more order outs"
                onClick={() => {
                  setOpenOrderOutSelectionModal(true);
                }}
              />
              {orderOutNos.map((x) => (
                <Space key={x} size={4}>
                  <a
                    href={urlFull('/order-out-detail/' + x)}
                    target="_blank"
                    rel="noreferrer"
                    style={{ marginLeft: 8 }}
                  >
                    WA {x}{' '}
                  </a>
                  <span>{orderOutNosMap[x]?.customer}</span>
                  {orderOutNosMap[x]?.lotus_notes_id && (
                    <a
                      href={(getCodeValue(DictCode.LOTUS_PATH) ?? '').replace(
                        '{lotusNotesId}',
                        orderOutNosMap[x]?.lotus_notes_id,
                      )}
                      title="Open Lotus link"
                    >
                      Lotus
                    </a>
                  )}
                  <DeleteOutlined
                    className="cursor-pointer"
                    onClick={() => {
                      if (!x) return;
                      const hide = message.loading('Removing order out...', 0);
                      deleteOrderOutToLoInvoiceCheck(invoiceCheck.lo_id, {
                        orderOutNos: [x],
                      })
                        .then((res) => setOrderOutNos(res.order_out_nos || []))
                        .catch((err) => Util.error(err))
                        .finally(() => hide());
                    }}
                  />
                </Space>
              ))}
            </Col>
            <Divider orientation="center" type="vertical" style={{ alignSelf: 'center' }} />
            <Col style={{ fontSize: 12, fontWeight: 'normal' }}>
              <a
                href={`https://app.lexoffice.de/permalink/invoices/view/${lo_id}`}
                target="_blank"
                rel="noreferrer"
                style={{
                  textDecoration: invoiceCheck.lo_status == 'voided' ? 'line-through' : 'auto',
                }}
              >
                {lo_no}
              </a>
            </Col>
            <Col style={{ fontSize: 12, fontWeight: 'normal' }}>
              {order_out?.lotus_notes_id && (
                <a
                  href={(getCodeValue(DictCode.LOTUS_PATH) ?? '').replace(
                    '{lotusNotesId}',
                    order_out?.lotus_notes_id,
                  )}
                  title="Open Lotus link"
                  style={{ marginRight: 32 }}
                >
                  Lotus
                </a>
              )}
            </Col>
            <Col style={{ fontSize: 12, fontWeight: 'normal' }}>
              <Space size={4}>
                <div>{order_out?.customer}</div>
                <Divider orientation="center" type="vertical" />
                <div>{order_out?.supplier_obj?.name}</div>
                <Divider orientation="center" type="vertical" />
                <div>{order_out?.desc}</div>
              </Space>
            </Col>
          </Row>
        </div>
      }
      width={'95%'}
      bodyStyle={{ minHeight: '70vh' }}
      open={props.modalVisible}
      onCancel={() => {
        if (parentReloadRequired.current) {
          props.reloadParentTable?.();
        }
        props.handleModalVisible(false);
      }}
      maskClosable={false}
      footer={false}
    >
      <Row gutter={48}>
        <Col>
          {order_out?.order_no && props.modalVisible && (
            <OrderOutInvoiceLineItems
              orderNo={order_out?.order_no}
              addToPreview={addFromOrderOutLineItem}
              loStatus={invoiceCheck.lo_status}
            />
          )}
        </Col>
        <Col flex="400px" className="text-right">
          <Row wrap={false} style={{ borderBottom: '1px solid #eee' }}>
            <Col span="6" />
            <Col span="6">VK</Col>
            <Col span="6">EK</Col>
            <Col span="6">Ertrag</Col>
          </Row>
          <Row wrap={false}>
            <Col span="6">Invoice:</Col>
            <Col
              span="6"
              className={`${
                isSnDiff(invoiceCheck.total_gross_amount, totalRow.Summe) ? 'c-red' : ''
              }`}
              style={{
                textDecoration: invoiceCheck.lo_status == 'voided' ? 'line-through' : 'auto',
              }}
            >
              {nf2(invoiceCheck.total_gross_amount)}
            </Col>
            <Col span="6" />
            <Col span="6" />
          </Row>
          <Row wrap={false}>
            <Col span="6">Summe:</Col>
            <Col
              span="6"
              className={`${
                isSnDiff(invoiceCheck.total_gross_amount, totalRow.Summe) ? 'c-red' : ''
              }`}
            >
              {nf2(totalRow.Summe)}
            </Col>
            <Col span="6" />
            <Col span="6" />
          </Row>

          <Row wrap={false} style={{ marginTop: 20 }}>
            <Col span="6">Items:</Col>
            <Col
              span="6"
              title="Summe for Item No >= 10"
              className={`${
                isSnDiff(itemsSumme, itemsVkKg) || isSnDiff(itemsSumme, totalRow.vk_kg_sum)
                  ? 'c-red'
                  : ''
              }`}
            >
              {nf2(itemsSumme)}
            </Col>
            <Col span="6" title="EK_Summe">
              {nf2(totalRow.EK_Summe)}
            </Col>
            <Col span="6" title="Summe for Item No >= 10 - EK_Summe">
              {nf2(sn(itemsSumme) - sn(totalRow.EK_Summe))}
            </Col>
          </Row>

          <Row wrap={false}>
            <Col span="6">Items_Kg:</Col>
            <Col
              span="6"
              title="Sum_VKKg for Item No >= 10"
              className={`${
                isSnDiff(itemsSumme, itemsVkKg) || isSnDiff(itemsSumme, totalRow.vk_kg_sum)
                  ? 'c-red'
                  : ''
              }`}
            >
              {nf2(itemsVkKg)}
            </Col>
            <Col
              span="6"
              title="Sum_EKKg"
              className={`${isSnDiff(totalRow.EK_Summe, totalRow.ek_kg_sum) ? 'c-red' : ''}`}
            >
              {nf2(totalRow.ek_kg_sum)}
            </Col>
            <Col span="6" title="Sum_VKKg - Sum_EKKg">
              {nf2(sn(totalRow.vk_kg_sum) - sn(totalRow.ek_kg_sum))}
            </Col>
          </Row>

          <Row wrap={false}>
            <Col span="6">Sum_VKKg:</Col>
            <Col
              span="6"
              title="Sum_VKKg"
              className={`${
                isSnDiff(itemsSumme, itemsVkKg) || isSnDiff(itemsSumme, totalRow.vk_kg_sum)
                  ? 'c-red'
                  : ''
              }`}
            >
              {nf2(totalRow.vk_kg_sum)}
            </Col>
            <Col span="6" />
            <Col span="6" title="Ertrag">
              <span
                className={
                  sn(totalRow.Ertrag) > 0 ? 'c-green' : sn(totalRow.Ertrag) < 0 ? 'c-red' : ''
                }
              >
                {nf2(totalRow.Ertrag)}
              </span>
            </Col>
          </Row>

          {totalRow.net_weight && (
            <Row wrap={false} className="c-lightgrey" style={{ marginTop: 8 }}>
              <Col span="6" />
              <Col span="6" title="Items_Kg / Total Net Kg">
                {nf2(sn(itemsVkKg) / sn(totalRow.net_weight))}
              </Col>
              <Col span="6" title="Sum_EKKg / Total Net Kg">
                {nf2(sn(totalRow.ek_kg_sum) / sn(totalRow.net_weight))}
              </Col>
            </Row>
          )}
        </Col>
        <Col flex={'300px'}>
          <Card size="small" bordered={false}>
            {Object.keys(warnings).length
              ? Object.keys(warnings).map((x) => (
                  <div key={x} className="c-red">
                    {(warnings as any)[`${x}`]} rows {getWarningText(x)}
                  </div>
                ))
              : null}
          </Card>
        </Col>
      </Row>
      <EditableProTable<EditableRowType>
        editableFormRef={editableFormRef}
        actionRef={actionRef}
        headerTitle={
          <>
            <div>Financial details (Preview)</div>
            <Space style={{ marginLeft: 80, fontSize: 12, fontWeight: 'normal' }} size={16}>
              {order_out?.lotus_notes_id && (
                <a
                  href={(getCodeValue(DictCode.LOTUS_PATH) ?? '').replace(
                    '{lotusNotesId}',
                    order_out?.lotus_notes_id,
                  )}
                  title="Open Lotus link"
                  style={{ marginRight: 32 }}
                >
                  Lotus
                </a>
              )}
              <a
                href={getLexOfficeLink(invoiceCheck.lo_type, invoiceCheck.lo_id)}
                target="_blank"
                rel="noreferrer"
                style={{
                  textDecoration: invoiceCheck.lo_status == 'voided' ? 'line-through' : 'auto',
                }}
              >
                {invoiceCheck.lo_no}
              </a>
              <div>
                {nf2(invoiceCheck.total_gross_amount)}
                {EURO2}
              </div>
              <div>{Util.dtToDMY(invoiceCheck.lo_voucher_date)}</div>
              <EditOutlined
                onClick={() => {
                  setColStates((prev) => ({
                    ...prev,
                    ...{
                      Field1: { show: !prev.Field1.show },
                      Re_Nr: { show: !prev.Re_Nr.show },
                      Kunde: { show: !prev.Kunde.show },
                      Datum: { show: !prev.Datum.show },
                    },
                  }));
                }}
              />
              <div>{order_out?.customer_obj?.name}</div>
            </Space>
          </>
        }
        rowKey="uid"
        sticky
        bordered={false}
        debounceTime={200}
        className="editable-table-xs"
        size="small"
        cardProps={{
          style: { marginBottom: '2rem' },
          bodyStyle: { padding: 0 },
        }}
        scroll={{
          x: 700,
        }}
        style={{
          padding: 0,
        }}
        recordCreatorProps={{
          newRecordType: 'dataSource',
          record: () => {
            return {
              uid: Util.genNewKey(),
              supplier_name: order_out?.supplier_obj?.name,
              supplier_org_a: order_out?.supplier_obj?.org_a,
            };
          },
          position: 'bottom',
          creatorButtonText: 'Add new row',
        }}
        loading={loading}
        columns={columns}
        value={dataSource}
        onChange={(recordList) => {
          setDataSource(recordList);
        }}
        editable={{
          type: 'multiple',
          editableKeys,
          onChange: setEditableRowKeys,
          deletePopconfirmMessage: 'Are you sure you want to delete?',
          onlyAddOneLineAlertMessage: 'You can only add one.',
          deleteText: <DeleteFilled style={{ marginLeft: 'auto' }} />,
          actionRender: (row, config, defaultDoms) => {
            return [defaultDoms.delete];
          },
        }}
        columnsState={{
          value: colStates,
          onChange(map) {
            setColStates(map);
          },
        }}
        rowClassName={(r, ind) => {
          const row = { ...r, ...editableFormRef.current?.getRowData?.(ind) };
          return validateRowClassname(row);
        }}
        summary={(dataParam) => {
          // dataParam is based on dataSource, so we don't use this param.
          const data = editableFormRef.current?.getRowsData?.() || [];
          // console.log('summary render: ', data, 'dataparam: ', dataParam);
          const totalRowLocal = calcTotal(data);

          return (
            <ProTable.Summary fixed="top">
              <ProTable.Summary.Row>
                {columns
                  .filter((c) => {
                    if (!c.dataIndex) return false;
                    if (c.hideInTable) return false;
                    const tmpKey = ((c.dataIndex || []) as any[]).join('.');
                    if (colStates[tmpKey]?.show === false) return false;
                    return true;
                  })
                  .map((c, index) => {
                    const tmpKey = ((c.dataIndex || []) as any[]).join('.');
                    let value = null;
                    if (index == 0) value = 'Itemno';
                    else if (
                      tmpKey == 'Gepackt' ||
                      tmpKey == 'Order' ||
                      tmpKey == 'Summe' ||
                      tmpKey == 'EK_Summe' ||
                      tmpKey == 'Ertrag' ||
                      tmpKey == 'ek_kg_sum' ||
                      tmpKey == 'vk_kg_sum' ||
                      tmpKey == 'net_weight'
                    )
                      value = nf2(totalRowLocal[tmpKey]);
                    let cls = '';
                    if (tmpKey == 'Summe') {
                      if (
                        Math.abs(
                          toRound(sn(invoiceCheck.total_gross_amount), 2) -
                            toRound(sn(totalRowLocal[tmpKey]), 2),
                        ) > 0.05
                      ) {
                        cls += ' c-red';
                      }
                    }
                    return (
                      <ProTable.Summary.Cell key={tmpKey} index={index}>
                        <span className={cls}>{value}</span>
                      </ProTable.Summary.Cell>
                    );
                  })}
              </ProTable.Summary.Row>
            </ProTable.Summary>
          );
        }}
        toolBarRender={() => [
          <ProFormCheckbox
            key="vk_kg_price"
            name="showOrHideCols"
            label={<span className="text-sm">Show Cols?</span>}
            tooltip="Show or hide `VK/Kg, Sum_VKKg, EK/Kg, Sum_EKKg` cols"
            className="text-sm"
            fieldProps={{
              checked: colStates.vk_kg_price.show,
              onChange(e) {
                const checked = e.target.checked;
                setColStates((prev) => ({
                  ...prev,
                  vk_kg_price: { show: checked },
                  vk_kg_sum: { show: checked },
                  ek_kg_price: { show: checked },
                  ek_kg_sum: { show: checked },
                }));
              },
            }}
            formItemProps={{ style: { marginBottom: 0, marginRight: 16 } }}
          />,
          <Button
            key="import"
            type="primary"
            size="small"
            onClick={() => {
              loadFinDetailList({ with: 'importable' });
            }}
          >
            <ImportOutlined /> Import
          </Button>,
          <ProFormUploadButton
            key="import-xls"
            max={1}
            name="files"
            title="Import by Xls"
            accept=".xls,.xlsx,.csv"
            action={`${API_URL}/api/fin/detail/importDraftFromXls`}
            buttonProps={{ size: 'small' }}
            fieldProps={{
              name: 'file',
              method: 'post',
              data: {
                order_no: order_out?.order_no,
                lo_id,
              },
              headers: {
                Authorization: `Bearer ${localStorage.getItem(LS_TOKEN_NAME)}`,
              },
              itemRender: () => null,
              beforeUpload: async () => {
                uploadingRef.current = message.loading('Uploading...', 0);
                return true;
              },
              onChange(info) {
                if (info.file.status == 'done') {
                  const res = info.file.response.message;
                  if (uploadingRef.current) {
                    uploadingRef.current?.();
                    uploadingRef.current = null;
                  }
                  message.success('Successfully imported. Check the list below.');
                  handleDraftListImported(res);
                } else if (info.file.status == 'error') {
                  message.error(info.file.response?.message);
                  uploadingRef.current?.();
                  uploadingRef.current = null;
                }
              },
            }}
            formItemProps={{ style: { marginBottom: 0 } }}
          />,
          <Button
            key="save"
            type="primary"
            size="small"
            onClick={() => {
              handleSaveFinDetailList();
            }}
          >
            <SaveOutlined /> Save
          </Button>,
          <Button
            key="saveAndClose"
            type="primary"
            size="small"
            onClick={() => {
              handleSaveFinDetailList(() => {
                if (parentReloadRequired.current) {
                  props.reloadParentTable?.();
                }
                console.log('callback 2');
                props.handleModalVisible(false);
              });
            }}
          >
            <SaveOutlined /> Save & Close
          </Button>,
          <Popconfirm
            key="deleteAll"
            title={<>Are you sure you want to delete all data?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 300 }}
            onConfirm={() => {
              const hide = message.loading('Deleting data...', 0);
              deleteFinDetailList({
                lo_id,
                lo_no,
                order_no: order_out?.order_no,
              })
                .then((res) => {
                  message.success('Deleted successfully.');
                  actionRef.current?.reload();
                  setDataSource([]);
                  setEditableRowKeys([]);
                  parentReloadRequired.current = true;
                })
                .catch((err) => Util.error(err))
                .finally(() => {
                  hide();
                });
            }}
          >
            <Button type={'default'} size="small" danger icon={<DeleteOutlined />}>
              Delete All
            </Button>
          </Popconfirm>,
        ]}
        columnEmptyText=""
        locale={{ emptyText: <></> }}
      />
      <OrderOutSelectionModal
        invoiceCheck={{ lo_id: invoiceCheck.lo_id }}
        modalVisible={openOrderOutSelectionModal}
        handleModalVisible={setOpenOrderOutSelectionModal}
        orderOutNos={orderOutNos}
        setOrderOutNos={setOrderOutNos}
      />
    </Modal>
  );
};

export default ExportToStatModal;
