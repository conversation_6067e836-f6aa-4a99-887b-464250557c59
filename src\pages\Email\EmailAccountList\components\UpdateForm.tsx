import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect, ProFormSwitch } from '@ant-design/pro-form';
import { ProFormText, ModalForm } from '@ant-design/pro-form';
import { updateEmailAccount } from '@/services/app/Email/email-account';
import Util from '@/util';
import { getEmailServerList } from '@/services/app/Email/email-server';
import SDatePicker from '@/components/SDatePicker';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...');

  try {
    await updateEmailAccount(fields);
    hide();
    message.success('Update is successful');
    return true;
  } catch (error) {
    hide();
    Util.error('Update failed, please try again!', error);
    return false;
  }
};

export type FormValueType = Partial<API.EmailAccount>;

export type UpdateFormProps = {
  initialValues?: Partial<API.EmailAccount>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.EmailAccount) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef && formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, props.initialValues]);

  return (
    <ModalForm
      title={'Update Email account'}
      width="500px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      initialValues={props.initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate({ ...value, id: props.initialValues?.id });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
          formRef.current?.resetFields();
        }
      }}
    >
      <ProFormText
        rules={[
          {
            required: true,
            message: 'Email is required',
          },
        ]}
        width="md"
        name="email"
        label="Email"
      />
      <ProFormText.Password width="md" name={['password']} label="IMAP Password" />
      <ProFormSelect
        name="server_id"
        label="Server"
        request={(params) => {
          return getEmailServerList({ ...params }).then((res) =>
            res.data.map((x) => ({ value: x.id, label: x.domain })),
          );
        }}
      />
      <SDatePicker
        name={['settings', 'imapSince']}
        label="Since"
        width="md"
        help="Pop Emails since this date."
      />
      <ProFormSwitch name={['status']} label="Status" width="md" />
    </ModalForm>
  );
};

export default UpdateForm;
