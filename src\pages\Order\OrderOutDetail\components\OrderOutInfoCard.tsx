import { DictCode, DictType } from '@/constants';
import { getNextOrderOutNo, updateOrderOut } from '@/services/app/Order/order-out';
import Util, { urlFull } from '@/util';
import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  CloseOutlined,
  EditOutlined,
} from '@ant-design/icons';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormRadio } from '@ant-design/pro-form';
import { Button, Col, message, Row, Typography } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { Suspense, useEffect, useRef, useState } from 'react';
import { useModel } from 'umi';
import { isEqualAddresses, saveLastOrderTypeBySupplier } from '..';
import OrderOutAddress from './address/OrderOutAddress';
import CustomerSelectForm from './CustomerSelectForm';
import SupplierSelectForm from './SupplierSelectForm';
import { history } from 'umi';

type OrderOutInfoCardProps = {
  orderOut?: API.OrderOut;
  setOrderOut?: SetStateAction<Dispatch<API.OrderOut>>;
  loadOrderOut?: (no?: number) => Promise<void>;
};

type FormValueType = {
  order_type?: string;
} & Partial<API.OrderOut>;

const OrderOutInfoCard: React.FC<OrderOutInfoCardProps> = (props) => {
  const { orderOut, loadOrderOut } = props;
  const { getDictOptionsByType, getCodeValue } = useModel('app-settings');

  const formRef = useRef<ProFormInstance<FormValueType>>();
  const [visibleCustomerModal, setVisibleCustomerModal] = useState<boolean>(false);
  const [visibleSupplierModal, setVisibleSupplierModal] = useState<boolean>(false);
  const [type, setType] = useState('');
  // const [category, setCategory] = useState<string>();
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    if (formRef?.current && orderOut) {
      const firstCategory = orderOut?.categories?.map((x) => ({
        value: x.code,
        label: x.label,
      }))?.[0]?.value;

      formRef.current.setFieldsValue({
        ...orderOut,
        categories: firstCategory as any,
      });
      setType(orderOut.type_code ?? '');
      // setCategory(firstCategory);
    }
  }, [orderOut]);

  /**
   * Next or previous order out no
   *
   * @param dir -1 or 1
   */
  const hanldeAccountNoNavigation = (dir: number) => {
    if (!orderOut?.order_no) return;

    const currentAccountNo = orderOut.order_no;
    setLoading(true);
    getNextOrderOutNo(dir, currentAccountNo)
      .then((res) => {
        if (res) {
          history.push(urlFull(`/order-out-detail/${res.order_no}?task_id=${res.task?.id}`, true));
          loadOrderOut?.(res.order_no).finally(() => setLoading(false));
        }
      })
      .catch((err) => Util.error(err));
  };

  return (
    <Suspense fallback={false}>
      <Row wrap={false} gutter={16}>
        <Col flex={'auto'}>
          <Typography.Paragraph ellipsis className="bold" title={orderOut?.desc}>
            WA {orderOut?.order_no} - {orderOut?.customer} - {orderOut?.desc}
          </Typography.Paragraph>
        </Col>
        <Col flex={'70px'}>
          <Button
            onClick={() => hanldeAccountNoNavigation(-1)}
            icon={<ArrowLeftOutlined />}
            title="Previous Konto"
            size="small"
            disabled={loading}
          />
          <Button
            onClick={() => hanldeAccountNoNavigation(1)}
            icon={<ArrowRightOutlined />}
            disabled={loading}
            size="small"
            title="Next Konto"
          />
        </Col>

        {orderOut?.lotus_notes_id && (
          <Col>
            <a
              href={(getCodeValue(DictCode.LOTUS_PATH) ?? '').replace(
                '{lotusNotesId}',
                orderOut?.lotus_notes_id,
              )}
              title="Open Lotus link"
            >
              Lotus
            </a>
          </Col>
        )}
        <Col flex={'0 0 40px'}>
          <Button
            type="default"
            size="small"
            icon={<CloseOutlined title="Close tab" />}
            onClick={() => {
              window.close();
            }}
          />
        </Col>
      </Row>
      <Row gutter={12} style={{ marginBottom: 24 }}>
        <Col xl={8} lg={8} md={12} sm={24} xs={24}>
          <div>
            <span>Supplier </span>
            <Button
              type="link"
              title="Select supplier"
              size="small"
              icon={<EditOutlined />}
              onClick={() => setVisibleSupplierModal(true)}
            />
          </div>
          <div>{`${orderOut?.supplier_obj?.name || ''}${
            orderOut?.supplier_obj?.description
              ? ` - ${orderOut?.supplier_obj?.description.substring(0, 20)}`
              : ''
          }`}</div>
        </Col>
        <Col xl={16} lg={16} md={12} sm={24} xs={24}>
          <div>
            <span>Customer </span>
            <Button
              type="link"
              title="Select customer"
              size="small"
              icon={<EditOutlined />}
              onClick={() => setVisibleCustomerModal(true)}
            />
          </div>
          <div>{`${orderOut?.customer_obj?.name || ''}${
            orderOut?.customer_obj?.description
              ? ` - ${orderOut?.customer_obj?.description.substring(0, 20)}`
              : ''
          }`}</div>
        </Col>
      </Row>

      <Row gutter={12} style={{ marginBottom: 24 }}>
        <Col xl={8} lg={8} md={12} sm={24} xs={24}>
          {orderOut?.supplier_obj && (
            <OrderOutAddress
              card_title="Loading address"
              supplier_id={orderOut?.supplier_id ?? 0}
              order_no={orderOut?.order_no ?? 0}
              type="OrderOut"
              address_type="loading"
              addressList={orderOut?.supplier_obj?.addresses}
              address={orderOut?.addresses?.find((x) => x.address_type == 'loading')}
              loadOrderOut={loadOrderOut}
            />
          )}
        </Col>
        {orderOut?.customer_obj && (
          <Col xl={8} lg={8} md={12} sm={24} xs={24}>
            <OrderOutAddress
              card_title="Invoice address"
              customer_id={orderOut?.customer_id ?? 0}
              order_no={orderOut?.order_no ?? 0}
              type="OrderOut"
              address_type="invoice"
              addressList={orderOut?.customer_obj?.addresses}
              address={orderOut?.addresses?.find((x) => x.address_type == 'invoice')}
              loadOrderOut={loadOrderOut}
            />
          </Col>
        )}
        {orderOut?.customer_obj && (
          <Col xl={8} lg={8} md={12} sm={24} xs={24}>
            <OrderOutAddress
              card_title="Shipping address"
              customer_id={orderOut?.customer_id ?? 0}
              order_no={orderOut?.order_no ?? 0}
              type="OrderOut"
              address_type="shipping"
              addressList={orderOut?.customer_obj?.addresses}
              address={orderOut?.addresses?.find((x) => x.address_type == 'shipping')}
              loadOrderOut={loadOrderOut}
              same_with_invoice={isEqualAddresses(
                orderOut?.addresses?.find((x) => x.address_type == 'invoice'),
                orderOut?.addresses?.find((x) => x.address_type == 'shipping'),
              )}
            />
          </Col>
        )}
      </Row>
      <ProForm
        formRef={formRef}
        submitter={false}
        layout="horizontal"
        labelAlign="left"
        labelCol={{ flex: '0 0 90px' }}
        initialValues={{ ...orderOut }}
        onValuesChange={(changedValues, values) => {
          if ('type_code' in changedValues || 'categories' in changedValues) {
            updateOrderOut({ order_no: orderOut?.order_no, ...values })
              .then(() => {
                loadOrderOut?.(orderOut?.order_no);
                if ('type_code' in changedValues && orderOut?.supplier_id) {
                  saveLastOrderTypeBySupplier(orderOut?.supplier_id, changedValues.type_code);
                }
                message.destroy();
                message.success('Updated successfully.', 2);
              })
              .catch((e) => Util.error(e));
            if ('type_code' in changedValues) {
              setType(changedValues.type_code);
            }
          }
        }}
      >
        {(orderOut?.customer_obj || orderOut?.supplier_obj) && (
          <ProFormRadio.Group
            label="OrderType"
            name="type_code"
            options={getDictOptionsByType(DictType.OrderOutType)}
            fieldProps={{ value: type }}
          />
        )}
        {orderOut?.type_code && (
          <ProFormRadio.Group
            label="Category"
            name={['categories']}
            radioType="radio"
            options={getDictOptionsByType(DictType.OrderOutCategory)}
            /* fieldProps={{
              onChange: (e) => {
                setCategory(e.target.value);
              },
            }} */
          />
        )}
        {type == 'ORD_OUT_LAGER' && (
          <Button
            type="primary"
            size="small"
            disabled
            onClick={() => message.info('Sorry, not implemented yet!', 2)}
          >
            Connected OrderIn
          </Button>
        )}
      </ProForm>
      <CustomerSelectForm
        modalVisible={visibleCustomerModal}
        handleModalVisible={setVisibleCustomerModal}
        initialValues={orderOut}
        formRef={formRef}
        loadOrderOut={loadOrderOut}
      />
      <SupplierSelectForm
        modalVisible={visibleSupplierModal}
        handleModalVisible={setVisibleSupplierModal}
        initialValues={orderOut}
        formRef={formRef}
        loadOrderOut={loadOrderOut}
      />
    </Suspense>
  );
};
export default OrderOutInfoCard;
