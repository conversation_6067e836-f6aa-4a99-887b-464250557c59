import { getSysTextModuleACList } from '@/services/app/Sys/text-module';
import { sn } from '@/util';
import { Mentions } from 'antd';
import { useCallback, useRef, useState } from 'react';
import _, { debounce } from 'lodash';
import type { MentionProps } from '@ant-design/compatible/lib/mention';

type SMentionsProps = {
  changeCallback?: (value: string) => void;
} & MentionProps;

const SMentions: React.FC<SMentionsProps> = ({ changeCallback, ...restProps }) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any[]>([]);
  const ref = useRef<string>();

  const loadData = (key: string) => {
    setLoading(true);
    getSysTextModuleACList({ number: sn(key) })
      .then((res) => {
        // We found a Text by Number, so we need to put it in the text field.
        // if (ref.current !== key) return;
        setLoading(false);
        setData(res);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debounceLoadTextModule = useCallback(debounce(loadData, 800), []);

  const onSearch = (search: string) => {
    ref.current = search;
    debounceLoadTextModule(search);
  };

  return (
    <Mentions
      autoSize={{ minRows: 1, maxRows: 1 }}
      split=""
      style={{ width: '100%' }}
      loading={loading}
      filterOption={(input: string, optionData) => {
        // console.log('filter: ', input, optionData);
        return true;
      }}
      onSearch={onSearch}
      {...(restProps as any)}
    >
      {data.map((x) => (
        <Mentions.Option key={x.value} value={x.text}>
          {x.label}
        </Mentions.Option>
      ))}
    </Mentions>
  );
};
export default SMentions;
