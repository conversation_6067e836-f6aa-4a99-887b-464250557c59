/* eslint-disable */
import { AC_PER_PAGE_PAGINATION } from '@/constants';
import { request, RequestConfig } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/sys/workflow';

/** rule GET /api/sys/workflow */
export async function getSysWorkflowList(
  params: API.PageParams & Partial<API.SysWorkflow> & { is_date_valid?: number, ids?: number[] },
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.SysWorkflow>> {
  return request<API.Result<API.SysWorkflow>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** POST /api/sys/workflow */
export async function updateSysWorkflow(
  id?: number,
  data?: Partial<API.SysWorkflow>,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };

  return request<API.SysWorkflow>(`${urlPrefix}/` + id, config);
}

/** post POST /api/sys/workflow */
export async function addSysWorkflow(
  data: API.SysWorkflow | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'POST',
    ...(options || {}),
  };
  if (data instanceof FormData) {
    config.body = data;
  } else {
    config.data = data;
  }
  return request<API.SysWorkflow>(`${urlPrefix}`, config);
}

/** PUT /api/sys/workflow/mapping-details/{id} */
export async function updateMappingDetails(
  id?: number,
  data?: Partial<API.SysWorkflow> | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };

  return request<API.ResultObject<API.SysWorkflow>>(
    `${urlPrefix}/mapping-details/${id}`,
    config,
  ).then((res) => res.message);
}

/** PUT /api/sys/workflow/export-address/{id} */
export async function exportAddressByTpl(
  id?: number, // tpl ID.
  data?: { addressId?: number; supplierId?: number; orderNo?: number },
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };

  return request<API.ResultObject<API.SysWorkflow>>(
    `${urlPrefix}/export-address/${id}`,
    config,
  ).then((res) => res.message);
}

/** GET /api/sys/workflow/{id} */
export async function getSysWorkflow(
  id?: number,
  params?: Partial<API.SysWorkflow> & { with?: string } | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'GET',
    params,
    ...(options || {}),
  };

  return request<API.ResultObject<API.SysWorkflow>>(`${urlPrefix}/${id}`, config).then(
    (res) => res.message,
  );
}

/** delete DELETE /api/sys/workflow/{id} */
export async function deleteSysWorkflow(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}

export async function getSysWorkflowACList(
  params: API.PageParams & { number?: number; numberExact?: number },
  sort?: any,
  filter?: any,
): Promise<any[]> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize ?? AC_PER_PAGE_PAGINATION,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) =>
    res.message.data.map((x: API.SysWorkflow) => ({
      value: x.id,
      text: x.name,
      label: x.name,
    })),
  );
}

export async function getAddressMappingFields(id?: number): Promise<any> {
  return request<API.BaseResult>(`${urlPrefix}/mapping-fields/${id}`, {
    method: 'GET',
    paramsSerializer,
    withToken: true,
  }).then((res) => res.message);
}
