import { parseCSAItem } from "@/services/app/LexOffice/lo-finance";
import { useCallback, useMemo } from "react";

const useCustomerOrSupplier = (customerOrSupplier?: (API.Customer | API.Supplier) & { uid?: string }) => {
    const parsedInfo = useMemo(() => {
        const pObj = parseCSAItem(customerOrSupplier?.uid ?? '');
        return pObj;
    }, [customerOrSupplier?.uid]);

    const isSupplier = !!parsedInfo.supplierId;
    const selectedId = parsedInfo.id;
    const accountNo = parsedInfo.account_no;

    /** Get supplier or customer ID object */
    const getSCParam = useCallback(() => {
        return {
            customerId: isSupplier ? undefined : selectedId,
            supplierId: isSupplier ? selectedId : undefined,
            accountNo: accountNo,
        }
    }, [isSupplier, selectedId, accountNo]);

    return {
        isSupplier,
        selectedId,
        accountNo,
        getSCParam
    }
}

export default useCustomerOrSupplier;