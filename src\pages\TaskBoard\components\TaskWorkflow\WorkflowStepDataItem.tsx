import SDatePicker from '@/components/SDatePicker';
import SProFormDateRange from '@/components/SProFormDateRange';
import { updateTaskWorkflowStep } from '@/services/app/Task/task-workflow-step';
import Util, { sn } from '@/util';
import {
  ProFormCheckbox,
  ProFormRadio,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-form';
import type { ProFormFieldItemProps } from '@ant-design/pro-form/lib/interface';
import { Divider, message } from 'antd';
import { useRef } from 'react';

type WorkflowStepDataItemProps = {
  task_id: number;
  step?: API.SysWorkflowStep;
  updateCallback?: (res: any) => void;
};
const WorkflowStepDataItem: React.FC<WorkflowStepDataItemProps> = (props) => {
  const { step, task_id, updateCallback } = props;
  const isChangedRef = useRef(false);

  const handleUpdate = async (value: any, isSencodValue?: boolean) => {
    const hide = message.loading('Updating...', 0);
    const data: any = {
      workflow_id: sn(step?.workflow_id),
      step_id: step?.id,
      task_id,
      single_update: true,
    };
    if (isSencodValue) {
      data.value2 = step?.field_type === 'switch' ? (value ? '1' : '0') : value;
    } else {
      data.value = step?.field_type === 'switch' ? (value ? '1' : '0') : value;
    }

    return updateTaskWorkflowStep(data)
      .then((res) => {
        message.success('Saved successfully.');
        if (isSencodValue) {
          updateCallback?.({ step_id: step?.id, value2: value, step: res });
        } else {
          updateCallback?.({ step_id: step?.id, value: value, step: res });
        }
        return res;
      })
      .catch((e) => Util.error(e))
      .finally(() => hide());
  };

  let ele = null;
  const eleProps: ProFormFieldItemProps = {
    label: step?.desc + (process.env.NODE_ENV === 'development' ? ` (${step?.id})` : ''),
    name: `${step?.id}`,
    colon: false,

    fieldProps: {
      size: 'small',
      // event or direct value
      onChange: (e: any) => {
        if (step?.field_type == 'text' || step?.field_type == 'textarea') {
          isChangedRef.current = true;
        } else {
          let changedValue = null;

          if (step?.field_type == 'date') {
            if (e) {
              changedValue = Util.dtToYMD(e);
            }
          } else if (step?.field_type == 'daterange') {
            if (e) {
              changedValue = Util.dtToYMD(e);
            }
          } else if (step?.field_type == 'switch') {
            changedValue = e;
          } else if (step?.field_type == 'multiselect' || step?.field_type == 'checkbox') {
            changedValue = e;
          } else if (step?.field_type == 'select') {
            changedValue = e;
          } else if (step?.field_type == 'radio') {
            changedValue = e.target.value;
          } else {
            changedValue = e.target.value;
          }

          // console.log(' -->', step?.field_type, changedValue);
          handleUpdate(changedValue).then((res) => {
            if (res) {
              isChangedRef.current = false;
            }
          });
        }
      },
      onBlur: (e: any) => {
        if (
          (step?.field_type == 'text' || step?.field_type == 'textarea') &&
          isChangedRef.current
        ) {
          handleUpdate(e.target.value).then((res) => {
            if (res) {
              isChangedRef.current = false;
            }
          });
        }
      },
    },
    formItemProps: { style: { marginBottom: 4 /* , borderBottom: '1px solid #f4f4f4' */ } },
  };

  // For second value of workflow step value: Used in daterange.
  const eleProps2: ProFormFieldItemProps = {
    fieldProps: {
      size: 'small',
      name: `${step?.id}_2`,
      // event or direct value
      onChange: (e: any) => {
        if (step?.field_type == 'daterange') {
          let changedValue = null;
          if (step?.field_type == 'daterange') {
            if (e) {
              changedValue = Util.dtToYMD(e);
            }
          }
          // console.log(' -->', step?.field_type, changedValue);
          handleUpdate(changedValue, true).then((res) => {
            if (res) {
              isChangedRef.current = false;
            }
          });
        }
      },
    },
    formItemProps: { style: { marginBottom: 4 /* , borderBottom: '1px solid #f4f4f4' */ } },
  };

  if (step?.field_type == 'text') {
    ele = <ProFormText {...eleProps} />;
  } else if (step?.field_type == 'textarea') {
    ele = <ProFormTextArea {...eleProps} />;
  } else if (step?.field_type == 'multiselect') {
    ele = <ProFormSelect {...eleProps} mode="multiple" options={step.options || []} />;
  } else if (step?.field_type == 'select') {
    ele = <ProFormSelect {...eleProps} mode="single" options={step.options || []} />;
  } else if (step?.field_type == 'switch') {
    ele = <ProFormSwitch {...eleProps} fieldProps={{ ...eleProps.fieldProps }} />;
  } else if (step?.field_type == 'radio') {
    ele = <ProFormRadio.Group {...eleProps} options={step.options || []} />;
  } else if (step?.field_type == 'checkbox') {
    ele = <ProFormCheckbox.Group {...eleProps} options={step.options || []} />;
  } else if (step?.field_type == 'date') {
    ele = <SDatePicker {...eleProps} />;
  } else if (step?.field_type == 'daterange') {
    ele = (
      <SProFormDateRange
        startDateName={`${step?.id}`}
        endDateName={`${step?.id}_2`}
        fieldItemProps1={eleProps}
        fieldItemProps2={eleProps2}
        label={eleProps.label}
      />
    );
  } else if (step?.field_type == 'divider') {
    ele = <Divider orientation="left">{step.desc}</Divider>;
  }

  return ele;
};

export default WorkflowStepDataItem;
