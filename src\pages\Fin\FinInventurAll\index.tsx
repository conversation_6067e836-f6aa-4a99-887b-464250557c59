import { <PERSON><PERSON>, <PERSON>, Col, Row, Space } from 'antd';
import React, { useState, useRef, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { nf2 } from '@/util';

import { FileExcelOutlined } from '@ant-design/icons';

import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import ImportModalForm from './components/ImportModalForm';
import { getFinInventurAllList } from '@/services/app/Fin/fin-inventur-all';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import FinSinfoListModal from '../FinSinfo/FinSinfoListModal';

type RowType = API.FinIventurAll;
type SfFormType = API.FinIventurAll;

const FinInventurAllListPage: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();

  // XLS import
  const [openImportModal, setOpenImportModal] = useState<boolean>(false);

  // Sinfos modal
  const [openSinfoListModal, setOpenSinfoListModal] = useState<boolean>(false);

  const columns: ProColumns<RowType>[] = [
    {
      title: 'Kategorie',
      dataIndex: 'Kategorie',
      sorter: true,
      search: false,
      width: 250,
    },
    {
      title: 'Angebotsgruppe',
      sorter: false,
      dataIndex: 'Angebotsgruppe',
      search: false,
      width: 110,
      /* render: (dom, record) =>
        TransactionTypeOptions.find((x) => x.value == record.category)?.label ?? '', */
    },
    {
      title: 'Herkunft',
      dataIndex: 'Herkunft',
      sorter: false,
      search: false,
      width: 90,
    },
    {
      title: 'FremdArtikelnummer',
      dataIndex: 'FremdArtikelnummer',
      sorter: false,
      search: false,
      width: 150,
    },
    {
      title: 'Artikelnummer',
      dataIndex: 'Artikelnummer',
      sorter: true,
      search: false,
      align: 'right',
      width: 80,
    },
    {
      title: 'Bezeichnung',
      dataIndex: 'Bezeichnung',
      sorter: false,
      search: false,
      width: 150,
    },
    {
      title: 'Anzahl',
      dataIndex: 'Anzahl',
      sorter: true,
      search: false,
      align: 'right',
      width: 80,
      render: (dom, record) => nf2(record.Anzahl),
    },
    {
      title: 'Einheit',
      dataIndex: 'Einheit',
      sorter: false,
      search: false,
      width: 150,
    },

    {
      title: 'Inv',
      dataIndex: 'Inv',
      sorter: true,
      search: false,
      align: 'right',
      width: 80,
      render: (dom, record) => nf2(record.Inv),
    },
    {
      title: 'SummeInv',
      dataIndex: 'SummeInv',
      sorter: true,
      search: false,
      align: 'right',
      width: 80,
      render: (dom, record) => nf2(record.SummeInv),
    },
    {
      title: 'EK',
      dataIndex: 'EK',
      sorter: true,
      search: false,
      align: 'right',
      width: 80,
      render: (dom, record) => nf2(record.EK),
    },
    {
      title: 'SummeEK',
      dataIndex: 'SummeEK',
      sorter: true,
      search: false,
      align: 'right',
      width: 80,
      render: (dom, record) => nf2(record.SummeEK),
    },
    {
      title: 'VK',
      dataIndex: 'VK',
      sorter: true,
      search: false,
      align: 'right',
      width: 80,
      render: (dom, record) => nf2(record.VK),
    },
    {
      title: 'SonderVK',
      dataIndex: 'SonderVK',
      sorter: true,
      search: false,
      align: 'right',
      width: 80,
      render: (dom, record) => nf2(record.SonderVK),
    },

    {
      title: 'SummeVK',
      dataIndex: 'SummeVK',
      sorter: true,
      search: false,
      align: 'right',
      width: 80,
      render: (dom, record) => nf2(record.SummeVK),
    },
    {
      title: 'LVK',
      dataIndex: 'LVK',
      sorter: false,
      search: false,
      width: 150,
    },
    {
      title: 'SummeLVK',
      dataIndex: 'SummeLVK',
      sorter: true,
      search: false,
      align: 'right',
      width: 80,
      render: (dom, record) => nf2(record.SummeLVK),
    },
    {
      title: 'Feld18',
      dataIndex: 'Feld18',
      sorter: false,
      search: false,
      width: 150,
    },
    {
      title: 'WarenAlter',
      dataIndex: 'WarenAlter',
      sorter: true,
      search: false,
      align: 'right',
      width: 80,
      render: (dom, record) => nf2(record.WarenAlter),
    },
    {
      title: 'Feld20',
      dataIndex: 'Feld20',
      sorter: false,
      search: false,
      width: 150,
    },

    {
      title: 'FreigabeA',
      dataIndex: 'FreigabeA',
      sorter: true,
      search: false,
      align: 'right',
      width: 80,
      render: (dom, record) => nf2(record.FreigabeA),
    },
    {
      title: 'FreigabeB',
      dataIndex: 'FreigabeB',
      sorter: true,
      search: false,
      align: 'right',
      width: 80,
      render: (dom, record) => nf2(record.FreigabeB),
    },
    {
      title: 'VE',
      dataIndex: 'VE',
      sorter: true,
      search: false,
      align: 'right',
      width: 80,
      render: (dom, record) => nf2(record.VE),
    },
    {
      title: 'Pal',
      dataIndex: 'Pal',
      sorter: false,
      search: false,
      width: 150,
    },
  ];

  useEffect(() => {
    const lastSf = Util.getSfValues('sf_fin_inventur_all', {});
    searchFormRef.current?.setFieldsValue(lastSf);
    Util.setSfValues(lastSf);
  }, []);

  return (
    <PageContainer
      title={
        <Row gutter={64}>
          <Col>Inventur All</Col>
          <Col>
            <Button type="primary" size="small" onClick={() => setOpenSinfoListModal(true)}>
              Sinfos...
            </Button>
          </Col>
        </Row>
      }
    >
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SfFormType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            submitButtonProps: {
              htmlType: 'submit',
            },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => actionRef.current?.reload(),
            render: (form, dom) => {
              return [...dom];
            },
          }}
        >
          <ProFormText
            name={'Kategorie'}
            label="Kategorie"
            width={'sm'}
            placeholder={'Kategorie'}
          />
          <ProFormText
            name={'Artikelnummer'}
            label="Artikelnummer"
            width={'xs'}
            placeholder={'Artikelnummer'}
          />
        </ProForm>
      </Card>

      <ProTable<RowType, API.PageParams>
        headerTitle={'Inventur All list'}
        actionRef={actionRef}
        rowKey="Artikelnummer"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        size="small"
        pagination={{
          showSizeChanger: true,
          pageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        toolBarRender={() => [
          <Space key="new">
            <Button
              type="primary"
              size="small"
              icon={<FileExcelOutlined />}
              onClick={() => setOpenImportModal(true)}
              title="Import by XLS or CSV"
            >
              Import
            </Button>
          </Space>,
        ]}
        scroll={{ x: 2400 }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_fin_inventur_all', searchFormValues);

          return getFinInventurAllList(
            { ...params, ...searchFormValues },
            { ...sort },
            filter,
          ).then((res) => {
            return res;
          });
        }}
        onRequestError={(err) => Util.error(err)}
        columns={columns}
        tableAlertRender={false}
        columnEmptyText=""
      />

      <ImportModalForm
        modalVisible={openImportModal}
        handleModalVisible={setOpenImportModal}
        onSubmit={async (value) => {
          actionRef.current?.reload();
          setOpenImportModal(false);
        }}
      />

      <FinSinfoListModal
        modalVisible={openSinfoListModal}
        handleModalVisible={setOpenSinfoListModal}
      />
    </PageContainer>
  );
};

export default FinInventurAllListPage;
