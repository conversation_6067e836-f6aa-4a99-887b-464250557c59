import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { Divider, message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormDependency } from '@ant-design/pro-form';
import { ProFormSwitch } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProFormText, ModalForm } from '@ant-design/pro-form';
import { getTaskGroupACList, updateTaskGroup } from '@/services/app/Task/task-group';
import { getUsersACList } from '@/services/app/user';
import Util from '@/util';
import TaskBlockList from './TaskBlockList';
import { useModel } from 'umi';
import SProFormDigit from '@/components/SProFormDigit';
import {
  TaskGroupLayout,
  TaskGroupLayoutKV,
  UpdateTaskLayout,
  UpdateTaskLayoutKV,
} from '@/constants';
import { getSysWorkflowACList } from '@/services/app/Sys/sys-workflow';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...');

  try {
    await updateTaskGroup(fields);
    hide();
    message.success('Update is successful');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

// eslint-disable-next-line @typescript-eslint/ban-types
export type FormValueType = {} & Partial<API.TaskGroup>;

export type UpdateFormProps = {
  initialValues?: Partial<API.TaskGroup>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.TaskGroup) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { setTaskGroups } = useModel('task-group');

  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (!props.modalVisible) return;
    if (formRef.current) {
      const newValues = {
        ...(props.initialValues || {}),
        users: props.initialValues?.users?.map((x) => x.user_id),
      };
      if (!newValues?.settings?.updateTaskLayout) {
        newValues.settings = { ...newValues.settings, updateTaskLayout: UpdateTaskLayout.LAYOUT_A };
      }
      if (!newValues?.settings?.layout) {
        newValues.settings = { ...newValues.settings, layout: TaskGroupLayout.LAYOUT_DEFAULT };
      }
      if (!newValues?.settings?.wfStepIds) {
        newValues.settings = { ...newValues.settings, wfStepIds: '' };
      }

      formRef.current.setFieldsValue(newValues);
    }
  }, [props.initialValues, props.modalVisible]);

  useEffect(() => {
    if (!props.modalVisible) return;
    getTaskGroupACList({ with: 'taskBlocks' })
      .then((res) => {
        setTaskGroups(res);
      })
      .catch((e) => Util.error('Failed to load task groups. Please try again'));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.modalVisible]);

  return (
    <ModalForm<API.TaskGroup>
      title={'Update Task Group'}
      width="800px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
      initialValues={props.initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const updatedData = {
          ...value,
          settings: { ...props.initialValues?.settings, ...value?.settings },
          id: props.initialValues?.id,
        };
        if (updatedData.settings?.updateTaskLayout == UpdateTaskLayout.LAYOUT_A) {
          delete updatedData.settings?.updateTaskLayoutWSId;
        }

        const success = await handleUpdate(updatedData);

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
    >
      <ProFormText
        rules={[
          {
            required: true,
            message: 'Name is required',
          },
        ]}
        name="name"
        label="Name"
        fieldProps={{
          onChange: (e) => {
            formRef.current?.setFieldValue('code', Util.sInitials(e.target.value));
          },
        }}
      />
      {/* {props.initialValues?.code != 'OrderIn' && props.initialValues?.code != 'OrderOut' && ( */}
      <ProFormText
        rules={[
          {
            required: true,
            type: 'regexp',
            pattern: new RegExp(/[a-zA-Z0-9-]/),
            message: 'Alpah, numeric, dash only.',
          },
          {
            required: true,
            max: 10,
            message: 'Code is required',
          },
        ]}
        help="Alpah, numeric, dash only. Max 10."
        width="md"
        name="code"
        label="Code"
      />
      {/* )} */}
      <SProFormDigit name="sort" label="Sort" placeholder="Sort" width="xs" />
      <ProFormSwitch name={['settings', 'useInitials']} label="Use user initials?" />
      <ProFormSelect
        name={['settings', 'layout']}
        placeholder="Group Layout"
        label="Group Layout"
        valueEnum={TaskGroupLayoutKV}
        width="sm"
        fieldProps={{ dropdownMatchSelectWidth: false }}
      />

      <Divider orientation="left">Task update modal setting</Divider>
      <ProFormSelect
        name={['settings', 'updateTaskLayout']}
        placeholder="Task update modal's Layout"
        label="Task update modal's Layout"
        valueEnum={UpdateTaskLayoutKV}
        width="sm"
        fieldProps={{ dropdownMatchSelectWidth: false }}
      />
      <ProFormDependency name={[['settings', 'updateTaskLayout']]}>
        {(depValues) => {
          if (depValues?.settings?.updateTaskLayout == UpdateTaskLayout.LAYOUT_B) {
            return (
              <ProFormSelect
                name={['settings', 'updateTaskLayoutWSId']}
                placeholder="Workflow"
                label="Workflow ID"
                help="Select a Workflow which will be used in this task group. If not exist, please create a new one and define workflow steps."
                request={(params) => getSysWorkflowACList(params)}
                width="sm"
                fieldProps={{ dropdownMatchSelectWidth: false }}
              />
            );
          }
          return null;
        }}
      </ProFormDependency>

      <Divider />
      <ProFormDependency name={[['settings', 'layout']]}>
        {(depValues) => {
          return (
            <TaskBlockList
              group_id={props.initialValues?.id}
              group_layout={depValues?.settings?.layout}
            />
          );
        }}
      </ProFormDependency>

      <Divider />
      <ProFormSelect
        name="users"
        placeholder="Select user"
        label="Users"
        mode="multiple"
        showSearch
        request={(params: API.PageParams) => {
          return getUsersACList(params);
        }}
      />
      <Divider orientation="left">Columns in table viewer</Divider>
      <ProFormText
        name={['settings', 'wfStepIds']}
        placeholder="Workflow step IDs"
        label="Workflow step IDs"
        help="specify workflow step ids in CSV which will be used as columns in table viewer. Format {id}:{width}  e.g. 2:100,3:153,4"
      />
    </ModalForm>
  );
};

export default UpdateForm;
