import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { But<PERSON>, Col, message, Row, Divider } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import { ProFormText, ModalForm } from '@ant-design/pro-form';
import { updateTask } from '@/services/app/Task/task';
import Util from '@/util';
import type { ActionType } from '@ant-design/pro-table';
import { useModel } from 'umi';
import _ from 'lodash';
import { TaskBlockLayout, TaskStatusOptions } from '@/constants';
import SubTaskList from './SubTaskList';
import type { onTaskClickType } from '..';
import TaskCommentList from './TaskCommentList';
import SProFormDateRange from '@/components/SProFormDateRange';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...');

  try {
    const res = await updateTask(fields);
    hide();
    message.success('Update is successful');
    return res;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

// eslint-disable-next-line @typescript-eslint/ban-types
export type FormValueType = {} & Partial<API.Task>;

export type UpdateFormInBProps = {
  initialValues?: Partial<API.Task & { group_id?: number; block_id?: number; index?: number }>;
  onSubmit?: (formData: API.Task) => Promise<boolean | void>;
  onTaskClick?: onTaskClickType;

  refreshTick: number;
  setRefreshTick: Dispatch<SetStateAction<number>>;
};

const UpdateFormInB: React.FC<UpdateFormInBProps> = (props) => {
  const { getBlockLayout } = useModel('task-group');

  const { initialValues: task, onTaskClick, refreshTick, setRefreshTick } = props;
  const { parent_id: parentId, group_id: groupId, block_id: droppableId, index } = task || {};

  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  // we disable block Ids editing for now. Multiple comments below.
  // const [blockIds, setBlockIds] = useState<number[]>([]);

  useEffect(() => {
    if (props.initialValues) {
      if (formRef.current) {
        const newValues = { ...(props.initialValues || {}) };
        formRef.current.setFieldsValue(newValues);
      }

      if (props.initialValues?.id) {
        actionRef.current?.reload();
        setRefreshTick((prev) => prev + 1);
      }
    }
  }, [props.initialValues, setRefreshTick]);

  const blockLayout = getBlockLayout(props.initialValues?.block_id);

  return (
    <ProForm
      layout="vertical"
      labelAlign="left"
      initialValues={props.initialValues || {}}
      formRef={formRef}
      style={{ width: 800 }}
      submitter={{
        render(__) {
          return [
            <Button key="submit" type="primary" onClick={() => formRef.current?.submit()}>
              Save
            </Button>,
          ];
        },
      }}
      onFinish={async (value) => {
        const res = await handleUpdate({
          ...value,
          id: props.initialValues?.id,
          block_id: [props.initialValues?.block_id],
        });

        if (res) {
          if (props.onSubmit) props.onSubmit({ ...res, block_id: props.initialValues?.block_id });
        }
      }}
    >
      <Row gutter={32}>
        <Col span={12}>
          <ProFormText
            required
            rules={[
              {
                required: true,
                message: 'Title is required',
              },
            ]}
            width="lg"
            name="title"
            label="Title"
          />
          {blockLayout == TaskBlockLayout.LAYOUT_T_DATE && (
            <>
              <SProFormDateRange
                label="Date"
                startDateName="date"
                endDateName="end_date"
                formRef={formRef}
              />
            </>
          )}
          <ProFormTextArea width="lg" name="desc" label="Description" />
          {task?.ref && (
            <>
              <Divider>{task?.ref_type}</Divider>
              <Row wrap={false} gutter={[16, 16]}>
                <Col span={6} className="bold">
                  Order No:
                </Col>
                <Col>{task?.ref?.order_no}</Col>
              </Row>
              <Row wrap={false}>
                <Col span={6} className="bold">
                  {task?.ref_type == 'OrderIn' ? 'Supplier' : 'Customer'}:
                </Col>
                <Col>
                  {task?.ref_type == 'OrderIn'
                    ? (task?.ref as API.OrderIn)?.supplier
                    : (task?.ref as API.OrderOut)?.customer}
                  :
                </Col>
              </Row>
              <Row wrap={false}>
                <Col span={6} className="bold">
                  {task?.ref_type == 'OrderIn' ? 'Description' : 'Description'}:
                </Col>
                <Col>
                  {task?.ref_type == 'OrderIn'
                    ? (task?.ref as API.OrderIn)?.desc
                    : (task?.ref as API.OrderOut)?.desc}
                </Col>
              </Row>
            </>
          )}

          {!task?.parent_id && (
            <>
              <Divider> </Divider>
              <SubTaskList
                tableTitle={'Sub tasks'}
                initialValues={task as any}
                onTaskClick={onTaskClick}
                groupId={groupId}
                droppableId={`${droppableId}`}
                index={index}
                refreshTick={refreshTick}
              />
            </>
          )}

          {parentId && (
            <ProFormSelect
              width="lg"
              name="status"
              label="Status"
              initialValue={0}
              options={TaskStatusOptions}
            />
          )}
        </Col>
        <Col span={12}>
          <TaskCommentList initialValues={props.initialValues} refreshTick={refreshTick} />
        </Col>
      </Row>
    </ProForm>
  );
};

export default UpdateFormInB;
