/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/order/order-out/detail-crm';

/** orderOut GET /api/order/order-out/detail-crm */
export async function getOrderOutDetailCrmList(
  params: API.PageParams & { order_no?: number },
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.OrderOutDetailCrm>> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/**  
 * Save CMR data
 * 
 * PUT /api/order/order-out/detail-crm */
export async function updateOrderOutDetailCmr(
  data: API.OrderOutDetailCrm,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject<API.OrderOutDetailCrm>>(`${urlPrefix}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** 
 * get CMR authors
 * 
 * GET /api/order/order-out/detail-crm/authors */
export async function getOrderOutDetailCrmAuthors(
  params?: API.PageParams & { order_no?: number },
  sort?: any,
  filter?: any,
): Promise<any[]> {
  return request<API.BaseResult>(`${urlPrefix}/authors`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params?.pageSize,
      page: params?.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => res.message.map((x: string) => ({ value: x, label: x })));
}

/** put PUT /api/order/order-out/detail-crm/export-by-tpl */
export async function exportOrderOutDetailCrmAndAddress(
  data: {
    orderNo: number;
    tplId: number;
    crmId: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.ResultObject<API.Downloadable>>(`${urlPrefix}/export-by-tpl`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}


/**  
 * Get Rendered Email template
 * 
 * PUT /api/order/order-out/detail-crm/getRenderedEmailTemplate */
export async function getRenderedOrderOutCrmEmailTemplate(
  data: {
    orderNo: number;
    tplId: number;
    crmId: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.ResultObject<API.EmailTemplate>>(`${urlPrefix}/getRenderedEmailTemplate`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}
