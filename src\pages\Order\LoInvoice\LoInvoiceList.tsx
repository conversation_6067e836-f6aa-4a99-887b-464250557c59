import React, { useRef, useEffect } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util from '@/util';

import { getLoInvoiceList } from '@/services/app/LexOffice/lo-invoice';
import { FilePdfOutlined } from '@ant-design/icons';

type LoInvoiceListProps = {
  title?: string;
  order_no?: number;
  lo_type?: API.LoVoucherType;
  lo_types?: API.LoVoucherType[];
  lo_status?: 'draft' | 'open';
  hide_date?: boolean;
  refreshTick?: number;
};

const LoInvoiceList: React.FC<LoInvoiceListProps> = ({
  title,
  order_no,
  lo_type,
  lo_types,
  lo_status,
  hide_date,
  refreshTick,
}) => {
  const actionRef = useRef<ActionType>();
  // const [currentRow, setCurrentRow] = useState<API.LoInvoice>();
  // const [selectedRowsState, setSelectedRows] = useState<API.LoInvoice[]>([]);

  useEffect(() => {
    if (refreshTick) {
      actionRef.current?.reload();
    }
  }, [refreshTick]);

  useEffect(() => {
    if (order_no) {
      actionRef.current?.reload();
    }
  }, [order_no]);

  const columns: ProColumns<API.LoInvoice>[] = [
    {
      title: 'Order No',
      dataIndex: 'order_no',
      sorter: true,
      hideInTable: !!order_no,
    },
    {
      title: 'Type',
      dataIndex: 'lo_type',
      sorter: true,
      hideInForm: true,
      ellipsis: true,
      hideInTable: !!lo_type,
    },
    {
      title: '',
      dataIndex: 'lo_document_file_id',
      sorter: false,
      hideInForm: true,
      ellipsis: true,
      width: 30,
      className: 'p0',
      render: (dom, record) => {
        return record.lo_document_file_id && record.file_url ? (
          <a href={`${API_URL}/${record.file_url}`} target="_blank" rel="noreferrer">
            <FilePdfOutlined />
          </a>
        ) : undefined;
      },
    },
    {
      title: 'ID',
      dataIndex: 'lo_id',
      sorter: true,
      hideInForm: true,
      ellipsis: true,
      width: 150,
      render: (dom, record) => {
        let partialUrl = 'quotations';
        if (record.lo_type == 'invoice') partialUrl = 'invoices';
        else if (record.lo_type == 'order_confirmation') partialUrl = 'order-confirmations';
        return (
          <a
            href={`https://app.lexoffice.de/permalink/${partialUrl}/view/${record.lo_id}`}
            target="_blank"
            rel="noreferrer"
          >
            {record.lo_id}
          </a>
        );
      },
    },
    {
      title: 'No',
      dataIndex: 'lo_no',
      sorter: true,
      hideInForm: true,
      ellipsis: true,
    },
    {
      title: 'Status',
      dataIndex: 'lo_status',
      sorter: true,
      hideInForm: true,
      ellipsis: true,
    },
    {
      title: 'Created on',
      sorter: true,
      dataIndex: 'created_on',
      valueType: 'dateTime',
      search: false,
      width: 150,
      hideInTable: hide_date,
      className: 'c-grey text-sm',
      render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
    },
    {
      title: 'Updated on',
      sorter: true,
      dataIndex: 'updated_on',
      valueType: 'dateTime',
      search: false,
      width: 150,
      hideInTable: hide_date,
      className: 'c-grey text-sm',
      render: (dom, record) => Util.dtToDMYHHMM(record.updated_on),
    },
    /* {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 120,
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            setCurrentRow(record);
          }}
        >
          Edit
        </a>,
      ],
    }, */
  ];

  return (
    <ProTable<API.LoInvoice, API.PageParams>
      headerTitle={title ?? undefined}
      actionRef={actionRef}
      rowKey="id"
      revalidateOnFocus={false}
      options={{ fullScreen: false, density: false, reload: true, search: false, setting: false }}
      search={false}
      size="small"
      cardProps={{ bodyStyle: { padding: 0 } }}
      pagination={{
        showSizeChanger: true,
        pageSize: 10,
        hideOnSinglePage: true,
      }}
      request={(params, sort, filter) => {
        if (!order_no) return Promise.resolve({ data: [], success: true, total: 0 });
        return getLoInvoiceList(
          { ...params, order_no: order_no, lo_type, lo_status, lo_types },
          sort,
          filter,
        );
      }}
      columnEmptyText=""
      columns={columns}
      rowSelection={false}
    />
  );
};

export default LoInvoiceList;
