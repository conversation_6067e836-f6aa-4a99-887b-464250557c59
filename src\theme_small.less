.ant-page-header {
  padding: 8px 24px;
  list-style: none;
}

/*
Table customization
-------------------------------------------------------------------
*/
.ant-table.ant-table-small .ant-table-title,
.ant-table.ant-table-small .ant-table-footer,
.ant-table.ant-table-small .ant-table-thead > tr > th,
.ant-table.ant-table-small .ant-table-tbody > tr.ant-table-row > td,
.ant-table.ant-table-small tfoot > tr > th,
.ant-table.ant-table-small tfoot > tr > td {
  padding: 3px 5px !important;
  font-size: 13px;
  input,
  textarea {
    font-size: 13px;
  }
  .ant-form-item-control-input {
    min-height: 24px;
  }
  .ant-input-affix-wrapper > input.ant-input {
    font-size: 13px;
  }
}
.ant-pro-table td.ant-table-cell > a {
  font-size: 13px !important;
  line-height: 20px;
}
.ant-radio-wrapper {
  font-size: 13px;
}

.editable-table {
  .ant-table.ant-table-small .ant-table-tbody > tr > td.ant-table-cell {
    padding: 5px 5px !important;
  }
}

/*
Table customization 2 for xs table
-------------------------------------------------------------------
*/
.size-xs {
  .ant-table.ant-table-small .ant-table-title,
  .ant-table.ant-table-small .ant-table-footer,
  .ant-table.ant-table-small .ant-table-thead > tr > th,
  .ant-table.ant-table-small .ant-table-tbody > tr.ant-table-row > td,
  .ant-table.ant-table-small tfoot > tr > th,
  .ant-table.ant-table-small tfoot > tr > td {
    padding: 3px 5px !important;
    font-size: 11px;
    input,
    textarea {
      font-size: 11px;
    }
    .ant-form-item-control-input {
      min-height: 24px;
    }
    .ant-input-affix-wrapper > input.ant-input {
      font-size: 11px;
    }
  }
  .ant-pro-table td.ant-table-cell > a {
    font-size: 11px !important;
    line-height: 20px;
  }
  .ant-radio-wrapper {
    font-size: 11px;
  }
}

.size-sm {
  .ant-table.ant-table-small .ant-table-title,
  .ant-table.ant-table-small .ant-table-footer,
  .ant-table.ant-table-small .ant-table-thead > tr > th,
  .ant-table.ant-table-small .ant-table-tbody > tr.ant-table-row > td,
  .ant-table.ant-table-small tfoot > tr > th,
  .ant-table.ant-table-small tfoot > tr > td {
    padding: 3px 5px !important;
    font-size: 12px;
    input,
    textarea {
      font-size: 12px;
    }
    .ant-form-item-control-input {
      min-height: 24px;
    }
    .ant-input-affix-wrapper > input.ant-input {
      font-size: 12px;
    }
  }
  .ant-pro-table td.ant-table-cell > a {
    font-size: 11px !important;
    line-height: 20px;
  }
  .ant-radio-wrapper {
    font-size: 12px;
  }
}

/** Form
-------------------------------------- */
.ant-form-inline .ant-form-item {
  margin-right: 8px;
}
.search-form .ant-space.ant-space-horizontal {
  align-items: start;
}

/* .ant-form-item-label > label {
  font-size: 13px;
  line-height: 20px;
}

.ant-input-affix-wrapper {
  position: relative;
  display: inline-block;
  display: inline-flex;
  width: 100%;
  min-width: 0;
  padding: 4px 8px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 13px;
  line-height: 20px;
  line-height: 1.5715;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  transition: all 0.3s;
}

.ant-select {
  font-size: 13px;
}

.ant-select-item {
  min-height: 30px;
  padding: 3px 8px;
  font-size: 13px;
  line-height: 20px;
}

.ant-select-multiple .ant-select-selection-item {
  height: 22px;
  line-height: 20px;
} */

.ant-back-top {
  position: fixed;
  right: 0;
  bottom: 30px;
  font-size: 13px;
}
