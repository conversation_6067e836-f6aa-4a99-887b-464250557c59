import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSwitch } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { addTaskGroup } from '@/services/app/Task/task-group';
import { Divider, message } from 'antd';
import Util from '@/util';
import { getUsersACList } from '@/services/app/user';
import SProFormDigit from '@/components/SProFormDigit';
import { TaskGroupLayout, TaskGroupLayoutKV } from '@/constants';

const handleAdd = async (fields: API.TaskGroup) => {
  const hide = message.loading('Adding...');
  const data = { ...fields };
  try {
    await addTaskGroup(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type CreateFormProps = {
  values?: Partial<API.TaskGroup>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.TaskGroup) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={'New Task Group'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.TaskGroup);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormText
        rules={[
          {
            required: true,
            message: 'Name is required',
          },
        ]}
        width="md"
        name="name"
        label="Name"
        fieldProps={{
          onChange: (e) => {
            formRef.current?.setFieldValue('code', Util.sInitials(e.target.value));
          },
        }}
      />
      <ProFormText
        rules={[
          {
            required: true,
            type: 'regexp',
            pattern: new RegExp(/[a-zA-Z0-9-]/),
            message: 'Alpah, numeric, dash only.',
          },
          {
            required: true,
            max: 10,
            message: 'Code is required',
          },
        ]}
        help="Alpah, numeric, dash only. Max 10."
        width="md"
        name="code"
        label="Code"
      />
      <SProFormDigit name="sort" label="Sort" placeholder="Sort" />
      <ProFormSwitch name={['settings', 'useInitials']} label="Use user initials?" />
      <ProFormSelect
        name={['settings', 'layout']}
        placeholder="Group Layout"
        label="Group Layout"
        initialValue={TaskGroupLayout.LAYOUT_DEFAULT}
        valueEnum={TaskGroupLayoutKV}
      />
      <Divider />
      <ProFormSelect
        name="users"
        placeholder="Select user"
        mode="multiple"
        label="Users"
        showSearch
        request={(params: API.PageParams) => {
          return getUsersACList(params);
        }}
      />
    </ModalForm>
  );
};

export default CreateForm;
