import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormDependency, ProFormDigit } from '@ant-design/pro-form';
import { ProFormItem, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { addSysXlsColNameMap } from '@/services/app/Sys/sys-xls-col-name-map';
import { message } from 'antd';
import Util from '@/util';
import ColumnNamesFormList from './ColumnNamesFormList';
import { DbFieldType } from '@/constants';
import type { MappableTableType } from '..';
import { getMappableDBTablesOptions } from '..';

const handleAdd = async (fields: API.SysXlsColNameMap) => {
  const hide = message.loading('Adding...', 0);
  const data = { ...fields };
  try {
    await addSysXlsColNameMap(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type CreateFormProps = {
  values?: Partial<API.SysXlsColNameMap>;
  tables?: Record<string, MappableTableType>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.SysXlsColNameMap) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={'New XLS Column Map'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 7 }}
      wrapperCol={{ span: 16 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd({
          ...value,
          xls_column: value.xls_column_list?.map((x: any) => x.value),
        });
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormSelect
        name="table_name"
        label="Table Name"
        placeholder="Please select table name"
        required
        rules={[
          {
            required: true,
            message: 'Table Name is required.',
          },
        ]}
        options={getMappableDBTablesOptions(props.tables)}
        width="md"
      />
      <ProFormText
        name="db_field"
        label="DB Field"
        required
        rules={[
          {
            required: true,
            message: 'DB Field is required.',
          },
        ]}
      />
      <ProFormSelect
        name="db_field_type"
        label="DB Field Type"
        options={DbFieldType}
        required
        rules={[
          {
            required: true,
            message: 'DB Field Type is required.',
          },
        ]}
      />
      <ProFormDependency name={['db_field_type', 'table_name']}>
        {(depValues) => {
          const isLengthAvailable = depValues.db_field_type == 'varchar';
          return !isLengthAvailable || !props.tables?.[depValues.table_name]?.columnDeletable ? (
            <></>
          ) : (
            <>
              <ProFormDigit
                width="md"
                name="db_field_length"
                tooltip="Legnth"
                label="Length"
                required
                initialValue={255}
                rules={[
                  {
                    required: true,
                    message: 'Length is required.',
                  },
                ]}
              />
            </>
          );
        }}
      </ProFormDependency>
      <ProFormText name="db_field_comment" label="DB Field Comment" />
      <ProFormItem label="XLS Names" style={{ marginBottom: 0 }}>
        <ColumnNamesFormList />
      </ProFormItem>
    </ModalForm>
  );
};

export default CreateForm;
