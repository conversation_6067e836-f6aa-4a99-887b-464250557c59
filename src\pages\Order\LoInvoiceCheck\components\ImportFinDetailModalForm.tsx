import type { Dispatch, SetStateAction } from 'react';
import { useEffect } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormUploadButton } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { message } from 'antd';
import Util from '@/util';
import type { RcFile, UploadFile } from 'antd/lib/upload';
import SProFormDigit2 from '@/components/SProFormDigit2';
import { importFinDetailFromXls } from '@/services/app/Fin/fin-detail';

export type FormValueType = {
  headerRowNo?: number;
  dataStartRowNo?: number;
  importMode?: string;
  delimiter?: string;
  files?: UploadFile[];
};

const handleImport = async (dataParam: FormValueType) => {
  const data = new FormData();
  data.set('dataStartRowNo', `${dataParam.dataStartRowNo}`);
  data.set('headerRowNo', `${dataParam.headerRowNo}`);
  if (dataParam.delimiter) {
    data.set('delimiter', `${dataParam.delimiter}`);
  }
  if (dataParam.importMode) {
    data.set('importMode', `${dataParam.importMode}`);
  }

  if (dataParam.files) {
    data.append(`file`, dataParam.files[0].originFileObj as RcFile);
  }

  const hide = message.loading('Importing Fin_details from selected file...', 0);
  return importFinDetailFromXls(data)
    .then((res) => {
      return res;
    })
    .catch((err) => Util.error(err))
    .finally(() => hide());
};

export type ImportFinDetailModalFormProps = {
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: FormValueType) => Promise<boolean | void>;
};

const ImportFinDetailModalForm: React.FC<ImportFinDetailModalFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;

  useEffect(() => {
    if (modalVisible) {
      const cachedData = Util.getSfValues('f_fin_detail_import', {});
      formRef.current?.setFieldsValue(cachedData);
    }
  }, [modalVisible]);

  return (
    <ModalForm<FormValueType>
      title={'Import FinDetails_until2023 from XLS/CSV file'}
      formRef={formRef}
      width="700px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      size="small"
      className="search-form"
      labelAlign="left"
      labelCol={{ flex: '150px' }}
      onFinish={async (formValue) => {
        const success = await handleImport({ ...formValue, importMode: 'until2023' });
        if (success) {
          if (formRef.current) formRef.current.setFieldValue('files', null);
          if (onSubmit) await onSubmit(formValue);
        }
      }}
    >
      <SProFormDigit2
        label="Header Row No"
        name="headerRowNo"
        width={100}
        initialValue={4}
        min={1}
      />
      <SProFormDigit2
        label="First Data Row No"
        name="dataStartRowNo"
        width={100}
        initialValue={5}
        min={1}
      />
      <ProFormUploadButton
        max={1}
        name="files"
        label="File"
        title="Select File"
        accept=".xls,.xlsx,.csv,.xlsm"
        required
        rules={[
          {
            required: true,
            message: 'File is required',
          },
        ]}
        fieldProps={{
          beforeUpload: () => {
            return false;
          },
        }}
      />
    </ModalForm>
  );
};

export default ImportFinDetailModalForm;
