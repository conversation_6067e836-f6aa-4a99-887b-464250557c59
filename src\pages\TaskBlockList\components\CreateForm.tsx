import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { addTaskBlock } from '@/services/app/Task/task-block';
import { message } from 'antd';
import Util from '@/util';

const handleAdd = async (fields: API.TaskBlock) => {
  const hide = message.loading('Adding...');
  const data = { ...fields };
  try {
    await addTaskBlock(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error('Adding failed, please try again!', error);
    return false;
  } finally {
    hide();
  }
};

export type CreateFormProps = {
  values?: Partial<API.TaskBlock>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.TaskBlock) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={'New Task Block'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.TaskBlock);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormText
        rules={[
          {
            required: true,
            message: 'Name is required',
          },
        ]}
        width="md"
        name="name"
        label="Name"
        fieldProps={{
          onChange: (e) => {
            formRef.current?.setFieldValue('code', Util.sInitials(e.target.value));
          },
        }}
      />
      <ProFormText
        rules={[
          {
            required: true,
            message: 'Code is required',
          },
        ]}
        width="md"
        name="name"
        label="Name"
      />
    </ModalForm>
  );
};

export default CreateForm;
