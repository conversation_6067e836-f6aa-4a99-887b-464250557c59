import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useState } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import { addOrderOutHead } from '@/services/app/Order/order-out-head';
import { Button, message, Modal } from 'antd';
import Util from '@/util';
import OrderOutHeadList from '..';

const handleAdd = async (fields: API.OrderOutHead) => {
  const hide = message.loading('Adding...', 0);
  const data = { ...fields };
  try {
    await addOrderOutHead(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

// eslint-disable-next-line @typescript-eslint/ban-types
export type FormValueType = {};

export type CreateFormProps = {
  values?: Partial<API.OrderOutHead>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.OrderOutHead) => Promise<boolean | void>;

  includeLatestList?: boolean;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  const [refreshTick, setRefreshTick] = useState<number>(0);

  useEffect(() => {
    setRefreshTick((prev) => prev + 1);
  }, [modalVisible]);

  return (
    <>
      <Modal
        title={'New Order Out Head'}
        width="500px"
        open={modalVisible}
        footer={false}
        onCancel={() => {
          handleModalVisible(false);
        }}
      >
        <ProForm
          layout="horizontal"
          labelAlign="left"
          grid
          formRef={formRef}
          submitter={false}
          onFinish={async (value) => {
            const newOrderOutHeadHead = {
              ...value,
            };
            const res = await handleAdd(newOrderOutHeadHead as API.OrderOutHead);
            if (res) {
              if (formRef.current) formRef.current.resetFields();
              if (onSubmit) await onSubmit({ ...newOrderOutHeadHead });
            }
          }}
        >
          <ProFormText
            required
            rules={[
              {
                required: true,
                message: 'Name is required',
              },
            ]}
            width="md"
            name="name"
            label="Name"
            colProps={{ flex: '0 1 250px' }}
          />
          <Button
            key="submit"
            type="primary"
            onClick={() => formRef.current?.submit()}
            style={{ flex: '1 0 50px' }}
          >
            Create
          </Button>
        </ProForm>
        {props.includeLatestList && (
          <OrderOutHeadList
            pageSize={props.includeLatestList ? 15 : 10}
            hidePageContainer
            refreshTick={refreshTick}
          />
        )}
      </Modal>
    </>
  );
};

export default CreateForm;
