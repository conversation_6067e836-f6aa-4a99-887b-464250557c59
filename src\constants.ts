export const LS_PREFIX = 'WHC_ORDER_';
export const LS_TOKEN_NAME = `${LS_PREFIX}TOKEN`;
export const DEFAULT_PER_PAGE_PAGINATION = 100;
export const AC_PER_PAGE_PAGINATION = 100;

export const DAYS = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

export const CURRENT_YEAR = new Date().getFullYear();

export const EURO = '€';
export const EURO2 = ' €';

export enum UserRole {
  USER = 0,
  ADMIN = 1,
}

export enum UserStatus {
  DISABLED = 0,
  ENABLED = 1,
  BLOCKED = 2,
}

export enum AlertStatus {
  'success' = 'success',
  'error' = 'error',
  'info' = 'info',
}

export enum Status {
  'ACTIVE' = 1,
  'INACTIVE' = 0,
}

export const StatusOptions = [
  { value: Status.ACTIVE, label: 'Active' },
  { value: Status.INACTIVE, label: 'Inactive' },
];

export const YNOptions = [
  { value: 1, label: 'Ja' },
  { value: 0, label: 'Nein' },
];

export const YNOptionsStr = [
  { value: '1', label: 'Ja' },
  { value: '0', label: 'Nein' },
];

export enum ScrapSystem {
  WoS = 'WoS',
  SwO = 'SwO',
}
export const ScrapSystemIds = [ScrapSystem.WoS, ScrapSystem.SwO];

export enum TaskStatus {
  OPEN = 0,
  IN_PROGRESS = 2,
  WAITING = 3,
  PROBLEM = 4,
  DONE = 1,
}

export const TaskStatusOptions = [
  { value: TaskStatus.OPEN, label: 'Open' },
  { value: TaskStatus.IN_PROGRESS, label: 'In Progress' },
  { value: TaskStatus.WAITING, label: 'Waiting' },
  { value: TaskStatus.PROBLEM, label: 'Problem' },
  { value: TaskStatus.DONE, label: 'Done' },
];

export enum TaskVisible {
  INVISIBLE = 0,
  VISIBLE = 1,
}

export enum TaskGroupStatus {
  ACTIVE = 1,
  HIDDEN = 2,
}

export enum OrderPrefix {
  OrderIn = 'WE',
  OrderOut = 'WA',
}

export enum TaskGroupLayout {
  LAYOUT_DEFAULT = 'Default',
  LAYOUT_2 = 'Layout 2',
  LAYOUT_3 = 'Layout 3',
}

export const TaskGroupLayoutKV = {
  [TaskGroupLayout.LAYOUT_DEFAULT]: 'Std 1 - 3 x 2',
  [TaskGroupLayout.LAYOUT_2]: 'Std 2 - 4 x 2',
  [TaskGroupLayout.LAYOUT_3]: 'Std 3 - 5 x 2',
};

/**
 * Update Task layout: defines how to show Update Task modal dialog.
 */
export enum UpdateTaskLayout {
  LAYOUT_A = 'Layout A',    // default
  LAYOUT_B = 'Layout B',    // sys workflow based layouts
}

export const UpdateTaskLayoutKV = {
  [UpdateTaskLayout.LAYOUT_A]: 'Layout A',
  [UpdateTaskLayout.LAYOUT_B]: 'Layout B',
};


export enum TaskBlockLayout {
  LAYOUT_A = 'Std A',
  LAYOUT_B = 'Std B',
  LAYOUT_C = 'Std C',
  LAYOUT_T_DATE = 'T Date',
  LAYOUT_ORDER_C = 'Order C',
}

export const TaskBlockLayoutOptions = [
  { value: TaskBlockLayout.LAYOUT_A, label: TaskBlockLayout.LAYOUT_A },
  { value: TaskBlockLayout.LAYOUT_B, label: TaskBlockLayout.LAYOUT_B },
  { value: TaskBlockLayout.LAYOUT_C, label: TaskBlockLayout.LAYOUT_C },
  { value: TaskBlockLayout.LAYOUT_T_DATE, label: 'T (Date)' },
  { value: TaskBlockLayout.LAYOUT_ORDER_C, label: 'Order C' },
];

export enum TaskBlockPosition {
  BLOCK_1 = 1,
  BLOCK_2 = 2,
  BLOCK_3 = 3,
  BLOCK_4 = 4,
  BLOCK_5 = 5,
  BLOCK_6 = 6,
}

export const TaskBlockPositionMap = {
  [TaskBlockPosition.BLOCK_1]: { text: 'Block #1' },
  [TaskBlockPosition.BLOCK_2]: { text: 'Block #2' },
  [TaskBlockPosition.BLOCK_3]: { text: 'Block #3' },
  [TaskBlockPosition.BLOCK_4]: { text: 'Block #4' },
  [TaskBlockPosition.BLOCK_5]: { text: 'Block #5' },
  [TaskBlockPosition.BLOCK_6]: { text: 'Block #6' },
};

export enum XlsTemplateType {
  ADDRESS = 'address',
}

export const XlsTemplateTypeOptions = [{ value: XlsTemplateType.ADDRESS, label: 'Address' }];

export enum DictType {
  OrderOutType = 'order out',
  OrderOutCategory = 'order out cat',
  OrderOutFileCategoryName = 'order out file cat name',
  SysConfig = 'sys config',
  Susa = 'susa',
}

export const DictTypeKv: Record<DictType | string, string> = {
  [DictType.SysConfig]: 'System Config',
  [DictType.OrderOutType]: 'OrderOut',
  [DictType.OrderOutCategory]: 'OrderOut Category',
  [DictType.OrderOutFileCategoryName]: 'OrderOut File Category Name',
  [DictType.Susa]: 'Susa',
};

// biz logic constants
export const SUPPLIER_ID_WHC = 1;

export const DT_FORMAT_DMY = 'DD.MM.YYYY';
export const DT_FORMAT_MY = 'MM.YYYY';
export const DT_FORMAT_DMY_HHMMSS = 'DD.MM.YYYY';
export const DT_FORMAT_YMD = 'YYYY-MM-DD';
export const DT_FORMAT_TIME_MAX = '23:59:59';
export const DT_FORMAT_TIME_MAX_S = ' 23:59:59';
export const DT_FORMAT_LIST = [DT_FORMAT_DMY, DT_FORMAT_YMD];

export enum DictCode {
  LOTUS_PATH = 'LOTUS_PATH',
  PARENT_TASK_BG = 'PARENT_TASK_BG',
  CODE_ORDER_IN_WORKFLOW_ID = 'ORDER_IN_WORKFLOW_ID',
  CODE_ALWAYS_ON_LOADING_ADDRESS = 'ALWAYS_ON_LOADING_ADDRESS',

  CODE_ORDER_OUT_WORKFLOW_ID = 'ORDER_OUT_WORKFLOW_ID',
  CODE_ORDER_OUT_SUP_WORKFLOW_ID = 'ORDER_OUT_SUP_WORKFLOW_ID',
  CODE_ORDER_OUT_EUR_STATEMENT1 = 'ORDER_OUT_EUR_STATEMENT1',

  ORDER_OUT_AUTHORS = 'ORDER_OUT_AUTHORS',
  ORDER_OUT_CRM_XLS_TEMPLATE = 'ORDER_OUT_CRM_XLS_TEMPLATE',
  ORDER_OUT_CMR_EMAIL_TEMPLATE = 'ORDER_OUT_CMR_EMAIL_TEMPLATE',
  PAYMENT_TERMS = 'PAYMENT_TERMS',

  // order out file category names
  OOFCN_DETAIL_FILE = 'OOFCN_DETAIL_FILE',
  OOFCN_OTHER = 'OOFCN_OTHER',
  OOFCN_MRN_RETURN = 'OOFCN_MRN_RETURN',
  OOFCN_AB_SUPPLIER = 'OOFCN_AB_SUPPLIER',
  OOFCN_INCOMING_INVOICE = 'OOFCN_INCOMING_INVOICE',
  OOFCN_PACK_LIST_INVOICE = 'OOFCN_PACK_LIST_INVOICE',
  OOFCN_PACK_LIST_QUOTATION = 'OOFCN_PACK_LIST_QUOTATION',
  OOFCN_INCOMING_INVOICE_FREIGHT = 'OOFCN_INCOMING_INVOICE_FREIGHT',
  OOFCN_MRN_VON_UNS = 'OOFCN_MRN_VON_UNS',
  OOFCN_QUOTATION = 'OOFCN_QUOTATION',
  OOFCN_INVOICE = 'OOFCN_INVOICE',
  // lo susa
  SUSA_CATEGORY_COLOR = 'SUSA_CATEGORY_COLOR',

  // is copy or move
  CODE_FM_MISC_FILE_COPY_MODE = 'FM_MISC_FILE_COPY_MODE',
}

export enum ContactType {
  SUPPLIER_CONTACT = 'supplier',
  SUPPLIER_ADDRESS_CONTACT = 'supplier_address',
  CUSTOMER_CONTACT = 'customer',
  CUSTOMER_ADDRESS_CONTACT = 'customer_address',
}

export enum FileCategory {
  CAT_ADDRESS_TEMPLATE = 'address-template',
  CAT_ORDER_OUT_DETAIL_FILE = 'order-out-detail-file',
  CAT_ORDER_OUT_SEC_FILE = 'order-out-sec-file',

  // files which are linked by `order_out_file_map` table
  CAT_ORDER_OUT_AB_SUPPLIER = 'order-out-ab-supplier',
  CAT_ORDER_OUT_PACK_LIST_QUOTATION = 'order-out-pack-list-quotation',
  CAT_ORDER_OUT_PACK_LIST_INVOICE = 'order-out-pack-list-invoice',
  CAT_ORDER_OUT_MRN_RETURN = 'order-out-mrn-return', // zuruck (return in ENG)
  CAT_ORDER_OUT_MRN_VON_UNS = 'order-out-mrn-von-uns',

  CAT_ORDER_OUT_OTHER = 'order-out-other', // sonstige (other in ENG)
  CAT_ORDER_OUT_INCOMING_INVOICE = 'order-out-incoming-invoice', // eingansrechung
  CAT_ORDER_OUT_INCOMING_INVOICE_FREIGHT = 'order-out-incoming-invoice-freight', // eingansrechung
}

export const DbFieldType = [
  'int',
  'bigint',
  'double',
  'numeric',

  'text',
  'longtext',
  'varchar',

  'date',
  'datetime',
];

export const getDbFieldUIAttributes = (dbFieldType: string) => {
  const attr: any = {
    isNumber: false,
    isText: true,
  };

  if (
    dbFieldType == 'int' ||
    dbFieldType == 'bigint' ||
    dbFieldType == 'double' ||
    dbFieldType == 'numeric'
  ) {
    attr.isNumber = true;
    attr.isText = false;
    if (dbFieldType == 'int' || dbFieldType == 'bigint') {
      attr.precision = 0;
    } else if (dbFieldType == 'double' || dbFieldType == 'numeric') {
      attr.precision = 2;
    }
  }
  return attr;
};

export const ADDRESS_LO_TAX_TYPE_NONE = 'None';
export const ADDRESS_LO_TAX_TYPE_EU_TAX_FREE = 'EU Tax free';
export const ADDRESS_LO_TAX_TYPE_EXPORT = 'Export (Outside EU)';

export const AddressLoTaxTypeOptions = [
  { value: ADDRESS_LO_TAX_TYPE_NONE, label: ADDRESS_LO_TAX_TYPE_NONE },
  { value: ADDRESS_LO_TAX_TYPE_EU_TAX_FREE, label: ADDRESS_LO_TAX_TYPE_EU_TAX_FREE },
  { value: ADDRESS_LO_TAX_TYPE_EXPORT, label: ADDRESS_LO_TAX_TYPE_EXPORT },
];

export enum TransactionType {
  GL = 'GL', // Sachkonten
  Debitor = 'Debitor',// Debitoren
  Creditor = 'Creditor',// Kreditoren
}

export const TransactionTypeOptions = [
  { value: TransactionType.GL, label: 'Sachkonten' },
  { value: TransactionType.Debitor, label: 'Debitoren' },
  { value: TransactionType.Creditor, label: 'Kreditoren' },
];

export enum XlsImportableDBTable {
  order_out_xls_detail_local = 'order_out_xls_detail_local',
  lo_susa = 'lo_susa',
  fin_inventur_all = 'fin_inventur_all',
  fin_sinfo = 'fin_sinfo',
}

export type MappableTableKv = {
  [XlsImportableDBTable.order_out_xls_detail_local]: 'XLS File Data';
  [XlsImportableDBTable.lo_susa]: 'SuSa';
  [XlsImportableDBTable.fin_inventur_all]: 'Inventur All';
  [XlsImportableDBTable.fin_sinfo]: 'Sinfos';
};

export const CSVFileDelimiterOptions = [
  { value: ';', label: 'Semi-colon' },
  { value: ',', label: 'XLS friendly (comma)' },
];

/**
 * ----------------------------------------------------------------------------
 * SysWorkflow
 * ----------------------------------------------------------------------------
 */
export const WF_ID_ORDER_OUT_INCOMING_INVOICE = 100;
export const WF_ID_ORDER_OUT_INCOMING_AB = 101;
export const WF_ID_ORDER_OUT_INCOMING_CMR = 102;
export const WF_ID_ORDER_OUT_INCOMING_FREIGHT = 103;
export const WF_ID_ORDER_OUT_INCOMING_OTHER2 = 104;

export const WF_ID_ORDER_IN_INCOMING_INVOICE = 200;
export const WF_ID_ORDER_IN_INCOMING_AB = 201;
export const WF_ID_ORDER_IN_INCOMING_CMR = 202;
export const WF_ID_ORDER_IN_INCOMING_FREIGHT = 203;
export const WF_ID_ORDER_IN_INCOMING_OTHER2 = 204;

export const WF_ID_OTHER_INVOICE = 300;
export const WF_ID_OTHER_AB = 301;
export const WF_ID_OTHER_CMR = 302;
export const WF_ID_OTHER_FREIGHT = 303;
export const WF_ID_OTHER_OTHER2 = 304;


export enum SysWorkflowType {
  Task = 'update task modal',
  OrderOutSupplier = 'OrderOut Supplier',
  OrderOutIncomingInvoice = 'OrderOut - Invoice (Incoming)',
}

export const SysWorkflowTypeOptionsKv = {
  [SysWorkflowType.Task]: 'Task Workflow',
  [SysWorkflowType.OrderOutSupplier]: 'OrderOut Supplier',
  [SysWorkflowType.OrderOutIncomingInvoice]: 'OrderOut - Invoice (Incoming)',
}

export const SysWorkflowTypeOptionsKvCreatable = {
  [SysWorkflowType.Task]: 'Task Workflow',
  [SysWorkflowType.OrderOutSupplier]: 'OrderOut Supplier',
  // [SysWorkflowType.OrderOutIncomingInvoice]: 'OrderOut - Invoice (Incoming)',
}
