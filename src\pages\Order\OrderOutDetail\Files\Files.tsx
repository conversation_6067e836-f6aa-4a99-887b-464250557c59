import { DictCode, FileCategory } from '@/constants';
import { getOrderOut } from '@/services/app/Order/order-out';
import Util from '@/util';
import { Card, Col, Row } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { useModel } from 'umi';
import LoInvoiceList from '../../LoInvoice/LoInvoiceList';
import FileDetailUploadForm from './FileDetailUploadForm';
import FileVariantUploadForm from './FileVariantUploadForm';

type FilesProps = {
  orderNo: number;
};

const cardDefaultProps: any = {
  bordered: false,
  style: { height: '100%' },
  size: 'small',
};

const Files: React.FC<FilesProps> = (props) => {
  const { orderNo } = props;
  const { getCodeValue } = useModel('app-settings');

  const [orderOut, setOrderOut] = useState<API.OrderOut>({});

  const loadOrderOut = useCallback(
    (cb?: any) => {
      getOrderOut(orderNo, { with: 'detailFile,detailFileSetting,secFiles' })
        .then((res) => {
          setOrderOut(res);
          cb?.(res);
        })
        .catch((e) => Util.error(e));
    },
    [orderNo],
  );

  useEffect(() => {
    loadOrderOut();
  }, [loadOrderOut]);

  return (
    <div>
      <Row gutter={24} style={{ marginBottom: 8 }}>
        <Col span={12}>
          <Card title={getCodeValue(DictCode.OOFCN_DETAIL_FILE)} {...cardDefaultProps}>
            {orderOut && setOrderOut && (
              <FileDetailUploadForm orderOut={orderOut} setOrderOut={setOrderOut} />
            )}
          </Card>
        </Col>
        <Col span={12}>
          <Card title={getCodeValue(DictCode.OOFCN_AB_SUPPLIER)} {...cardDefaultProps}>
            {orderOut && setOrderOut && (
              <FileVariantUploadForm
                orderOut={orderOut}
                setOrderOut={setOrderOut}
                fileCategory={FileCategory.CAT_ORDER_OUT_AB_SUPPLIER}
              />
            )}
          </Card>
        </Col>
      </Row>
      <Row gutter={24} style={{ marginBottom: 8 }}>
        <Col span={12}>
          <Card
            title={getCodeValue(DictCode.OOFCN_QUOTATION)}
            {...cardDefaultProps}
            bodyStyle={{ padding: '1px 24px 24px 24px' }}
          >
            <LoInvoiceList
              hide_date
              //   lo_type="quotation"
              lo_types={['quotation', 'order_confirmation']}
              //   lo_status="open"
              order_no={orderOut?.order_no}
              //   refreshTick={refreshInvoiceList}
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card
            title={getCodeValue(DictCode.OOFCN_INVOICE)}
            {...cardDefaultProps}
            bodyStyle={{ padding: '1px 24px 24px 24px' }}
          >
            <LoInvoiceList
              hide_date
              lo_type="invoice"
              //   lo_status="open"
              order_no={orderOut?.order_no}
              //   refreshTick={refreshInvoiceList}
            />
          </Card>
        </Col>
      </Row>
      <Row gutter={24} style={{ marginBottom: 8 }}>
        <Col span={12}>
          <Card title={getCodeValue(DictCode.OOFCN_PACK_LIST_QUOTATION)} {...cardDefaultProps}>
            {orderOut && setOrderOut && (
              <FileVariantUploadForm
                orderOut={orderOut}
                setOrderOut={setOrderOut}
                fileCategory={FileCategory.CAT_ORDER_OUT_PACK_LIST_QUOTATION}
              />
            )}
          </Card>
        </Col>
        <Col span={12}>
          <Card title={getCodeValue(DictCode.OOFCN_PACK_LIST_INVOICE)} {...cardDefaultProps}>
            {orderOut && setOrderOut && (
              <FileVariantUploadForm
                orderOut={orderOut}
                setOrderOut={setOrderOut}
                fileCategory={FileCategory.CAT_ORDER_OUT_PACK_LIST_INVOICE}
              />
            )}
          </Card>
        </Col>
      </Row>
      <Row gutter={24} style={{ marginBottom: 8 }}>
        <Col span={12}>
          <Card title={getCodeValue(DictCode.OOFCN_MRN_VON_UNS)} {...cardDefaultProps}>
            {orderOut && setOrderOut && (
              <FileVariantUploadForm
                orderOut={orderOut}
                setOrderOut={setOrderOut}
                fileCategory={FileCategory.CAT_ORDER_OUT_MRN_VON_UNS}
              />
            )}
          </Card>
        </Col>
        <Col span={12}>
          <Card title={getCodeValue(DictCode.OOFCN_MRN_RETURN)} {...cardDefaultProps}>
            {orderOut && setOrderOut && (
              <FileVariantUploadForm
                orderOut={orderOut}
                setOrderOut={setOrderOut}
                fileCategory={FileCategory.CAT_ORDER_OUT_MRN_RETURN}
              />
            )}
          </Card>
        </Col>
      </Row>
      <Row gutter={24} style={{ marginBottom: 8 }}>
        <Col span={24}>
          <Card title={getCodeValue(DictCode.OOFCN_OTHER)} {...cardDefaultProps}>
            {orderOut && setOrderOut && (
              <FileVariantUploadForm
                orderOut={orderOut}
                setOrderOut={setOrderOut}
                fileCategory={FileCategory.CAT_ORDER_OUT_OTHER}
              />
            )}
          </Card>
        </Col>
      </Row>
      <Row gutter={24} style={{ marginBottom: 8 }}>
        <Col span={12}>
          <Card title={getCodeValue(DictCode.OOFCN_INCOMING_INVOICE)} {...cardDefaultProps}>
            {orderOut && setOrderOut && (
              <FileVariantUploadForm
                orderOut={orderOut}
                setOrderOut={setOrderOut}
                fileCategory={FileCategory.CAT_ORDER_OUT_INCOMING_INVOICE}
              />
            )}
          </Card>
        </Col>
        <Col span={12}>
          <Card title={getCodeValue(DictCode.OOFCN_INCOMING_INVOICE_FREIGHT)} {...cardDefaultProps}>
            {orderOut && setOrderOut && (
              <FileVariantUploadForm
                orderOut={orderOut}
                setOrderOut={setOrderOut}
                fileCategory={FileCategory.CAT_ORDER_OUT_INCOMING_INVOICE_FREIGHT}
              />
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};
export default Files;
