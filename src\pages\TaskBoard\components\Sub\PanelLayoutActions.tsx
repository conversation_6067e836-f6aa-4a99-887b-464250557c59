import type { MenuProps } from 'antd';
import { message } from 'antd';
import { Dropdown, Menu } from 'antd';
import { useMemo } from 'react';
import { useModel } from 'umi';
import _ from 'lodash';
import { TaskBlockLayout } from '@/constants';
import { TaskBlockLayoutOptions } from '@/constants';
import type { ItemType } from 'antd/lib/menu/hooks/useItems';
import { updateTaskBlock } from '@/services/app/Task/task-block';
import Util from '@/util';
import { getTaskGroup } from '@/services/app/Task/task-group';

type PanelLayoutActionsProps = {
  blockId: number;
  layout: TaskBlockLayout;
};

const PanelLayoutActions: React.FC<PanelLayoutActionsProps> = (props) => {
  const { blockId, layout } = props;
  const { replaceTaskGroup } = useModel('task-group');

  const menu = useMemo(() => {
    const handleMenuClick: MenuProps['onClick'] = async (e) => {
      e.domEvent.stopPropagation();
      updateTaskBlock({ id: blockId, layout: e.key, returnGroup: true })
        .then((res) => {
          const hide = message.success('Updated! Reloading now...', 0);
          getTaskGroup({ id: res.group_id, with: 'taskBlocks' })
            .then((resGroup) => {
              replaceTaskGroup(resGroup);
            })
            .catch((reason) => Util.error(reason))
            .finally(() => hide());
        })
        .catch((reason) => Util.error(reason));
    };

    const menuItems: ItemType[] = [];
    TaskBlockLayoutOptions.forEach((x) => {
      menuItems.push({
        label: x.label,
        key: x.value,
        disabled: x.value == layout,
      });
    });
    return <Menu onClick={handleMenuClick} items={menuItems} />;
  }, [layout, blockId, replaceTaskGroup]);

  return (
    <Dropdown overlay={menu}>
      <span className="text-sm cursor-pointer">{layout ?? TaskBlockLayout.LAYOUT_C}</span>
    </Dropdown>
  );
};

export default PanelLayoutActions;
