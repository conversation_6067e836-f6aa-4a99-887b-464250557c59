import { getSysWorkflowSectionList } from '@/services/app/Sys/sys-workflow-section';
import { getSysWorkflowStepList } from '@/services/app/Sys/sys-workflow-step';
import Util, { sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import { Col, Modal, Row, Spin, Tabs } from 'antd';
import { isArray } from 'lodash';
import { Dispatch, SetStateAction, useEffect, useMemo, useRef, useState } from 'react';
import type { TabsProps } from 'antd';
import _ from 'lodash';
import { checkVisibleRule } from '@/pages/Order/OrderOutDetail/Workflow/WorkflowStepData';
import WorkflowStepDataItem from './WorkflowStepDataItem';
import TaskWorkflowStepLogList from './TaskWorkflowStepLogList';
import { ClockCircleOutlined, InfoOutlined } from '@ant-design/icons';

/**
 *
 * Get order out workflow step value including default value
 *
 * NOTE: step.task_workflow_steps should be queried on backend!!!
 *
 */
export const getTaskWorkflowStepValue = (step: API.SysWorkflowStep) => {
  const field_type = step.field_type;
  const definedSteps = step.task_workflow_steps;
  const values =
    isArray(definedSteps) && definedSteps.length
      ? step.task_workflow_steps?.map((x) => x.value)
      : step.default_value || [];
  const firstValue = values?.[0];

  let fieldValue = firstValue;
  if (field_type == 'switch') {
    fieldValue = fieldValue == '1' || fieldValue == 'true';
  } else if (field_type == 'multiselect' || field_type == 'checkbox') {
    fieldValue = isArray(values) ? values : [values];
  }
  return fieldValue;
};

type WorkflowStepDataProps = {
  workflow_id: number;
  task?: Partial<API.Task>;
  lastTab?: React.ReactElement | JSX.Element;
  workflowUpdateCallback: (values: any) => void;
};

const WorkflowStepData: React.FC<WorkflowStepDataProps> = (props) => {
  const { workflow_id, task, workflowUpdateCallback } = props;
  const task_id = sn(task?.id);

  const [refreshTick, setRefreshTick] = useState<number>(0); // for refreshing history table.

  const [loading, setLoading] = useState<boolean>(false);
  const [loadingSection, setLoadingSection] = useState<boolean>(false);
  const [sections, setSections] = useState<API.SysWorkflowSection[]>([]);
  const [steps, setSteps] = useState<API.SysWorkflowStep[]>([]);
  const formRef = useRef<ProFormInstance>();

  // history modal
  const [currentStepId, setCurrentStepId] = useState<number>();
  const [openHistoryModal, setOpenHistoryModal] = useState<boolean>(false);

  const stepsAll = useMemo(() => {
    return [...steps];
  }, [steps]);

  useEffect(() => {
    if (!task_id) return;
    if (!sections?.length) return;

    setLoading(true);
    getSysWorkflowStepList({
      pageSize: 500,
      with: 'taskWorkflowSteps,latestTaskWorkflowStepLog',
      task_id,
      workflow_id,
    })
      .then((res) => {
        setSteps(res.data ?? []);
        const initialValues = {};
        let existAnyWorkflowStepInfo = false;

        (res.data ?? []).forEach((step) => {
          const field_type = step.field_type;
          const definedSteps = step.task_workflow_steps;
          const values =
            isArray(definedSteps) && definedSteps.length
              ? step.task_workflow_steps?.map((x) => x.value)
              : step.default_value || [];
          const firstValue = values?.[0];

          let fieldValue = firstValue;
          if (field_type == 'switch') {
            fieldValue = fieldValue == '1' || fieldValue === true || fieldValue === 'true';
          } else if (field_type == 'multiselect' || field_type == 'checkbox') {
            fieldValue = isArray(values) ? values : [values];
          } else if (field_type == 'radio') {
          } else if (field_type == 'daterange') {
            initialValues[`${step.id}_2`] = definedSteps?.[0]?.value2;
          }
          initialValues[`${step.id}`] = fieldValue;

          if (!existAnyWorkflowStepInfo && isArray(definedSteps) && definedSteps.length) {
            existAnyWorkflowStepInfo = true;
          }
        });
        // setIsDefaultDataSet(existAnyWorkflowStepInfo);
        formRef.current?.setFieldsValue(initialValues);
      })
      .finally(() => setLoading(false));
  }, [task_id, workflow_id, sections]);

  useEffect(() => {
    if (!workflow_id || !task_id) return;

    setLoadingSection(true);
    getSysWorkflowSectionList({
      pageSize: 500,
      workflow_id,
    })
      .then((res) => {
        setSections(res.data ?? []);
      })
      .finally(() => setLoadingSection(false));
  }, [task_id, workflow_id]);

  const [tab, setTab] = useState<string>();

  /**
   * Tabs
   */
  const sectionItems: TabsProps['items'] = useMemo(() => {
    setTab(sections?.[0]?.parent_name);
    return sections
      .map((x) => x.parent_name)
      .filter(function (value, index, array) {
        return array.indexOf(value) == index;
      })
      .map((section) => ({
        label: section,
        key: `${section}`,
        children: (
          <>
            <Row gutter={36}>
              {sections
                .filter((x) => x.parent_name == section)
                .map((s) => (
                  <Col key={s.id} span={8}>
                    <h4>{s.name_alias}</h4>
                    {stepsAll
                      .filter(
                        (step) =>
                          step.section_id == s.id &&
                          checkVisibleRule(step, stepsAll, (depStep, filter) => {
                            const depValue = getTaskWorkflowStepValue(depStep);
                            return depValue;
                          }),
                      )
                      .map((step) => (
                        <Row
                          key={step.id}
                          gutter={8}
                          className="workflow label-h-auto"
                          wrap={false}
                        >
                          <Col span={step?.field_type !== 'divider' ? 23 : 24}>
                            <WorkflowStepDataItem
                              step={step}
                              task_id={sn(task_id)}
                              updateCallback={(res: any) => {
                                console.log('Update cb: ', res);
                                if ('value2' in res) {
                                  formRef.current?.setFieldsValue({
                                    [`${res.step_id}_2`]: res.value2,
                                  });
                                } else {
                                  formRef.current?.setFieldsValue({
                                    [`${res.step_id}`]: res.value,
                                  });
                                }

                                setSteps((prev) => {
                                  const newDS = [...prev];
                                  const row = _.find(newDS, {
                                    id: res.step_id,
                                  }) as API.SysWorkflowStep;
                                  if (row) {
                                    row.latest_task_workflow_step_log =
                                      res.step.latest_task_workflow_step_log;
                                    row.task_workflow_steps = res.step.task_workflow_steps;
                                  }
                                  return newDS;
                                });

                                workflowUpdateCallback(res);
                              }}
                            />
                          </Col>
                          {step?.field_type !== 'divider' && (
                            <Col span={1}>
                              {step?.latest_task_workflow_step_log && (
                                <>
                                  <ClockCircleOutlined
                                    className="text-sm c-grey"
                                    title={
                                      'View history\n' +
                                      (step?.latest_task_workflow_step_log
                                        ? Util.dtToDMYHHMM(
                                            step?.latest_task_workflow_step_log?.created_on,
                                          ) +
                                          ' ' +
                                          step?.latest_task_workflow_step_log?.user?.username
                                        : '')
                                    }
                                    onClick={() => {
                                      setOpenHistoryModal(true);
                                      setCurrentStepId(step?.id);
                                    }}
                                  />
                                </>
                              )}
                            </Col>
                          )}
                        </Row>
                      ))}
                  </Col>
                ))}
            </Row>
          </>
        ),
      }))
      .concat({ label: 'Basic / Comments', key: '_Basic', children: props.lastTab as JSX.Element });
  }, [sections, props.lastTab, stepsAll, task_id, workflowUpdateCallback]);

  return (
    <>
      <Spin spinning={loading || loadingSection} style={{ minHeight: 400 }}>
        <ProForm
          formRef={formRef}
          layout="horizontal"
          labelCol={{
            flex: '0 0 130px',
            style: { whiteSpace: 'pre-wrap', fontSize: 13, lineHeight: 1.3 },
          }}
          labelAlign="left"
          submitter={false}
          onValuesChange={(__) => {
            setRefreshTick((prev) => prev + 1);
          }}
        >
          <Tabs
            className="section-tabs"
            activeKey={tab}
            type="card"
            size={'small'}
            items={sectionItems}
            onTabClick={(key) => setTab(key)}
          />
        </ProForm>
      </Spin>
      <Modal
        width="700px"
        wrapProps={{ paddingTop: 50 }}
        open={openHistoryModal}
        onCancel={() => setOpenHistoryModal(false)}
        footer={false}
      >
        {currentStepId && task_id && (
          <TaskWorkflowStepLogList
            hidePageContainer
            task_id={task_id}
            step_id={currentStepId}
            open={openHistoryModal}
            refreshTick={refreshTick}
          />
        )}
      </Modal>
    </>
  );
};

export default WorkflowStepData;
