/* eslint-disable */
import { request, RequestConfig } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/sys/workflow-step';

/** rule GET /api/sys/workflow-step */
export async function getSysWorkflowStepList(
  params: API.PageParams & Partial<API.SysWorkflowStep> & { order_no?: number, task_id?: number, supplier_id?: number, ids?: number[], workflow_ids?: number[] } & { section_position?: number },
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.SysWorkflowStep>> {
  return request<API.Result<API.SysWorkflowStep>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}
/** post POST /api/sys/workflow-step */
export async function addSysWorkflowStep(
  workflowId: number,
  data: API.SysWorkflowStep | FormData,
  options?: { [key: string]: any },
): Promise<API.SysWorkflowStep> {
  const config: RequestConfig = {
    method: 'POST',
    data: {
      ...data,
      workflow_id: workflowId,
    },
    ...(options || {}),
  };
  return request<API.ResultObject<API.SysWorkflowStep>>(`${urlPrefix}`, config).then((res) => {
    return res.message;
  });
}

/** post PUT /api/sys/workflow-step/{workflowId} */
export async function updateSysWorkflowStep(
  id: number,
  data: API.SysWorkflowStep | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.SysWorkflowStep>>(`${urlPrefix}/${id}`, config).then(
    (res) => res.message,
  );
}

/** post PUT /api/sys/workflow-step/update-sort */
export async function updateSysWorkflowStepsSort(
  data: { sortInfo: any[]; workflow_id?: number },
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };
  return request<API.BaseResult>(`${urlPrefix}/update-sort`, config);
}

/** delete DELETE /api/sys/workflow-step/{id} */
export async function deleteSysWorkflowStep(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}
