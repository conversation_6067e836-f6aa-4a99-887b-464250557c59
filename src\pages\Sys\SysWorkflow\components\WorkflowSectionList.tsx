/* eslint-disable react/no-array-index-key */
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { DragSortTable } from '@ant-design/pro-table';
import { useCallback, useEffect, useRef, useState } from 'react';
import { But<PERSON>, message, Popconfirm, Space } from 'antd';
import { DeleteOutlined, MenuOutlined, PlusOutlined } from '@ant-design/icons';
import Util, { sn } from '@/util';
import {
  addSysWorkflowSection,
  deleteSysWorkflowSection,
  getSysWorkflowSectionList,
  updateSysWorkflowSection,
  updateSysWorkflowSectionsSort,
} from '@/services/app/Sys/sys-workflow-section';
import EditableCell from '@/components/EditableCell';
// import WorkflowSectionOptionsModalForm from './WorkflowSectionOptionsModalForm';
import _ from 'lodash';
// import Tag from 'antd/lib/tag';
// import { YNOptions, YNOptionsStr } from '@/constants';
// import WorkflowSectionOptionsCell from './WorkflowSectionOptionsCell';

type TableRowType = Omit<API.SysWorkflowSection, 'id'> & {
  id?: number | string;
};

type WorkflowSectionProps = {
  workflow_id: number;
};

const WorkflowSection: React.FC<WorkflowSectionProps> = ({ workflow_id }) => {
  const actionRef = useRef<ActionType>();
  const [dataSource, setDatasource] = useState<TableRowType[]>([]);

  // options modal form
  // const [openOptionsModal, setOpenOptionsModal] = useState<boolean>(false);
  // const [currentRow, setCurrentRow] = useState<TableRowType>();

  const columns: ProColumns<TableRowType>[] = [
    {
      title: '',
      dataIndex: 'position',
      align: 'center',
      width: 40,
    },
    {
      title: 'Name',
      dataIndex: 'name',
      width: 400,
      render: (dom, record) => (
        <EditableCell
          dataType="text"
          defaultValue={record.name}
          triggerUpdate={function (
            value: any,
            cancelEdit?: (() => void) | undefined,
          ): Promise<void> {
            return updateSysWorkflowSection(sn(record.id), { name: value }).then((res) =>
              actionRef.current?.reload(),
            );
          }}
        >
          {dom}
        </EditableCell>
      ),
    },
    /* {
      title: 'Options',
      dataIndex: 'options',
      tooltip: 'Drag & Drop sorting availabe',
      onCell: (record, index) => {
        if (
          record.field_type == 'text' ||
          record.field_type == 'textarea' ||
          record.field_type == 'switch' ||
          record.field_type == 'divider'
        ) {
          return {};
        }
        return {
          className: 'cursor-pointer',
          onClick: (e) => {
            setCurrentRow(record);
            setOpenOptionsModal(true);
          },
        };
      },
      render: (dom, record) => (
        <WorkflowSectionOptionsCell
          step={record as API.SysWorkflowSection}
          onSaveCallback={async (id, options) => {
            setDatasource((prev: TableRowType[]) => {
              const newDS = [...prev];
              const row = _.find(newDS, { id });
              if (row) {
                row.options = options || [];
              }
              return newDS;
            });
          }}
        />
      ),
    },
    {
      title: 'Default value',
      dataIndex: 'default_value',
      width: 200,
      render: (dom, record) => {
        if (record.field_type == 'divider') return undefined;

        let children = record.default_value;
        if (record.field_type == 'text' || record.field_type == 'textarea') {
          children = children?.[0];
        } else if (
          record.field_type == 'radio' ||
          record.field_type == 'switch' ||
          record.field_type == 'select'
        ) {
          const optionObj = _.find(record.options, { value: children?.[0] });
          children = optionObj ? <Tag>{optionObj.label}</Tag> : undefined;
        } else {
          children = (children || []).map((x: any, index: number) => (
            <Tag key={`${x}-${index}`}>{_.get(_.find(record.options, { value: x }), 'label')}</Tag>
          ));
        }

        let dataType = record.field_type;
        if (dataType == 'radio') dataType = 'select';
        else if (dataType == 'checkbox') dataType = 'multiselect';

        return (
          <EditableCell
            dataType={dataType as any}
            defaultValue={record.default_value}
            options={record.options}
            allowClear
            triggerUpdate={function (
              value: any,
              cancelEdit?: (() => void) | undefined,
            ): Promise<void> {
              let default_value = value ? (isArray(value) ? value : [value]) : value;
              if (record.field_type == 'switch') {
                default_value = value === true || value === false ? (value ? [1] : [0]) : [];
              }
              return updateSysWorkflowSection(sn(record.id), {
                default_value,
              }).then((res) => actionRef.current?.reload());
            }}
          >
            {children}
          </EditableCell>
        );
      },
    }, */
    {
      title: 'Position',
      dataIndex: 'position_2',
      align: 'center',
      width: 40,
      render(__, entity) {
        return entity.position;
      },
    },
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      align: 'center',
      className: 'c-grey',
    },
    {
      title: '',
      valueType: 'option',
      width: 50,
      render: (text, record, __, action) => (
        <Space>
          <Popconfirm
            key="delete"
            className="c-red"
            title={<>Are you sure you want to delete?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ width: 300 }}
            onConfirm={async (e) => {
              const hide = message.loading('Deleting...', 0);
              deleteSysWorkflowSection({
                id: record.id,
              })
                .then((res) => {
                  actionRef.current?.reload();
                })
                .catch((err) => {
                  Util.error(err);
                })
                .finally(() => hide());
            }}
          >
            <DeleteOutlined />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleDragSortEnd = async (newDataSource: any) => {
    const hide = message.loading('Update sort...', 0);
    const prevDS = [...dataSource];
    setDatasource(newDataSource);
    updateSysWorkflowSectionsSort({
      sortInfo: newDataSource.map((x: TableRowType, index: number) => ({
        id: x.id,
        position: index + 1,
      })),
    })
      .then((res) => {
        setDatasource(newDataSource.map((x: any, ind: number) => ({ ...x, position: ind + 1 })));
        // actionRef.current?.reload();
      })
      .catch((e) => {
        setDatasource(prevDS);
        Util.error(e);
      })
      .finally(() => hide());
  };

  const dragHandleRender = (rowData: any, idx: any) => (
    <>
      <MenuOutlined style={{ cursor: 'grab', color: '#999' }} />
    </>
  );

  const addNewRecord = async (isEnd?: boolean) => {
    if (!workflow_id) return;
    const hide = message.loading('Adding...', 0);
    const newRow: API.SysWorkflowSection = {
      workflow_id,
      name: 'New Section',
      position: isEnd ? dataSource.length + 1 : 1,
    };

    addSysWorkflowSection(sn(workflow_id), newRow)
      .then((res) => {
        setDatasource((prev) => (isEnd ? [...prev, res] : [res, ...prev]));
      })
      .catch((e) => Util.error(e))
      .finally(() => hide());
  };

  const loadStepsList = useCallback(
    (params?: any) => {
      if (!workflow_id) return Promise.resolve({});

      return getSysWorkflowSectionList({ ...params, pageSize: 500, workflow_id }).then((res) => {
        setDatasource(res.data);
        return res;
      });
    },
    [workflow_id],
  );

  useEffect(() => {
    loadStepsList();
  }, [loadStepsList]);

  return workflow_id ? (
    <>
      <DragSortTable
        headerTitle="Workflow sections"
        columns={columns}
        rowKey="id"
        actionRef={actionRef}
        search={false}
        size="small"
        pagination={false}
        dataSource={dataSource}
        dragSortKey="position"
        dragSortHandlerRender={dragHandleRender}
        onDragSortEnd={handleDragSortEnd}
        columnEmptyText=""
        toolBarRender={() => [
          <Button
            type="primary"
            key="newAfter"
            onClick={() => {
              addNewRecord(true);
            }}
            icon={<PlusOutlined />}
          >
            New
          </Button>,
        ]}
        request={(params) => {
          return loadStepsList(params);
        }}
      />
    </>
  ) : (
    <></>
  );
};
export default WorkflowSection;
