import type { Dispatch, SetStateAction } from 'react';
import { useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { Button, message, Modal, Space } from 'antd';
import Util, { sn } from '@/util';
import { DeleteOutlined, MenuOutlined, PlusOutlined, SaveOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { DragSortTable } from '@ant-design/pro-table';
import EditableCell from '@/components/EditableCell';
import { updateTaskBlockPartial } from '@/services/app/Task/task-block';
import { useModel } from 'umi';

export type TaskBlockSettingModalProps = {
  initialValues: Partial<API.TaskBlock>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSaveCallback?: (id?: number, data?: Partial<API.TaskBlock>) => Promise<void>;
};

const TaskBlockSettingModal: React.FC<TaskBlockSettingModalProps> = (props) => {
  const { initialValues, onSaveCallback } = props;
  const { taskGroups, blockInfoMap } = useModel('task-group');

  const actionRef = useRef<ActionType>();
  const [dataSource, setDataSource] = useState<API.TaskBlockSettingsPanelMenu[]>([]);

  useEffect(() => {
    if (props.modalVisible) {
      setDataSource(props.initialValues.settings?.panelMenus ?? []);
    }
  }, [props.modalVisible, props.initialValues]);

  const columns: ProColumns<API.TaskBlockSettingsPanelMenu>[] = [
    {
      title: '',
      dataIndex: 'position',
      align: 'center',
      width: 40,
    },
    {
      title: 'Label',
      dataIndex: 'label',
      width: 150,
      render: (dom, record) => {
        const prevDS = [...dataSource];
        const recordId = record.id;

        return (
          <EditableCell
            dataType="text"
            defaultValue={record.label}
            triggerUpdate={async function (
              value: any,
              cancelEdit?: (() => void) | undefined,
            ): Promise<void> {
              const newDataSource = [...prevDS];
              const tmp = newDataSource.find((x) => x.id == recordId);
              if (tmp) {
                tmp.label = value;
              }
              return Promise.resolve().then((res) => {
                setDataSource(newDataSource);
              });
            }}
            fieldProps={{ width: 'sm' }}
          >
            {dom}
          </EditableCell>
        );
      },
    },
    /* {
      title: 'Target Group',
      dataIndex: 'destGroupId',
      width: 400,
      render: (dom, record) => (
        <EditableCell
          dataType="select"
          defaultValue={record.destGroupId}
          triggerUpdate={function (
            value: any,
            cancelEdit?: (() => void) | undefined,
          ): Promise<void> {
            const newDataSource = [...dataSource];
            const tmp = newDataSource.find((x) => (x.id = record.id));
            if (tmp) {
              tmp.destGroupId = value;
            }
            return Promise.resolve().then((res) => {
              setDataSource(newDataSource);
            });
          }}
        >
          {dom}
        </EditableCell>
      ),
    }, */
    {
      title: 'Target Block',
      dataIndex: 'destBlockId',
      render: (dom, record) => {
        const prevDS = [...dataSource];
        const recordId = record.id;
        console.log('--> Cell render', recordId, prevDS);

        const newDom = blockInfoMap[sn(record.destBlockId)]?.name;

        return (
          <EditableCell
            dataType="select"
            defaultValue={sn(record.destBlockId)}
            triggerUpdate={async function (
              value: any,
              cancelEdit?: (() => void) | undefined,
            ): Promise<void> {
              const newDataSource = [...prevDS];
              const tmp = newDataSource.find((x) => x.id == recordId);
              console.log('founded: ', value, tmp, 'newDataSource', newDataSource);
              if (tmp) {
                tmp.destBlockId = sn(value);
              }
              setDataSource(newDataSource);
              return Promise.resolve();
            }}
            options={taskGroups?.map((x: API.TaskGroup) => ({
              value: sn(x.id),
              label: `${x.name} (${x.code})`,
              children: x.task_blocks?.map((b) => ({
                value: sn(b.id),
                label: `${x.code} / ${b.name}`,
              })),
            }))}
            fieldProps={{ dropdownMatchSelectWidth: false, width: 'md' }}
          >
            {newDom}
          </EditableCell>
        );
      },
    },
    {
      title: '',
      valueType: 'option',
      width: 50,
      render: (text, record, _, action) => (
        <DeleteOutlined
          onClick={(e) => {
            setDataSource((prev) => prev.filter((x) => x.id != record.id));
          }}
        />
      ),
    },
  ];

  const handleDragSortEnd = async (newDataSource: API.TaskBlockSettingsPanelMenu[]) => {
    setDataSource([...newDataSource].map((x, ind) => ({ ...x, position: ind + 1 })));
  };

  const dragHandleRender = (rowData: any, idx: any) => (
    <>
      <MenuOutlined style={{ cursor: 'grab', color: '#999' }} />
    </>
  );

  const addNewRecord = async (isEnd?: boolean) => {
    const hide = message.loading('Adding...', 0);
    const newRow: API.TaskBlockSettingsPanelMenu = {
      id: Util.genNewKey(),
      label: 'Block name',
      position: isEnd ? dataSource.length + 1 : 1,
      destBlockId: null,
    };

    setDataSource((prev) => (isEnd ? [...prev, newRow] : [newRow, ...prev]));

    hide();

    /* addSysWorkflowSection(sn(workflow_id), newRow)
      .then((res) => {
        setDataSource((prev) => (isEnd ? [...prev, res] : [res, ...prev]));
      })
      .catch((e) => Util.error(e))
      .finally(() => hide()); */
  };

  const handleSave = (close?: boolean) => {
    console.log(dataSource);
    const id = sn(initialValues.id);
    if (id) {
      const hide = message.loading('Saving...', 0);
      updateTaskBlockPartial({
        id,
        settings: { panelMenus: dataSource.map((x, ind) => ({ ...x, position: ind + 1 })) },
      })
        .then((res) => {
          if (close) {
            props.handleModalVisible(false);
          }
          onSaveCallback?.(res);
        })
        .finally(() => {
          hide();
        });
    }
  };

  console.log('render: ', dataSource);

  return (
    <Modal
      title={'Update shortcut menus'}
      width="600px"
      open={props.modalVisible}
      onCancel={() => props.handleModalVisible(false)}
      footer={
        <Space>
          <Button
            type="primary"
            onClick={() => {
              handleSave(false);
            }}
            icon={<SaveOutlined />}
          >
            Save
          </Button>
          <Button
            type="primary"
            onClick={() => {
              handleSave(true);
            }}
          >
            Save & Close
          </Button>
          <Button
            onClick={() => {
              props.handleModalVisible(false);
            }}
          >
            Cancel
          </Button>
        </Space>
      }
    >
      <DragSortTable
        headerTitle="Task block sections"
        columns={columns}
        rowKey="id"
        actionRef={actionRef}
        search={false}
        size="small"
        pagination={false}
        dataSource={dataSource}
        dragSortKey="position"
        dragSortHandlerRender={dragHandleRender}
        onDragSortEnd={handleDragSortEnd}
        columnEmptyText=""
        toolBarRender={() => [
          <Button
            type="primary"
            key="newAfter"
            onClick={() => {
              addNewRecord(true);
            }}
            icon={<PlusOutlined />}
          >
            New
          </Button>,
        ]}
      />
    </Modal>
  );
};

export default TaskBlockSettingModal;
