import type { Dispatch, SetStateAction } from 'react';
import { useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { Button, Col, message, Row, Divider } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import { ProFormText, ModalForm } from '@ant-design/pro-form';
import { updateTask, updateTaskPartial } from '@/services/app/Task/task';
import Util from '@/util';
import type { ActionType } from '@ant-design/pro-table';
import { useModel } from 'umi';
import _ from 'lodash';
import { TaskBlockLayout, TaskStatusOptions } from '@/constants';
import { CheckSquareFilled, SwitcherOutlined } from '@ant-design/icons';
import SubTaskList from './SubTaskList';
import type { onTaskClickType } from '..';
import TaskCommentList from './TaskCommentList';
import SProFormDateRange from '@/components/SProFormDateRange';
import EditableCell from '@/components/EditableCell';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...');

  try {
    const res = await updateTask(fields);
    hide();
    message.success('Update is successful');
    return res;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.Task>;

export type UpdateFormProps = {
  initialValues?: Partial<API.Task & { group_id?: number; block_id?: number; index?: number }>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Task) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  onTaskClick?: onTaskClickType;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { getBlockLayout } = useModel('task-group');

  const { initialValues: task, onTaskClick } = props;
  const { parent_id: parentId, group_id: groupId, block_id: droppableId, index } = task || {};

  const [refreshTick, setRefreshTick] = useState<number>(0);

  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  // we disable block Ids editing for now. Multiple comments below.
  // const [blockIds, setBlockIds] = useState<number[]>([]);

  useEffect(() => {
    if (props.modalVisible && props.initialValues) {
      if (formRef.current) {
        const newValues = { ...(props.initialValues || {}) };
        formRef.current.setFieldsValue(newValues);
      }

      if (props.initialValues?.id) {
        actionRef.current?.reload();
        setRefreshTick((prev) => prev + 1);
      }
    }
  }, [props.initialValues, props.modalVisible]);

  const blockLayout = getBlockLayout(props.initialValues?.block_id);

  return (
    <ModalForm
      title={
        parentId ? (
          <>
            <SwitcherOutlined className="c-blue4" /> Update Sub Task #{props.initialValues?.id} in
            &nbsp;
            <CheckSquareFilled className="c-blue4" /> #{parentId}
          </>
        ) : (
          <Row gutter={16} style={{ height: 32, alignItems: 'center' }} wrap={false}>
            <Col flex={'0 0 200px'}>
              <CheckSquareFilled className="c-blue4" /> Update Task #{props.initialValues?.id}
            </Col>
            <Col flex="auto" style={{ paddingRight: 30 }}>
              <EditableCell
                dataType="text"
                defaultValue={props.initialValues?.title}
                triggerUpdate={async function (
                  value: any,
                  cancelEdit?: (() => void) | undefined,
                ): Promise<void> {
                  return updateTaskPartial({ id: props.initialValues?.id, title: value }).then(
                    (res) => {
                      cancelEdit?.();
                      props.onSubmit?.({
                        ...(props.initialValues ?? {}),
                        id: res.id,
                        title: res.title,
                      });
                    },
                  );
                }}
                width={'md'}
                showEditIcon
              >
                {props.initialValues?.title}
              </EditableCell>
            </Col>
          </Row>
        )
      }
      width="800px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="vertical"
      labelAlign="left"
      initialValues={props.initialValues || {}}
      formRef={formRef}
      submitter={{
        render(__) {
          return [
            <Button key="cancel" onClick={() => props.handleModalVisible(false)}>
              {' '}
              Cancel
            </Button>,
            <Button key="submit" type="primary" onClick={() => formRef.current?.submit()}>
              Save
            </Button>,
          ];
        },
      }}
      onFinish={async (value) => {
        const res = await handleUpdate({
          ...value,
          id: props.initialValues?.id,
          block_id: [props.initialValues?.block_id],
        });

        if (res) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit({ ...res, block_id: props.initialValues?.block_id });
        }
      }}
    >
      <Row gutter={32}>
        <Col span={12}>
          <ProFormText
            required
            rules={[
              {
                required: true,
                message: 'Title is required',
              },
            ]}
            width="lg"
            name="title"
            label="Title"
          />
          {blockLayout == TaskBlockLayout.LAYOUT_T_DATE && (
            <>
              <SProFormDateRange
                label="Date"
                startDateName="date"
                endDateName="end_date"
                formRef={formRef}
              />
            </>
          )}
          <ProFormTextArea width="lg" name="desc" label="Description" />
          {task?.ref && (
            <>
              <Divider>{task?.ref_type}</Divider>
              <Row wrap={false} gutter={[16, 16]}>
                <Col span={6} className="bold">
                  Order No:
                </Col>
                <Col>{task?.ref?.order_no}</Col>
              </Row>
              <Row wrap={false}>
                <Col span={6} className="bold">
                  {task?.ref_type == 'OrderIn' ? 'Supplier' : 'Customer'}:
                </Col>
                <Col>
                  {task?.ref_type == 'OrderIn'
                    ? (task?.ref as API.OrderIn)?.supplier
                    : (task?.ref as API.OrderOut)?.customer}
                  :
                </Col>
              </Row>
              <Row wrap={false}>
                <Col span={6} className="bold">
                  {task?.ref_type == 'OrderIn' ? 'Description' : 'Description'}:
                </Col>
                <Col>
                  {task?.ref_type == 'OrderIn'
                    ? (task?.ref as API.OrderIn)?.desc
                    : (task?.ref as API.OrderOut)?.desc}
                </Col>
              </Row>
            </>
          )}

          {!task?.parent_id && (
            <>
              <Divider> </Divider>
              <SubTaskList
                tableTitle={'Sub tasks'}
                initialValues={task as any}
                onTaskClick={onTaskClick}
                groupId={groupId}
                droppableId={`${droppableId}`}
                index={index}
                refreshTick={refreshTick}
              />
            </>
          )}

          {parentId && (
            <ProFormSelect
              width="lg"
              name="status"
              label="Status"
              initialValue={0}
              options={TaskStatusOptions}
            />
          )}
        </Col>
        <Col span={12}>
          <TaskCommentList initialValues={props.initialValues} />
        </Col>
      </Row>
    </ModalForm>
  );
};

export default UpdateForm;
