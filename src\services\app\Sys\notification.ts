/* eslint-disable */
import { request } from 'umi';

const urlPrefix = '/api/sys/notification';

/** rule GET /api/sys/notification */
export async function getSysNotificationList(
  params: API.PageParams & Partial<API.SysNotification> & { is_date_valid?: number },
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.SysNotification>> {
  return request<API.Result<API.SysNotification>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: JSON.stringify(sort),
      filter_detail: JSON.stringify(filter),
    },
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** put PUT /api/sys/notification */
export async function updateSysNotification(
  data: Partial<API.SysNotification>,
  options?: { [key: string]: any },
) {
  return request<API.SysNotification>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/sys/notification */
export async function addSysNotification(
  data: API.SysNotification,
  options?: { [key: string]: any },
) {
  return request<API.SysNotification>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/sys/notification/{id} */
export async function deleteSysNotification(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}
