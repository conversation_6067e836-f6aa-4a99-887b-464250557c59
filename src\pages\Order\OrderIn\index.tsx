import { LinkOutlined, PlusOutlined, SyncOutlined } from '@ant-design/icons';
import { Button, message, Drawer, Row, Col } from 'antd';
import React, { useState, useRef } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import UpdateForm from './components/UpdateForm';

import Util, { urlFull } from '@/util';
import CreateForm from './components/CreateForm';
import { getOrderInList, deleteOrderIn, exportOrderInToTask } from '@/services/app/Order/order-in';
import { DEFAULT_PER_PAGE_PAGINATION, DictCode } from '@/constants';
import { useModel } from 'umi';

/**
 *  Delete node
 *
 * @param selectedRows
 */

export const handleRemove = async (selectedRows: API.OrderIn[]) => {
  const hide = message.loading('Deleting');
  if (!selectedRows) return true;

  try {
    await deleteOrderIn({
      id: selectedRows.map((row) => row.order_no),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

const OrderInList: React.FC = () => {
  const { getCodeValue } = useModel('app-settings');

  const [createModalVisible, handleModalVisible] = useState<boolean>(false);

  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.OrderIn>();
  const [selectedRowsState, setSelectedRows] = useState<API.OrderIn[]>([]);

  const columns: ProColumns<API.OrderIn>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
    },
    {
      title: 'Order No',
      dataIndex: 'order_no',
      sorter: true,
      render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow(entity);
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: 'Supplier',
      dataIndex: 'supplier',
      sorter: true,
      hideInForm: true,
    },
    {
      title: 'Description',
      dataIndex: 'desc',
      sorter: true,
      hideInForm: true,
      ellipsis: true,
    },
    {
      title: 'Lotus Notes ID',
      dataIndex: 'lotus_notes_id',
      sorter: true,
      hideInForm: true,
      ellipsis: true,
      render: (dom, record) =>
        record.lotus_notes_id ? (
          <Row wrap={false}>
            <Col flex={'auto'}>{record.lotus_notes_id}</Col>
            <Col flex="0 0 40px">
              <a
                href={(getCodeValue(DictCode.LOTUS_PATH) ?? '').replace(
                  '{lotusNotesId}',
                  record.lotus_notes_id,
                )}
                title="Open Lotus link"
              >
                L
              </a>
              <a
                href={urlFull('/order-out-detail/lotus/' + record.lotus_notes_id)}
                target="_blank"
                rel="noreferrer"
                title="Open order out detail"
                style={{ marginLeft: 8 }}
              >
                <LinkOutlined />
              </a>
            </Col>
          </Row>
        ) : undefined,
    },
    {
      title: 'Created on',
      sorter: true,
      dataIndex: 'created_on',
      valueType: 'dateTime',
      search: false,
      width: 150,
      className: 'c-grey text-sm',
      render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
    },
    {
      title: 'Updated on',
      sorter: true,
      dataIndex: 'updated_on',
      valueType: 'dateTime',
      search: false,
      width: 150,
      className: 'c-grey text-sm',
      render: (dom, record) => Util.dtToDMYHHMM(record.updated_on),
    },

    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 120,
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          Edit
        </a>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.OrderIn, API.PageParams>
        headerTitle={'Order In list'}
        actionRef={actionRef}
        rowKey="order_no"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={{
          labelWidth: 'auto',
          searchText: 'Search',
          span: 6,
          filterType: 'query',
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="new"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
          <Button
            type="default"
            key="export-task"
            onClick={() => {
              const hide = message.loading('Syncing Order In entries with tasks...', 0);
              exportOrderInToTask()
                .then((res) => {
                  if (res) {
                    message.success('Synced successfully.');
                  }
                })
                .finally(() => hide());
            }}
          >
            <SyncOutlined /> Sync with tasks
          </Button>,
        ]}
        pagination={{
          showSizeChanger: true,
          pageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        request={getOrderInList}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              chosen{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              OrderIn &nbsp;&nbsp;
            </div>
          }
        >
          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            Bacth deletion
          </Button>
          {/* <Button type="primary">batch approval</Button> */}
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.order_no && (
          <ProDescriptions<API.OrderIn>
            column={2}
            title={currentRow?.order_no}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.order_no,
            }}
            columns={columns as ProDescriptionsItemProps<API.OrderIn>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default OrderInList;
