import { Card } from 'antd';

type CustomerInfoProps = {
  orderOut: Partial<API.OrderOut>;
};

const CustomerInfo: React.FC<CustomerInfoProps> = (props) => {
  const { orderOut } = props;
  return (
    <>
      <Card title="Notes of customer" bordered={false}>
        <div
          dangerouslySetInnerHTML={{
            __html: orderOut?.customer_obj?.notes || '',
          }}
        />
      </Card>
    </>
  );
};

export default CustomerInfo;
