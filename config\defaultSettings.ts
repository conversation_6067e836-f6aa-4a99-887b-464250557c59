import { Settings as LayoutSettings } from '@ant-design/pro-layout';

const Settings: LayoutSettings & {
  pwa?: boolean;
  logo?: string;
} = {
  navTheme: process.env.NODE_ENV === 'development' ? 'dark' : 'light',
  primaryColor: '#1890ff',
  layout: 'side',
  contentWidth: 'Fluid',
  fixedHeader: false,
  fixSiderbar: true,
  colorWeak: false,
  title: 'WHC Task',
  pwa: false,
  headerHeight: 48,
  splitMenus: false,
  iconfontUrl: '',
};

export default Settings;
