.orderOut {
  :global {
    .workflows {
      .workflow {
        font-size: 12px;
        label,
        .ant-input {
          font-size: 12px;
        }
        .ant-form-item-label > label {
          height: 24px;
        }
        .ant-form-item-control-input {
          min-height: 24px;
        }
        .ant-form-item-label > label,
        .ant-form-item-control {
          font-size: 12px;
          .ant-select,
          .ant-checkbox-group {
            font-size: 12px;
          }
        }
      }
    }
    .files-tab-wrap {
      .ant-upload-drag-icon {
        display: none;
      }
      .hor-dragger-wrap .ant-form-item-control-input-content > span {
        display: flex;
        width: 100%;
        max-width: 100%;
        .ant-upload-list-item {
          margin-top: 0;
          font-size: 12px;
        }
        .ant-upload-drag {
          flex: 0 0 200px;
          margin-right: 16px;
        }
        .ant-upload-list-text {
          flex: 0;
          width: calc(100% - 216px);
        }
      }
    }
  }
}
