import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormDigit } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { addSysTextModule } from '@/services/app/Sys/text-module';
import { message } from 'antd';
import Util from '@/util';

const handleAdd = async (fields: API.SysTextModule) => {
  const hide = message.loading('Adding...', 0);
  const data = { ...fields };
  try {
    await addSysTextModule(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type CreateFormProps = {
  values?: Partial<API.SysTextModule>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.SysTextModule) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={'New Text Module'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 7 }}
      wrapperCol={{ span: 16 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.SysTextModule);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormDigit
        required
        rules={[
          {
            required: true,
            message: 'Number is required',
          },
        ]}
        width="md"
        name="number"
        label="Number"
      />
      <ProFormTextArea width="xl" name="text" label="Text" />
    </ModalForm>
  );
};

export default CreateForm;
