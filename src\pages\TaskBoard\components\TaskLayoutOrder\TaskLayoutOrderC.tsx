import Util from '@/util';
import { Col, Row, Tag, Typography } from 'antd';
import TaskActions from '../Sub/TaskActions';
import type { TaskProps } from '../Task';

type TaskLayoutOrderCProps = TaskProps;

const TaskLayoutOrderC: React.FC<TaskLayoutOrderCProps> = (props) => {
  const { task } = props;

  const ref = task?.ref as API.OrderIn | API.OrderOut;

  return (
    <>
      <Row wrap={false} gutter={16} className="ant-row-middle">
        <Col flex={'0 0 120px'}>{ref && <Tag color="lime">{ref?.order_no ?? '-'}</Tag>}</Col>
        <Col flex={'auto'}>
          <Typography.Paragraph ellipsis={true} style={{ marginBottom: 0 }}>
            {task?.ref_type == 'OrderIn' ? '' : ''}
            {task?.ref_type == 'OrderIn'
              ? (ref as API.OrderIn)?.supplier
              : (ref as API.OrderOut)?.customer}
          </Typography.Paragraph>
        </Col>
        <Col flex={'0 0 100px'} className="text-right">
          <span className="text-sm c-grey">{Util.dtToDMY(ref?.created_on)}</span>
        </Col>
        <TaskActions {...props} />
      </Row>
      <Row wrap={false} className="ant-row-middle">
        <Col flex="auto">
          <Typography.Paragraph className="text-sm c-grey m-0" ellipsis={{ rows: 1 }}>
            {ref?.desc}
          </Typography.Paragraph>
        </Col>
        <Col flex={'0 0 50px'} className="text-right">
          <span className="text-sm c-grey">#{task.id}</span>
        </Col>
      </Row>
    </>
  );
};

export default TaskLayoutOrderC;
