import { XlsImportableDBTable } from '@/constants';
import { getOrderOutXlsDetailLocalAggregation } from '@/services/app/Order/order-out-detail';
import { getSysXlsColNameMapByProfile } from '@/services/app/Sys/sys-xls-col-name-map';
import Util from '@/util';
import ProDescriptions from '@ant-design/pro-descriptions';
import { Card } from 'antd';
import _ from 'lodash';
import { useCallback, useEffect, useMemo, useState } from 'react';

export const AggregationColumns = ['net_weight', 'qty', 'box_qty', 'pal', 'ek', 'vk'];

/**
 * Load OrderOut's XLS Local file profile imported
 * Load aggregation results from imported data. *
 *
 * @param orderOut
 * @returns
 */
const useXlsFileLocal = (orderOut?: Partial<API.OrderOut>) => {
  const [dbField2NamesList, setDbField2NamesList] = useState<API.SysXlsColNameMap[]>([]);
  const [aggregation, setAggregation] = useState<API.OrderOutDetail>();

  const loadDbField2NamesList = useCallback(() => {
    getSysXlsColNameMapByProfile({ table_name: XlsImportableDBTable.order_out_xls_detail_local })
      .then((res) => {
        setDbField2NamesList(res);
      })
      .catch((e) => Util.error(e));
  }, []);

  const loadAggregation = useCallback<() => Promise<API.OrderOutDetail | undefined>>(async () => {
    if (!orderOut?.detail_file_id || !orderOut.order_no) {
      setAggregation(undefined);
      return Promise.resolve(undefined);
    }
    const res = await getOrderOutXlsDetailLocalAggregation({ order_no: orderOut?.order_no });
    setAggregation(res);
    return res;
  }, [orderOut?.order_no, orderOut?.detail_file_id]);

  useEffect(() => {
    if (orderOut?.order_no) loadDbField2NamesList();
  }, [loadDbField2NamesList, orderOut?.order_no]);

  useEffect(() => {
    loadAggregation();
  }, [loadAggregation]);

  /**
   * Rendered component
   */
  const aggregationElement = useMemo(() => {
    return aggregation ? (
      <>
        <Card
          style={{
            marginTop: 0,
            marginBottom: 12,
            background: '#d7e7f6',
            border: '1px solid #b8d7f4',
            borderRadius: 2,
          }}
          bodyStyle={{ padding: '4px 16px' }}
          bordered={false}
        >
          <ProDescriptions column={AggregationColumns.length} size="small">
            {AggregationColumns.map((column) => {
              const fieldDef = _.find(dbField2NamesList, { db_field: column });
              return fieldDef ? (
                <ProDescriptions.Item
                  key={column}
                  label={fieldDef.db_field_comment ?? fieldDef.db_field}
                  labelStyle={{ fontSize: '80%', width: 100 }}
                  contentStyle={{ width: 100 }}
                  style={{ padding: 0 }}
                >
                  <b className="text-sm">
                    {Util.numberFormat((aggregation as any)?.[column], false, 2, true)}
                  </b>
                </ProDescriptions.Item>
              ) : undefined;
            })}
          </ProDescriptions>
        </Card>
      </>
    ) : null;
  }, [aggregation, dbField2NamesList]);

  return {
    dbField2NamesList,
    setDbField2NamesList,
    aggregation,
    setAggregation,

    loadDbField2NamesList,
    loadAggregation,
    aggregationElement,
  };
};
export default useXlsFileLocal;
