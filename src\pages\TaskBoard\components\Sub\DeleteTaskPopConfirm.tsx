import { DeleteOutlined } from '@ant-design/icons';
import { Popconfirm } from 'antd';
import { TaskClickAction } from '../..';
import type { TaskProps } from '../Task';

type DeleteTaskPopConfirmProps = TaskProps & { title?: string; showIcon?: boolean };

const DeleteTaskPopConfirm: React.FC<DeleteTaskPopConfirmProps> = ({
  onTaskClick,
  droppableId,
  task,
  index,
  showIcon,
  title,
}) => {
  return (
    <Popconfirm
      title={<>Are you sure you want to delete?</>}
      okText="Yes"
      cancelText="No"
      overlayStyle={{ width: 300 }}
      onConfirm={async (e) => {
        if (onTaskClick) {
          onTaskClick(task, droppableId, index, e, TaskClickAction.delete);
        }
      }}
    >
      {showIcon && <DeleteOutlined className="c-red" />}
      {title}
    </Popconfirm>
  );
};

export default DeleteTaskPopConfirm;
