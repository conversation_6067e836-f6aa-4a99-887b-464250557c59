/* eslint-disable */
import { DateRangeType } from '@/util';
import { DropResult } from 'react-beautiful-dnd';
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/task/task';

/** task GET /api/task/task */
export async function getTaskList(
  params: Partial<
    API.PageParams &
    API.Task & { group_id?: number; block_id?: number } & {
      filters?: {
        status?: number;
        keyword?: string;
        keywordHead?: string | number;
        nUserIds?: (number | string)[];
        userIds?: (number | string)[];
      };
    }
  >,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.Task>> {
  return request<API.Result<API.Task>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** task GET /api/task/email-tasks */
export async function getEmailTaskList(
  params: Partial<
    API.PageParams &
    API.Task & { email_id?: number; task_ids_not?: number[]; email_id_not?: number } & {
      filters?: { status?: number; keyword?: string };
    }
  >,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.Task>> {
  return request<API.Result<API.Task>>(`/api/task/email-tasks`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

export type CalendarTaskDataType = {
  taskByDateKey: Record<string, API.Task[]>;
  taskInOrders: API.Task[];
  taskGeneral: API.Task[];
};

/** task GET /api/task/calendar-tasks */
export async function getCalendarTaskList(params: Partial<API.Task & { dtRange?: DateRangeType }>): Promise<CalendarTaskDataType> {
  return request<API.ResultObject<CalendarTaskDataType>>(`/api/task/calendar-tasks`, {
    method: 'GET',
    params,
    paramsSerializer,
    withToken: true,
  }).then((res) => res.message);
}

/** task GET /api/task/calendar-tasks/unassigned */
export async function getUnassignedCalendarTaskList(
  params: Partial<
    API.PageParams &
    API.Task & {
      filters?: { status?: number; keyword?: string };
    }
  >,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.Task>> {
  return request<API.Result<API.Task>>(`/api/task/calendar-tasks/unassigned`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** task GET /api/task/task/{id} */
export async function getTask(id?: number, params?: any) {
  return request<API.ResultObject<API.Task>>(`${urlPrefix}/${id}`, {
    method: 'GET',
    params: {
      ...params,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => res.message);
}

/** put PUT /api/task/task */
export async function updateTask(data: Partial<API.Task>, options?: { [key: string]: any }) {
  return request<API.ResultObject<API.Task>>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
    paramsSerializer,
  }).then((res) => res.message);
}

/** put PUT /api/task/task/update-partial/{id} */
export async function updateTaskPartial(data: Partial<API.Task>, options?: { [key: string]: any }) {
  return request<API.ResultObject<API.Task>>(`${urlPrefix}/update-partial/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
    paramsSerializer,
  }).then((res) => res.message);
}

/** put PUT /api/task/task/moveTo/{id} */
export async function moveTaskTo(id?: number, data?: Partial<API.Task>, options?: { [key: string]: any }) {
  return request<API.ResultObject<API.Task>>(`${urlPrefix}/moveTo/` + id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
    paramsSerializer,
  }).then((res) => res.message);
}



/** post POST /api/task/task */
export async function addTask(data: API.Task, options?: { [key: string]: any }) {
  return request<API.ResultObject<API.Task>>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** delete DELETE /api/task/task/{id} */
export async function deleteTask(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** put PUT /api/task/task/update-sort */
export async function updateTaskOrders(
  sourceId: number,
  targetId: number,
  dropResult: DropResult,
  group_id?: number,
) {
  if (sourceId == targetId) return;
  return request<API.Task>(`${urlPrefix}/update-sort`, {
    method: 'PUT',
    data: {
      sourceId,
      targetId,
      dropResult,
      group_id,
    },
  });
}

/** put PUT /api/task/task/assign-order-out-head/{id} */
export async function assignOrderOutHead(
  data: Partial<API.Task>,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject<API.Task>>(`${urlPrefix}/assign-order-out-head/` + data.id, {
    method: 'PUT',
    data: data,
    paramsSerializer,
  }).then((res) => res.message);
}

/** POST /api/task/task/create-by-email */
export async function addTaskByEmail(
  data: API.Task & { block_ids?: number[]; email_id?: number },
  options?: { [key: string]: any },
) {
  return request<API.ResultObject<API.Task>>(`${urlPrefix}/create-by-email`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** PUT /api/task/task/link-email/{emailId} */
export async function linkEmailToTask(
  emailId?: number,
  data?: { task_ids?: number[] },
  options?: { [key: string]: any },
) {
  return request<API.ResultObject<API.Email>>(`${urlPrefix}/link-email/${emailId}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}
/** PUT /api/task/task/unlink-email/{emailId} */
export async function unlinkEmailToTask(
  emailId?: number,
  data?: { task_ids?: number[] },
  options?: { [key: string]: any },
) {
  return request<API.ResultObject<API.Email>>(`${urlPrefix}/unlink-email/${emailId}`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** create assigners POST /api/task/{id}/assigners */
export async function createAssigners(
  id?: number,
  userIds?: number[] | string | number,
): Promise<API.Task> {
  return request<API.ResultObject<API.Task>>(`/api/task/${id}/assigners`, {
    method: 'POST',
    data: {
      userIds,
    },
    paramsSerializer,
  }).then((res) => res.message);
}

/** delete assigners DELETE /api/task/{id}/assigners */
export async function deleteAssigners(
  id?: number,
  userIds?: number[] | string | number,
): Promise<API.Task> {
  return request<API.ResultObject<API.Task>>(`/api/task/${id}/assigners`, {
    method: 'DELETE',
    data: {
      userIds,
    },
    paramsSerializer,
  }).then((res) => res.message);
}


/** PUT /api/task/task/sendToSubGroup/{emailId} */
export async function sendTaskToSubGroup(
  srcTaskId: number,
  destTaskId: number,
  srcBlockId: number,
  data?: any,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject<API.Email>>(`${urlPrefix}/sendToSubGroup/${srcTaskId}/${destTaskId}`, {
    method: 'PUT',
    data: { ...data, srcBlockId },
    ...(options || {}),
  }).then((res) => res.message);
}

/** PUT /api/task/task/sendToMasterGroup/{emailId} */
export async function sendToMasterGroup(
  srcTaskId: number,
  destTaskId: number,
  srcBlockId: number,
  destBlockId: number,
  data?: any,
  options?: { [key: string]: any },
) {
  return request<API.ResultObject<API.Email>>(`${urlPrefix}/sendToMasterGroup/${srcTaskId}/${destTaskId}`, {
    method: 'PUT',
    data: { ...data, srcBlockId, destBlockId },
    ...(options || {}),
  }).then((res) => res.message);
}
