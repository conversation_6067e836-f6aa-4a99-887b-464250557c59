/* eslint-disable */
import { RequestConfig, request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/fin/sinfo';

/** GET /api/fin/sinfo 
 * 
 * Get fin sinfo all list  
*/
export async function getFinSinfoList(
    params: API.PageParams & Partial<API.FinSinfo>,
    sort?: any,
    filter?: any,
): Promise<API.PaginatedResult<API.FinSinfo>> {
    return request<API.BaseResult>(`${urlPrefix}`, {
        method: 'GET',
        params: {
            ...params,
            perPage: params.pageSize,
            page: params.current,
            sort,
            filter,
        },
        paramsSerializer,
        withToken: true,
    }).then((res) => ({
        data: res.message.data,
        success: res.status == 'success',
        total: res.message.pagination.totalRows,
    }));
}

/** 
 * POST /api/fin/import 
 * 
 */
export async function importFinSinfo(
    data?: FormData,
    options?: { [key: string]: any },
) {
    const config: RequestConfig = {
        method: 'POST',
        ...(options || {}),
    };

    config.body = data;

    return request<any>(`${urlPrefix}/import`, config);
}