import { LinkOutlined, PlusOutlined, SyncOutlined } from '@ant-design/icons';
import { Button, message, Drawer, Row, Col, Card } from 'antd';
import React, { useState, useRef } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType, ProColumnType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import UpdateForm from '../OrderIn/components/UpdateForm';

import Util, { sn, urlFull } from '@/util';
import CreateForm from '../OrderIn/components/CreateForm';
import { getOrderInList, exportOrderInToTask } from '@/services/app/Order/order-in';
import { DEFAULT_PER_PAGE_PAGINATION, DictCode } from '@/constants';
import { useModel } from 'umi';
import { handleRemove } from '../OrderIn';
import {
  ProForm,
  ProFormSelect,
  type ProFormInstance,
  ProFormText,
  ProFormGroup,
  ProFormRadio,
} from '@ant-design/pro-form';
import { useOrderInSupplierFieldOptions } from '@/hooks/useOrderInSupplierFieldOptions';
import { FileCategorySuffixOptions } from '@/pages/FolderViewer/MiscFileBrowser';
import SFileIcon from '@/components/SFileIcon';

const OrderInDocumentList: React.FC = () => {
  const { getCodeValue } = useModel('app-settings');

  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();

  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [showDetail, setShowDetail] = useState<boolean>(false);

  const [currentRow, setCurrentRow] = useState<API.OrderIn>();
  const [selectedRowsState, setSelectedRows] = useState<API.OrderIn[]>([]);

  const { supplierOptions, searchSupplierOptions } = useOrderInSupplierFieldOptions();

  const columns: ProColumns<API.OrderIn>[] = [
    {
      title: 'Order No',
      dataIndex: 'order_no',
      sorter: true,
      defaultSortOrder: 'descend',
      width: 100,
      render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow(entity);
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: 'Supplier',
      dataIndex: 'supplier',
      sorter: true,
      hideInForm: true,
      width: 150,
    },
    {
      title: 'Description',
      dataIndex: 'desc',
      ellipsis: true,
    },
    {
      title: 'Lotus Notes ID',
      dataIndex: 'lotus_notes_id',
      sorter: false,
      hideInForm: true,
      ellipsis: true,
      width: 50,
      render: (dom, record) =>
        record.lotus_notes_id ? (
          <Row wrap={false}>
            {/* <Col flex={'auto'}>{record.lotus_notes_id}</Col> */}
            <Col flex="0 0 40px">
              <a
                href={(getCodeValue(DictCode.LOTUS_PATH) ?? '').replace(
                  '{lotusNotesId}',
                  record.lotus_notes_id,
                )}
                title="Open Lotus link"
              >
                L
              </a>
              <a
                href={urlFull('/order-out-detail/lotus/' + record.lotus_notes_id)}
                target="_blank"
                rel="noreferrer"
                title="Open order out detail"
                style={{ marginLeft: 8 }}
              >
                <LinkOutlined />
              </a>
            </Col>
          </Row>
        ) : undefined,
    },
    {
      title: 'Created on',
      sorter: true,
      dataIndex: 'created_on',
      valueType: 'dateTime',
      search: false,
      width: 150,
      className: 'c-grey text-sm',
      render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
    },
    {
      title: 'Updated on',
      sorter: true,
      dataIndex: 'updated_on',
      valueType: 'dateTime',
      search: false,
      width: 150,
      className: 'c-grey text-sm',
      render: (dom, record) => Util.dtToDMYHHMM(record.updated_on),
    },

    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 60,
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          Edit
        </a>,
      ],
    },
    ...FileCategorySuffixOptions.map((x, ind) => {
      return {
        title: x.label,
        dataIndex: `scan_file_${x.value}`,
        className: `${ind === 0 ? 'bl2' : ''}`,
        width: 70,
        render: (dom: any, record: API.OrderIn) => {
          const files = record.scan_files?.filter(
            (file) => file.category === 'order-in-' + x.value,
          );

          return (
            <Row gutter={4}>
              {files?.map((file) => {
                return (
                  <Col key={file.id}>
                    <a
                      href={file.url}
                      target="_blank"
                      rel="noreferrer"
                      title={file.clean_file_name}
                    >
                      <SFileIcon fileName={file.clean_file_name} />
                    </a>
                  </Col>
                );
              })}
            </Row>
          );
        },
      } as ProColumnType<API.OrderIn>;
    }),
  ];

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }} bodyStyle={{ paddingBottom: 12 }}>
        <ProForm<API.OrderIn>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_order_in_doc_list', {})}
          submitter={{
            submitButtonProps: {
              htmlType: 'submit',
            },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => actionRef.current?.reload(),
            render: (form, dom) => {
              return [...dom];
            },
          }}
        >
          <ProFormText name={'order_no'} label="Order No" width={'xs'} placeholder={'Order No'} />
          <ProFormSelect
            showSearch
            placeholder="Select a supplier"
            options={supplierOptions}
            request={async (params) => {
              return searchSupplierOptions(params) as any;
            }}
            width="sm"
            name="eqSupplier"
            label="Supplier"
            fieldProps={{
              dropdownMatchSelectWidth: false,
              onChange(value) {
                actionRef.current?.reload();
              },
            }}
          />
          <ProFormGroup size={'small'}>
            {FileCategorySuffixOptions.map((x, ind) => {
              return (
                <ProFormRadio.Group
                  key={x.value}
                  name={'exist_file_order-in-' + x.value}
                  label={x.label}
                  fieldProps={{
                    onChange: () => actionRef.current?.reload(),
                    buttonStyle: 'outline',
                    optionType: 'button',
                    size: 'small',
                  }}
                  initialValue={''}
                  options={[
                    { value: 'yes', label: 'Yes' },
                    { value: 'no', label: 'No' },
                    { value: '', label: 'Any' },
                  ]}
                />
              );
            })}
          </ProFormGroup>
        </ProForm>
      </Card>

      <ProTable<API.OrderIn, API.PageParams>
        headerTitle={'Order In Documents List'}
        actionRef={actionRef}
        rowKey="order_no"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        size="small"
        toolBarRender={() => [
          <Button
            type="primary"
            key="new"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
          <Button
            type="default"
            key="export-task"
            onClick={() => {
              const hide = message.loading('Syncing Order In entries with tasks...', 0);
              exportOrderInToTask()
                .then((res) => {
                  if (res) {
                    message.success('Synced successfully.');
                  }
                })
                .finally(() => hide());
            }}
          >
            <SyncOutlined /> Sync with tasks
          </Button>,
        ]}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(
            Util.getSfValues('sf_order_in_doc_list_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION,
          ),
        }}
        params={{
          with: 'scanFiles',
        }}
        request={async (params, sort, filter) => {
          const sfValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_order_in_doc_list', sfValues);
          Util.setSfValues('sf_order_in_doc_list_p', params);

          const newParams = { ...params, ...sfValues };

          return getOrderInList(newParams, sort, filter);
        }}
        tableAlertRender={false}
        onRequestError={Util.error}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        columnEmptyText=""
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              chosen{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              OrderIn &nbsp;&nbsp;
            </div>
          }
        >
          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            Batch deletion
          </Button>
          {/* <Button type="primary">batch approval</Button> */}
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.order_no && (
          <ProDescriptions<API.OrderIn>
            column={2}
            title={currentRow?.order_no}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.order_no,
            }}
            columns={columns as ProDescriptionsItemProps<API.OrderIn>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default OrderInDocumentList;
