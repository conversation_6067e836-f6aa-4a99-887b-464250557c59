import { Col, Row, Typography } from 'antd';
import TaskActions from '../Sub/TaskActions';
import type { TaskProps } from '../Task';

type TaskLayoutCProps = TaskProps;

const TaskLayoutC: React.FC<TaskLayoutCProps> = (props) => {
  const { task } = props;
  return (
    <>
      <Row wrap={false} gutter={16}>
        <Col flex={'auto'}>
          <Typography.Paragraph ellipsis={true} style={{ marginBottom: 8 }}>
            {task.title}
          </Typography.Paragraph>
        </Col>
        <TaskActions {...props} />
      </Row>
      <Row wrap={false} className="ant-row-middle">
        <Col flex="auto">
          <Typography.Paragraph className="text-sm c-grey m-0" ellipsis={{ rows: 1 }}>
            {task.desc}
          </Typography.Paragraph>
        </Col>
        <Col flex={'0 0 50px'} className="text-right">
          <span className="text-sm c-grey">#{task.id}</span>
        </Col>
      </Row>
    </>
  );
};

export default TaskLayoutC;
