import { EditOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, message, Drawer } from 'antd';
import React, { useState, useRef } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import UpdateForm from './components/UpdateForm';

import Util from '@/util';
import CreateForm from './components/CreateForm';
import {
  getSysAddressXlsTplList,
  deleteSysAddressXlsTpl,
} from '@/services/app/Sys/sys-address-xls-tpl';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import XlsTplDetails from './components/XlsTplDetails';

/**
 *  Delete node
 *
 * @param selectedRows
 */
const handleRemove = async (selectedRows: API.SysAddressXlsTpl[]) => {
  const hide = message.loading('Deleting');
  if (!selectedRows) return true;

  try {
    await deleteSysAddressXlsTpl({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error('Delete failed, please try again!', error);
    return false;
  }
};

const SysAddressXlsTplList: React.FC = () => {
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [openDetailsModal, setOpenDetailsModal] = useState<boolean>(false);

  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.SysAddressXlsTpl>();
  const [selectedRowsState, setSelectedRows] = useState<API.SysAddressXlsTpl[]>([]);

  const columns: ProColumns<API.SysAddressXlsTpl>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 40,
      align: 'center',
      fixed: 'left',
      render: (item, record, index, action) => {
        return (
          ((action?.pageInfo?.current ?? 1) - 1) *
            (action?.pageInfo?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION) +
          index +
          1
        );
      },
    },
    {
      title: 'Name',
      dataIndex: 'name',
      sorter: true,
      ellipsis: true,
      width: 150,
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            setOpenDetailsModal(true);
            setCurrentRow(record);
          }}
        >
          {record.name}
        </a>,
      ],
    },
    {
      title: 'File',
      dataIndex: 'file_path',
      sorter: true,
      ellipsis: true,
      render: (_, record) => [
        <a
          key="file_path"
          onClick={() => {
            window.open(`${API_URL}/uploads${record.file_path}`, '_blank');
          }}
        >
          {record.file_name}
        </a>,
      ],
    },
    {
      title: 'Created On',
      dataIndex: 'created_on',
      sorter: true,
      ellipsis: true,
      defaultSortOrder: 'descend',
      className: 'text-sm c-grey',
      width: 150,
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 120,
      fixed: 'right',
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          <EditOutlined />
        </a>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.SysAddressXlsTpl, API.PageParams>
        headerTitle={'Address XLS templates'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        scroll={{ x: 800 }}
        search={{
          labelWidth: 'auto',
          searchText: 'Search',
          span: 6,
          filterType: 'query',
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        pagination={{
          showSizeChanger: true,
          pageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        request={getSysAddressXlsTplList}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              chosen{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              SysAddressXlsTpl &nbsp;&nbsp;
            </div>
          }
        >
          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            Bacth deletion
          </Button>
          {/* <Button type="primary">batch approval</Button> */}
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);
          actionRef.current?.reload();
          handleUpdateModalVisible(false);
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
      />

      <XlsTplDetails
        modalVisible={openDetailsModal}
        handleModalVisible={setOpenDetailsModal}
        tpl={currentRow}
      />

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions<API.SysAddressXlsTpl>
            column={2}
            title={currentRow?.id}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<API.SysAddressXlsTpl>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default SysAddressXlsTplList;
