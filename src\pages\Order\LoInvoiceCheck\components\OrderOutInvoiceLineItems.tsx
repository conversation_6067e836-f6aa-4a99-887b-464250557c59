/* eslint-disable @typescript-eslint/dot-notation */
import Util, { sn } from '@/util';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card } from 'antd';
import { useCallback } from 'react';
import React, { useEffect } from 'react';
import { useRef, useState } from 'react';
import _ from 'lodash';
import useXlsFileLocal from '../../OrderOutDetail/FileDetail/useXlsFileLocal';
import { getOrderOut } from '@/services/app/Order/order-out';
import type { EditableRowType } from './ExportToStatModal';

type OrderOutItemEditableFormType = Partial<API.OrderOutItem> & {
  uid?: React.Key;
};

type OrderOutInvoiceLineItemsProps = {
  orderNo: number;
  loStatus?: string;
  addToPreview: (data: EditableRowType) => void;
};

const OrderOutInvoiceLineItems: React.FC<OrderOutInvoiceLineItemsProps> = (props) => {
  const { orderNo, loStatus } = props;

  const [loading] = useState(false);
  const [orderOut, setOrderOut] = useState<API.OrderOut>();

  // Editable table form
  const actionRef = useRef<ActionType>();
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>(() => []);
  const [dataSource, setDataSource] = useState<OrderOutItemEditableFormType[]>(() => []);

  const loadOrderOut = useCallback(async (no?: number, params?: any) => {
    const id = no ?? params?.lotus_notes_id;
    if (!id) {
      setOrderOut(undefined);
      return Promise.resolve(undefined);
    }

    return getOrderOut(id, {
      with: 'orderOutItems',
      ...params,
    })
      .then((res) => {
        setOrderOut(res);
      })
      .finally(() => {
        //
      });
  }, []);

  useEffect(() => {
    loadOrderOut(orderNo);
  }, [loadOrderOut, orderNo]);

  // custom hooks
  const { aggregationElement } = useXlsFileLocal(orderOut);
  const isVoided = loStatus == 'voided';

  const columns: ProColumns<OrderOutItemEditableFormType>[] = [
    {
      title: 'Qty',
      dataIndex: ['qty'],
      width: 100,
      fieldProps: {
        placeholder: 'Qty',
      },
      onCell: (record, index) => {
        if (record.parent_id) {
          return { colSpan: 0 };
        }
        return {};
      },
    },
    {
      title: 'Unit',
      dataIndex: ['unit'],
      width: 60,
      fieldProps: {
        placeholder: 'Unit',
      },
      onCell: (record, index) => {
        if (record.parent_id) {
          return { colSpan: 0 };
        }
        return {};
      },
    },
    {
      title: 'Description',
      dataIndex: ['desc'],
      width: 350,
      fieldProps: {
        placeholder: 'Description',
      },
      onCell: (record, index) => {
        if (record.parent_id) {
          return { colSpan: 5 };
        }
        return {};
      },
    },
    {
      title: 'Price',
      dataIndex: ['price'],
      width: 90,
      tooltip: 'Click to Enter key to add a new Pos line.',
      showSorterTooltip: false,
      fieldProps: {
        placeholder: 'Price',
      },
      onCell: (record, index) => {
        if (record.parent_id) {
          return { colSpan: 0 };
        }
        return {};
      },
      render: (dom, record) => Util.numberFormat(record.price, false, 3),
    },
    {
      title: 'Total',
      dataIndex: ['price2'],
      editable: false,
      width: 70,
      fieldProps: {
        placeholder: 'Price',
      },
      render: (dom, record) => {
        return Util.numberFormat(sn(record.price) * sn(record.qty), true, 3, false);
      },
      onCell: (record, index) => {
        if (record.parent_id) {
          return { colSpan: 0 };
        }
        return {};
      },
    },
    {
      title: '',
      valueType: 'option',
      width: 100,
      render(__, record) {
        return !record.parent_id ? (
          <Button
            type="link"
            size="small"
            title="Add this row in Finanical Preview"
            onClick={() => {
              props.addToPreview({
                Gepackt: record.qty,
                Beschreibung: record.desc,
                Preis: record.price,
                Summe: sn(record.qty) * sn(record.price),
              });
            }}
          >
            Add to preview
          </Button>
        ) : null;
      },
    },
  ];

  useEffect(() => {
    // invoice items
    const items = orderOut?.order_out_items ? [...(orderOut?.order_out_items || [])] : [];

    setDataSource(items);
    const pIds: React.Key[] = [];
    items.forEach((element) => {
      if (!element.parent_id) pIds.push(element.id as React.Key);
    });
    setExpandedRowKeys(pIds);
  }, [orderOut?.order_out_items]);

  return (
    <div style={{ width: 900 }}>
      {aggregationElement}
      <Card style={{ marginTop: 0 }} bodyStyle={{ padding: 0 }} bordered={false}>
        {isVoided && (
          <Alert
            message={' Invoice has been cancelled.'}
            type="warning"
            showIcon
            banner
            style={{
              marginBottom: 4,
            }}
          />
        )}
        <ProTable<OrderOutItemEditableFormType>
          actionRef={actionRef}
          rowKey="id"
          sticky
          bordered={false}
          debounceTime={200}
          className="editable-table"
          size="small"
          cardProps={{
            bodyStyle: { padding: 0 },
          }}
          scroll={{
            x: 700,
          }}
          style={{
            padding: 0,
          }}
          search={false}
          options={false}
          loading={loading}
          columns={columns}
          dataSource={dataSource}
          pagination={false}
          locale={{ emptyText: <></> }}
          expandable={{
            defaultExpandAllRows: true,
            expandedRowKeys,
            onExpandedRowsChange(expandedKeys) {
              setExpandedRowKeys([...expandedKeys]);
            },
            indentSize: 100,
            rowExpandable: (record) => {
              return !record?.parent_id;
            },
          }}
        />
      </Card>
    </div>
  );
};

export default OrderOutInvoiceLineItems;
