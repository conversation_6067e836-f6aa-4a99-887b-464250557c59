import type { Dispatch, SetStateAction } from 'react';
import { useEffect } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormUploadButton } from '@ant-design/pro-form';
import { ProFormRadio } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { message } from 'antd';
import Util from '@/util';
import { CSVFileDelimiterOptions } from '@/constants';
import type { RcFile, UploadFile } from 'antd/lib/upload';
import SProFormDigit2 from '@/components/SProFormDigit2';
import { importFinInventurAll } from '@/services/app/Fin/fin-inventur-all';

export type FormValueType = {
  headerRowNo?: number;
  dataStartRowNo?: number;
  importMode?: string;
  delimiter?: string;
  files?: UploadFile[];
};

const handleImport = async (dataParam: FormValueType) => {
  const data = new FormData();
  data.set('dataStartRowNo', `${dataParam.dataStartRowNo}`);
  data.set('headerRowNo', `${dataParam.headerRowNo}`);
  data.set('delimiter', `${dataParam.delimiter}`);
  data.set('importMode', `${dataParam.importMode}`);
  if (dataParam.files) {
    data.append(`file`, dataParam.files[0].originFileObj as RcFile);
  }

  const hide = message.loading('Importing Inventur all from selected file...', 0);
  return importFinInventurAll(data)
    .then((res) => {
      return res;
    })
    .catch((err) => Util.error(err))
    .finally(() => hide());
};

export type ImportModalFormProps = {
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: FormValueType) => Promise<boolean | void>;
};

const ImportModalForm: React.FC<ImportModalFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  // const [loading, setLoading] = useState<boolean>(false);
  // const [existStatus, setExistStatus] = useState<boolean | null>(null);

  useEffect(() => {
    if (modalVisible) {
      const cachedData = Util.getSfValues('f_lo_susa', {});
      formRef.current?.setFieldsValue(cachedData);
    }
  }, [modalVisible]);

  return (
    <ModalForm<FormValueType>
      title={'Import Inventur All from XLS/CSV file'}
      formRef={formRef}
      width="700px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ flex: '150px' }}
      onFinish={async (formValue) => {
        const success = await handleImport(formValue);
        if (success) {
          if (formRef.current) formRef.current.setFieldValue('files', null);
          if (onSubmit) await onSubmit(formValue);
        }
      }}
    >
      <SProFormDigit2
        label="Header Row No"
        name="headerRowNo"
        width={100}
        initialValue={1}
        min={1}
      />
      <SProFormDigit2
        label="First Data Row No"
        name="dataStartRowNo"
        width={100}
        initialValue={2}
        min={1}
      />
      <ProFormRadio.Group
        label="CSV file delimiter"
        name="delimiter"
        fieldProps={{ buttonStyle: 'solid' }}
        options={CSVFileDelimiterOptions}
        tooltip="valid only for Csv file."
        initialValue=";"
      />
      <ProFormRadio.Group
        label="Import mode"
        name="importMode"
        fieldProps={{ buttonStyle: 'solid' }}
        options={[
          {
            value: 'truncateAll',
            label: 'Truncate All',
          },
          {
            value: 'update',
            label: 'Update',
          },
        ]}
        initialValue="update"
      />
      <ProFormUploadButton
        max={1}
        name="files"
        label="File"
        title="Select File"
        accept=".xls,.xlsx,.csv"
        required
        rules={[
          {
            required: true,
            message: 'File is required',
          },
        ]}
        fieldProps={{
          beforeUpload: () => {
            return false;
          },
        }}
      />
    </ModalForm>
  );
};

export default ImportModalForm;
