import React, { useRef, useEffect, useState } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { nf2 } from '@/util';

import {
  createOrUpdateLoAccountPlanning,
  getLoAccountPlanningList,
} from '@/services/app/LexOffice/lo-susa';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import SProFormDigit2 from '@/components/SProFormDigit2';
import EditableCell from '@/components/EditableCell';

const currentFy = Util.dtCurrentFy() + 1;
const FyKv = Util.dtBuildFyKv(currentFy);
const FyListOptions = Util.dtBuildFyList(currentFy);

type LoAccountPlanningListPropsType = {
  accountNo?: number;
  refreshTick?: number;
  createOrUpdateCallback?: (newPlan?: API.LoAccountPlanning) => void;
};

const LoAccountPlanningList: React.FC<LoAccountPlanningListPropsType> = ({
  accountNo,
  refreshTick,
  createOrUpdateCallback,
}) => {
  const actionRef = useRef<ActionType>();
  const newPlanFormRef = useRef<ProFormInstance<API.LoAccountPlanning>>(null);

  const handleCreateOrUpdate = async (values: API.LoAccountPlanning) => {
    return createOrUpdateLoAccountPlanning({ ...values, account_no: accountNo })
      .catch((err) => Util.error(err))
      .then((res) => {
        if (res) {
          createOrUpdateCallback?.(res);
          newPlanFormRef.current?.resetFields();
          actionRef.current?.reload();
        }
        return res;
      });
  };

  useEffect(() => {
    if (accountNo) {
      actionRef.current?.reload();
    }
  }, [accountNo]);

  useEffect(() => {
    if (refreshTick) {
      actionRef.current?.reload();
    }
  }, [refreshTick]);

  const columns: ProColumns<API.LoAccountPlanning>[] = [
    {
      title: 'Year',
      dataIndex: 'year',
      width: 150,
      sorter: true,
      align: 'center',
      className: 'text-13',
      defaultSortOrder: 'descend',
      render(__, record) {
        return FyKv[`${record.year}`] ?? record.year;
      },
    },
    {
      title: 'Value',
      dataIndex: 'value',
      className: 'text-13',
      tooltip: 'Click to edit',
      render: (dom, record) => (
        <EditableCell
          dataType="number"
          defaultValue={record.value}
          precision={2}
          triggerUpdate={function (
            value: any,
            cancelEdit?: (() => void) | undefined,
          ): Promise<any> {
            return handleCreateOrUpdate({ year: record.year, value: value, id: record.id }).then(
              (res) => cancelEdit?.(),
            );
          }}
        >
          {nf2(record.value)}
        </EditableCell>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.LoAccountPlanning, API.PageParams>
        headerTitle={`Planning - ${accountNo}`}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: false, density: false, search: false, setting: false }}
        size="small"
        search={false}
        cardProps={{ bodyStyle: { padding: 0 } }}
        pagination={{
          hideOnSinglePage: true,
          showSizeChanger: true,
          pageSize: 100,
        }}
        request={async (params, sort) => {
          const newParams = {
            ...params,
            account_no: accountNo,
            sort: sort,
          };
          return getLoAccountPlanningList(newParams);
        }}
        onRequestError={(err) => Util.error(err)}
        columns={columns}
        locale={{ emptyText: <></> }}
        toolBarRender={() => [
          <ProForm<API.LoAccountPlanning>
            key="new-form"
            formRef={newPlanFormRef}
            layout="inline"
            size="small"
            onFinish={async (values) => {
              handleCreateOrUpdate(values);
            }}
            submitter={{
              searchConfig: { submitText: 'New' },
              resetButtonProps: false,
            }}
          >
            <ProFormSelect
              key="year"
              name="year"
              width={100}
              placeholder="Fiscal year"
              formItemProps={{ style: { marginBottom: 0 } }}
              options={FyListOptions}
              rules={[
                {
                  required: true,
                  message: '',
                },
              ]}
            />
            <SProFormDigit2
              key="value"
              name="value"
              formItemProps={{ style: { marginBottom: 0, width: 150 } }}
              fieldProps={{
                precision: 2,
                style: { width: 150 },
              }}
            />
          </ProForm>,
        ]}
        columnEmptyText=""
      />
    </>
  );
};

export default LoAccountPlanningList;
