import { useEffect, useState } from 'react';
import { getEmailTemplateMappingFields } from '@/services/app/Email/email-template';
import Util from '@/util';

export default () => {
  const [mappingData, setMappingData] = useState<{ fields?: any }>({ fields: {} });

  useEffect(() => {
    getEmailTemplateMappingFields().then((res) => setMappingData(res)).catch(err => Util.error(err));
  }, []);

  return { mappingData, setMappingData };
};
