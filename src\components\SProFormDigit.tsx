import Util, { sn } from '@/util';
import { ProFormDigit } from '@ant-design/pro-form';
import type { ProFormDigitProps } from '@ant-design/pro-form/lib/components/Digit';

/* const SProFormDigit = (props: ProFormItemProps<import("antd").DatePickerProps, any>) => {
    return (
        <ProFormDatePicker
            {...props}
            fieldProps={{
                locale: deDE.DatePicker,
                ...(props?.fieldProps || {}),
                // format: ['DD.MM.YYYY', (value) => value && value.isValid() ? value.format('YYYY-MM-DD') : ''],
                format: [DT_FORMAT_DMY, DT_FORMAT_YMD],
            }}
        />
    );
} */

const SProFormDigit = (
  props: ProFormDigitProps & { zeroShow?: boolean; noFormatting?: boolean },
) => {
  return (
    <ProFormDigit
      {...props}
      fieldProps={{
        formatter: (value) => {
          /* const ret =
            value !== undefined && value !== null && value !== 0 && value !== '0' && value !== ''
              ? '' + Util.numberFormat(value, props.zeroShow, props?.fieldProps?.precision, true)
              : (value as string);
          console.log(
            'Zero show? ',
            props.zeroShow,
            'values:',
            value,
            ret,
            'Calc; ',
            Util.numberFormat(value, props.zeroShow, props?.fieldProps?.precision, true),
          );
          console.log(value, '??', value !== undefined, value !== null,); */
          if (Util.isZero(sn(value), props.fieldProps?.precision ?? 0)) {
            if (props.zeroShow) return '0';
            else return '';
          } else {
            return (
              '' +
              (props.noFormatting
                ? value
                : Util.numberFormat(value, props.zeroShow, props?.fieldProps?.precision ?? 0, true))
            );
          }
        },
        parser: (x) => parseFloat(`${x}`.replace(/,/, '#').replace(/\./g, '').replace(/#/, '.')),
        decimalSeparator: ',',
        ...(props?.fieldProps || {}),
      }}
    />
  );
};

export default SProFormDigit;
