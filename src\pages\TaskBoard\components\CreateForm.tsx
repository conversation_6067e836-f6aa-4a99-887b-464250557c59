import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useState } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormItem } from '@ant-design/pro-form';
import { ProFormGroup } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import { ProFormDatePicker } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { addTask } from '@/services/app/Task/task';
import { Button, Col, message, Row, Space } from 'antd';
import Util from '@/util';
import { useModel } from 'umi';
import _ from 'lodash';
import { TaskBlockLayout, TaskStatus, TaskStatusOptions } from '@/constants';
import { CheckSquareOutlined, SwitcherOutlined } from '@ant-design/icons';
import SDatePicker from '@/components/SDatePicker';
import SProFormDateRange from '@/components/SProFormDateRange';

const handleAdd = async (fields: API.Task) => {
  const hide = message.loading('Adding...');
  const data = { ...fields };
  try {
    const res = await addTask(data);
    message.success('Added successfully');
    return res;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

// eslint-disable-next-line @typescript-eslint/ban-types
export type FormValueType = {};

export type CreateFormProps = {
  groupId?: number;
  blockId?: number;
  parentId?: number; // For sub task
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Task) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const { modalVisible, handleModalVisible, onSubmit, blockId, groupId, parentId } = props;
  const { getBlockLayout, taskGroups, blockInfoMap, getTaskGroupInModel } = useModel('task-group');
  const [blockIds, setBlockIds] = useState<number[]>([]);

  const formRef = useRef<ProFormInstance>();

  const blockLayout = getBlockLayout(blockId);

  useEffect(() => {
    if (modalVisible && blockId) {
      setBlockIds([blockId]);
      formRef.current?.setFieldValue('block_ids', [blockId]);
    }
  }, [modalVisible, blockId]);

  const groupInfo = getTaskGroupInModel(groupId);
  const isSubGroup = !!groupInfo?.task_id;

  return (
    <ModalForm
      title={
        parentId ? (
          <>
            <SwitcherOutlined className="c-blue4" /> New sub task from #{parentId}
          </>
        ) : (
          <>
            <CheckSquareOutlined className="c-blue4" /> New task
          </>
        )
      }
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
      formRef={formRef}
      submitter={{
        render(__, dom) {
          return [
            <Button key="cancel" onClick={() => handleModalVisible(false)}>
              {' '}
              Cancel
            </Button>,
            <Button key="submit" type="primary" onClick={() => formRef.current?.submit()}>
              Create
            </Button>,
          ];
        },
      }}
      onFinish={async (value) => {
        const newTask = {
          ...value,
          group_id: groupId,
          block_id: blockId,
          parent_id: parentId,
          block_ids: !isSubGroup ? blockIds : [blockId], // Override block Ids info in the sub groups
        };
        const res = await handleAdd(newTask as API.Task);
        if (res) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit({ ...newTask, ...res });
        }
      }}
    >
      <ProFormText
        required
        rules={[
          {
            required: true,
            message: 'Title is required',
          },
        ]}
        width="md"
        name="title"
        label="Title"
      />
      <ProFormTextArea width="md" name="desc" label="Description" />
      {blockLayout == TaskBlockLayout.LAYOUT_T_DATE && (
        <>
          {/* <Row>
            <Col flex={'285px'}>
              <SDatePicker
                width={110}
                name="date"
                label="Date"
                addonAfter="~"
                placeholder="Start date"
                labelCol={{ flex: '150px' }}
              />
            </Col>
            <Col>
              <SDatePicker width={110} name="end_date" placeholder="End date" />
            </Col>
          </Row> */}
          <SProFormDateRange label="Date" startDateName="date" endDateName="end_date" />
          {/* <ProFormItem label="Date alt">
            <div style={{ display: 'flex' }}>
              <SDatePicker width={110} name="date2" addonAfter="~" placeholder="Start date" />
              <SDatePicker
                width={110}
                name="end_date2"
                placeholder="End date"
                fieldProps={{ style: { marginLeft: 6 } }}
              />
            </div>
          </ProFormItem> */}
        </>
      )}
      {parentId && (
        <ProFormSelect
          width="lg"
          name="status"
          label="Status"
          options={TaskStatusOptions}
          initialValue={TaskStatus.OPEN}
        />
      )}
      {!parentId && !isSubGroup && (
        <ProFormSelect
          name="block_ids"
          label="Blocks"
          required
          mode="multiple"
          initialValue={blockId ? [blockId] : []}
          rules={[
            {
              required: true,
              message: 'Block is required',
            },
          ]}
          fieldProps={{
            onChange: (ids) => {
              setBlockIds(ids || []);
            },
          }}
          options={taskGroups?.map((x: API.TaskGroup) => ({
            value: x.id,
            label: `${x.name} (${x.code})`,
            children: x.task_blocks?.map((b) => ({
              value: b.id,
              label: `${x.code} / ${b.name}`,
              disabled:
                blockIds?.findIndex(
                  (id: any) => id != b.id && blockInfoMap?.[id]?.group_id == x.id,
                ) >= 0,
            })),
          }))}
        />
      )}
    </ModalForm>
  );
};

export default CreateForm;
