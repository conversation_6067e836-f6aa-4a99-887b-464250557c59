/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/lo-invoice';

/** rule GET /api/lo-invoice */
export async function getLoInvoiceList(
  params: API.PageParams &
    Partial<API.LoInvoice> & { orderNo?: number; lo_types?: string[]; lo_statuses?: string[] },
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.LoInvoice>> {
  return request<API.Result<API.LoInvoice>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** put PUT /api/lo-invoice */
export async function updateLoInvoice(
  data: Partial<API.LoInvoice>,
  options?: { [key: string]: any },
) {
  return request<API.LoInvoice>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/lo-invoice */
export async function addLoInvoice(data: API.LoInvoice, options?: { [key: string]: any }) {
  return request<API.LoInvoice>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/lo-invoice/{id} */
export async function deleteLoInvoice(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}

