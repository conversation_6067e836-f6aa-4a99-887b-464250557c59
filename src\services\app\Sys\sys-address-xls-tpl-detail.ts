/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/sys/sys-address-xls-tpl-detail';

/** rule GET /api/sys/sys-address-xls-tpl-detail */
export async function getSysAddressXlsTplDetailList(
  params: API.PageParams & Partial<API.SysAddressXlsTplDetail>,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.SysAddressXlsTplDetail>> {
  return request<API.Result<API.SysAddressXlsTplDetail>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** put PUT /api/sys/sys-address-xls-tpl-detail */
export async function updateSysAddressXlsTplDetail(
  id?: number,
  data?: Partial<API.SysAddressXlsTplDetail>,
  options?: { [key: string]: any },
) {
  return request<API.SysAddressXlsTplDetail>(`${urlPrefix}/` + id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/sys/sys-address-xls-tpl-detail */
export async function addSysAddressXlsTplDetail(
  data: API.SysAddressXlsTplDetail,
  options?: { [key: string]: any },
) {
  return request<API.SysAddressXlsTplDetail>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/sys/sys-address-xls-tpl-detail/{id} */
export async function deleteSysAddressXlsTplDetail(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}
