/* eslint-disable */
import { request } from 'umi';
import type { DefaultOptionType } from 'antd/lib/select';
import { getSupplierACList } from './supplier';

const urlPrefix = '/api/basic-data/customer';

/** rule GET /api/basic-data/customer */
export async function getCustomerList(
  params: API.PageParams,
  sort: any,
  filter: any,
): Promise<API.PaginatedResult<API.Customer>> {
  return request<API.Result<API.Customer>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort_detail: JSON.stringify(sort),
      filter_detail: JSON.stringify(filter),
    },
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

export async function getCustomerACList(
  params?: API.PageParams,
  sort?: Record<string, any>
): Promise<(DefaultOptionType & { uid?: string })[]> {
  return getCustomerList({ ...params, perPage: params?.perPage ?? 400, current: params?.current ?? 1 }, { name: 'ascend' }, sort ?? {}).then((res) =>
    res.data?.map((x) => ({
      value: x.id,
      uid: `c${x.id}`,
      label: `${x.name}${x.description ? ` - ${x.description.substring(0, 20)}` : ''}`,
    })),
  );
}

export async function getCustomerAndSupplierACList(
  params?: API.PageParams
): Promise<(DefaultOptionType & { uid?: string })[]> {
  return Promise.all([getCustomerACList(params), getSupplierACList(params)]).then(
    (res) => {
      return [...res[0], ...res[1]].map(x => ({ ...x, value: x.uid })) as any;
    },
  )
}

/** put PUT /api/basic-data/customer */
export async function updateCustomer(data: API.Customer, options?: { [key: string]: any }) {
  return request<API.Customer>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/basic-data/customer */
export async function addCustomer(data: API.Customer, options?: { [key: string]: any }) {
  return request<API.Customer>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/basic-data/customer/{id} */
export async function deleteCustomer(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}
