import type { Dispatch, SetStateAction } from 'react';
import { useEffect } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { createLoPayment } from '@/services/app/LexOffice/lo-payment';
import { message } from 'antd';
import Util from '@/util';
import SDatePicker from '@/components/SDatePicker';
import SProFormDigit2 from '@/components/SProFormDigit2';
import SProFormSelect from '@/components/SProFormSelect';
import { getCustomerACList } from '@/services/app/BasicData/customer';
import { getSupplierACList } from '@/services/app/BasicData/supplier';

export type FormValueType = API.LoPayment;

const handleAdd = async (dataParam: FormValueType) => {
  const hide = message.loading('Creating...', 0);
  const data = { ...dataParam };
  try {
    await createLoPayment(data);
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type CreateBalanceFormProps = {
  initialValues?: Partial<API.LoPayment>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: FormValueType) => Promise<boolean | void>;
};

const CreateBalanceForm: React.FC<CreateBalanceFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;

  useEffect(() => {
    if (modalVisible) {
      formRef.current?.setFieldsValue(props.initialValues);
    }
  }, [modalVisible, props.initialValues]);

  return (
    <ModalForm<FormValueType>
      title={'Create Balance'}
      width="700px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelCol={{ span: 8 }}
      labelAlign="left"
      formRef={formRef}
      onFinish={async (formValue) => {
        // Balance only available
        const success = await handleAdd({ ...formValue, type: 2 });
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(formValue);
        }
      }}
    >
      <SDatePicker
        name="date"
        width={120}
        label="Date"
        rules={[
          {
            required: true,
            message: 'Date is required',
          },
        ]}
      />
      <ProFormText name="actor" label="Empfänger / Auftraggeber" />
      <SProFormDigit2
        name="amount"
        label="Betrag"
        rules={[
          {
            required: true,
            message: 'Amount is required',
          },
        ]}
        fieldProps={{ precision: 3 }}
        width={150}
        zeroShow
      />
      <ProFormTextArea fieldProps={{ rows: 3 }} name="note" label="Verwendungszweck" />
      <SProFormSelect
        name="customer_id"
        label="Customer"
        width={170}
        mode="single"
        placeholder={'Customer'}
        request={(params) =>
          getCustomerACList(params).then((res) => [...res, { value: 0, label: '_None' }]) as any
        }
        onTabKeyCallback={(value) => {
          formRef.current?.setFieldValue('customer_id', +value);
        }}
      />
      <SProFormSelect
        name="supplier_id"
        label="Supplier"
        width={170}
        mode="single"
        placeholder={'Supplier'}
        request={(params) =>
          getSupplierACList(params).then((res) => [...res, { value: 0, label: '_None' }]) as any
        }
        onTabKeyCallback={(value) => {
          formRef.current?.setFieldValue('supplier_id', +value);
        }}
      />
    </ModalForm>
  );
};

export default CreateBalanceForm;
