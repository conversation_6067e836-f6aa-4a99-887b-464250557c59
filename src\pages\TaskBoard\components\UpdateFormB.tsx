import type { Dispatch, SetStateAction } from 'react';
import { useCallback } from 'react';
import { useMemo } from 'react';
import { useState } from 'react';
import React from 'react';
import { Col, Row, Card, Modal } from 'antd';
import { updateTaskPartial } from '@/services/app/Task/task';
import { sn } from '@/util';
import { useModel } from 'umi';
import _ from 'lodash';
import { CheckSquareFilled, SwitcherOutlined } from '@ant-design/icons';
import type { onTaskClickType } from '..';
import WorkflowStepData from './TaskWorkflow/WorkflowStepData';
import UpdateFormInB from './UpdateFormInB';
import EditableCell from '@/components/EditableCell';

// eslint-disable-next-line @typescript-eslint/ban-types
export type FormValueType = {} & Partial<API.Task>;

export type UpdateFormBProps = {
  initialValues?: Partial<API.Task & { group_id?: number; block_id?: number; index?: number }>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Task) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  onTaskClick?: onTaskClickType;
};

/**
 * Workflow based update form. Workflow step items will be shown on tabs
 *
 * Default editable task form is on the last tab.
 */
const UpdateFormB: React.FC<UpdateFormBProps> = (props) => {
  const { getTaskGroupInModel } = useModel('task-group');

  const { initialValues: task, onTaskClick, onSubmit } = props;
  const { parent_id: parentId, group_id: groupId, block_id: droppableId, index } = task || {};

  const [refreshTick, setRefreshTick] = useState<number>(0);

  const groupInfo = getTaskGroupInModel(groupId);

  const RenderedUpdateFormInB = useMemo(() => {
    return (
      <UpdateFormInB
        initialValues={task}
        onTaskClick={onTaskClick}
        onSubmit={onSubmit}
        refreshTick={refreshTick}
        setRefreshTick={setRefreshTick}
      />
    );
  }, [task, onTaskClick, onSubmit, refreshTick]);

  const workflowUpdateCallback = useCallback((values: any) => {
    setRefreshTick((prev) => prev + 1);
  }, []);

  return (
    <Modal
      title={
        parentId ? (
          <>
            <SwitcherOutlined className="c-blue4" /> Update Sub Task #{props.initialValues?.id} in
            &nbsp;
            <CheckSquareFilled className="c-blue4" /> #{parentId}
          </>
        ) : (
          <Row gutter={16} style={{ height: 32, alignItems: 'center' }} wrap={false}>
            <Col flex={'0 0 200px'}>
              <CheckSquareFilled className="c-blue4" /> Update Task #{props.initialValues?.id}
            </Col>
            <Col flex="auto" style={{ paddingRight: 30 }}>
              <EditableCell
                dataType="text"
                defaultValue={props.initialValues?.title}
                triggerUpdate={async function (
                  value: any,
                  cancelEdit?: (() => void) | undefined,
                ): Promise<void> {
                  return updateTaskPartial({ id: props.initialValues?.id, title: value }).then(
                    (res) => {
                      cancelEdit?.();
                      props.onSubmit?.({
                        ...(props.initialValues ?? {}),
                        id: res.id,
                        title: res.title,
                      });
                    },
                  );
                }}
                width={'md'}
                showEditIcon
              >
                {props.initialValues?.title}
              </EditableCell>
            </Col>
          </Row>
        )
      }
      width="1300px"
      open={props.modalVisible}
      onCancel={() => props.handleModalVisible(false)}
      footer={false}
    >
      <Card
        title=""
        bordered={false}
        style={{ minHeight: 350 }}
        className="workflows"
        bodyStyle={{ padding: 0 }}
      >
        <WorkflowStepData
          task={task}
          workflow_id={sn(groupInfo?.settings?.updateTaskLayoutWSId)}
          lastTab={RenderedUpdateFormInB}
          workflowUpdateCallback={workflowUpdateCallback}
        />
      </Card>
    </Modal>
  );
};

export default UpdateFormB;
