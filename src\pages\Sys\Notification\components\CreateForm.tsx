import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import { ProFormInstance, ProFormSelect } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { addSysNotification } from '@/services/app/Sys/notification';
import { message } from 'antd';
import Util from '@/util';
import { getTaskGroupACList } from '@/services/app/Task/task-group';
import SDatePicker from '@/components/SDatePicker';

const handleAdd = async (fields: API.SysNotification) => {
  const hide = message.loading('Adding...');
  const data = { ...fields };
  try {
    await addSysNotification(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

// eslint-disable-next-line @typescript-eslint/ban-types
export type FormValueType = {};

export type CreateFormProps = {
  values?: Partial<API.SysNotification>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.SysNotification) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={'New system notification'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 12 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd(value as API.SysNotification);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormText
        required
        rules={[
          {
            required: true,
            message: 'Title is required',
          },
        ]}
        width="md"
        name="title"
        label="Title"
      />
      <ProFormTextArea width="md" name="desc" label="Description" />
      <ProFormSelect
        name="type"
        label="Type"
        required
        options={[{ value: 'board', label: 'Board' }]}
        initialValue={'board'}
      />
      <ProFormSelect
        name="status"
        label="Status"
        required
        initialValue={1}
        options={[
          { value: 1, label: 'Enabled' },
          { value: 0, label: 'Disabled' },
        ]}
      />
      <ProFormSelect
        name="ref_id"
        label="Task group"
        request={(params) =>
          getTaskGroupACList(params).then((res) =>
            res.map((x: API.TaskGroup) => ({ value: x.id, label: x.name })),
          )
        }
      />
      <SDatePicker name="start_date" label="Start date" />
      <SDatePicker name="end_date" label="End date" />
    </ModalForm>
  );
};

export default CreateForm;
