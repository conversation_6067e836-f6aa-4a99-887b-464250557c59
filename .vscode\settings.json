{
  "editor.formatOnSave": true,
  "prettier.requireConfig": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "git.enableSmartCommit": true,
  "[jsonc]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  // "scss.lint.unknownAtRules": "ignore",
  "editor.fontFamily": "JetBrains Mono, <PERSON>homa, Consolas, 'Courier New', monospace",
  "editor.fontSize": 13,
  "editor.lineHeight": 21,
  "editor.fontWeight": "300",
  "antdRush.defaultAntdMajorVersion": "^4",
  "antdRush.language": "English",
  "jest.autoRun": "false",
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "vscode.typescript-language-features"
  },
  "cSpell.words": [
    "daterange",
    "OOFCN",
    "orderout",
    "Popconfirm"
  ],
  "workbench.tree.indent": 16
}