import { DictCode } from '@/constants';
import { getSysAddressXlsTplACList } from '@/services/app/Sys/sys-address-xls-tpl';
import { sn } from '@/util';
import { ExportOutlined } from '@ant-design/icons';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormSelect } from '@ant-design/pro-form';
import { Button } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useRef } from 'react';
import { useModel } from 'umi';

export type XlsTplSelectionModalProps = {
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit: (formData: API.Address) => Promise<boolean | void>;
};

const XlsTplSelectionModal: React.FC<XlsTplSelectionModalProps> = (props) => {
  const { modalVisible } = props;
  const formRef = useRef<ProFormInstance>();
  const { getCodeValue } = useModel('app-settings');

  return (
    <ModalForm
      title="Select XLS template & Export address"
      width="500px"
      visible={modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ flex: '0 0 120px' }}
      formRef={formRef}
      onFinish={async (value) => {
        props.handleModalVisible(false);
        if (props.onSubmit) props.onSubmit(value);
      }}
      submitter={{
        render(__, doms) {
          return (
            <>
              <Button
                type="primary"
                icon={<ExportOutlined />}
                onClick={() => formRef.current?.submit()}
              >
                Export
              </Button>
            </>
          );
        },
      }}
    >
      <ProFormSelect
        required
        rules={[
          {
            required: true,
            message: 'XLS Template is required',
          },
        ]}
        name="tplId"
        showSearch
        label="XLS Template"
        mode="single"
        initialValue={sn(getCodeValue(DictCode.ORDER_OUT_CRM_XLS_TEMPLATE))}
        request={(params) => getSysAddressXlsTplACList(params)}
      />
    </ModalForm>
  );
};
export default XlsTplSelectionModal;
