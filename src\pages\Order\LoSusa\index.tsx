import { <PERSON><PERSON>, <PERSON>, Space } from 'antd';
import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { nf2 } from '@/util';

import {
  getLoAccountsList,
  getLoSusaList,
  getNextAccountCategory,
  getNextAccountNo,
} from '@/services/app/LexOffice/lo-susa';
import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  FileExcelOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import CreateForm from './components/CreateForm';
import { TransactionTypeOptions } from '@/constants';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ProFormCheckbox } from '@ant-design/pro-form';
import { ProFormText } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import SDatePicker from '@/components/SDatePicker';
import UpdateAccountDataForm from './components/UpdateAccountDataForm';
import ImportModalForm from './components/ImportModalForm';

const LoSusaListPage: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();

  const [loading, setLoading] = useState<boolean>(false);

  const [openPreviewModal, setOpenPreviewModal] = useState<boolean>(false);
  const [openUpdateAccountModal, setOpenUpdateAccountModal] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.LoSusa>();

  // XLS import
  const [openImportModal, setOpenImportModal] = useState<boolean>(false);

  const [accountCategories, setAccountCategories] = useState<API.LoAccount[]>([]);

  const loadAccountCategories = useCallback(() => {
    getLoAccountsList({ perPage: 5000 }).then((res) => {
      setAccountCategories(res.data);
    });
  }, []);

  useEffect(() => {
    loadAccountCategories();
  }, [loadAccountCategories]);

  const columns: ProColumns<API.LoSusa>[] = [
    {
      title: 'Date',
      sorter: true,
      dataIndex: 'ym',
      valueType: 'date',
      search: false,
      width: 70,
      render: (dom, record) => Util.dtToMY(`${record.ym}-01`),
      className: 'text-13',
    },
    {
      title: 'Type',
      sorter: true,
      dataIndex: 'category',
      search: false,
      width: 110,
      className: 'text-13',
      render: (dom, record) =>
        TransactionTypeOptions.find((x) => x.value == record.category)?.label ?? '',
    },
    {
      title: 'Konto',
      sorter: true,
      dataIndex: 'account_no',
      search: false,
      width: 90,
      align: 'right',
      copyable: true,
      className: 'text-13',
    },
    {
      title: 'Category',
      sorter: false,
      dataIndex: ['lo_account', 'category'],
      search: false,
      width: 90,
      className: 'text-13 cursor-pointer',
      tooltip: 'Click to edit.',
      onCell: (record) => {
        return {
          onClick() {
            setCurrentRow(record);
            setOpenUpdateAccountModal(true);
          },
        };
      },
    },
    {
      title: 'Kontobezeichnung',
      sorter: true,
      dataIndex: 'account_name',
      search: false,
      className: 'text-13',
    },
    {
      title: 'EB-Wert Soll',
      dataIndex: 'eb_wert_debit',
      width: 90,
      align: 'right',
      className: 'text-13',
      render: (dom, record) => nf2(record.eb_wert_debit),
    },
    {
      title: 'EB-Wert Haben',
      dataIndex: 'eb_wert_credit',
      width: 90,
      align: 'right',
      className: 'text-13',
      render: (dom, record) => nf2(record.eb_wert_credit),
    },

    {
      title: 'Summe Monat Soll',
      dataIndex: 'sum_january_debit',
      width: 120,
      align: 'right',
      className: 'text-13',
      render: (dom, record) => nf2(record.sum_january_debit),
    },
    {
      title: 'Summe für Monat Haben',
      dataIndex: 'sum_january_credit',
      width: 120,
      align: 'right',
      className: 'text-13',
      render: (dom, record) => nf2(record.sum_january_credit),
    },

    {
      title: 'Saldo Soll',
      dataIndex: 'balance_all_debit',
      width: 90,
      align: 'right',
      className: 'text-13',
      render: (dom, record) => nf2(record.balance_all_debit),
    },
    {
      title: 'Saldo Haben',
      dataIndex: 'balance_all_credit',
      width: 90,
      align: 'right',
      className: 'text-13',
      render: (dom, record) => nf2(record.balance_all_credit),
    },

    {
      title: 'Created on',
      dataIndex: 'created_on',
      width: 150,
      className: 'text-sm c-grey',
      render: (dom, record) => Util.dtToDMYHHMM(record.created_on),
    },
    {
      title: 'Created by',
      dataIndex: 'created_by_username',
      width: 150,
      className: 'text-sm c-grey',
      ellipsis: true,
      render: (dom, record) => record.created_by_username,
    },

    /* {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 120,
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            setCurrentRow(record);
          }}
        >
          Edit
        </a>,
      ],
    }, */
  ];

  useEffect(() => {
    const lastSf = Util.getSfValues('sf_lo_susa', {});
    if (!lastSf.date_from) {
      const dtList = Util.dtBuildRanges('m', 1, 1);
      lastSf.date_from = dtList[0].from;
    }
    searchFormRef.current?.setFieldsValue(lastSf);
    Util.setSfValues(lastSf);
  }, []);

  /**
   * Next or previous account no (Konto)
   *
   * @param dir -1 or 1
   */
  const hanldeAccountNoNavigation = (dir: number) => {
    const currentAccountNo = searchFormRef.current?.getFieldValue('account_no');
    setLoading(true);
    getNextAccountNo(dir, currentAccountNo)
      .then((res) => {
        if (res) {
          searchFormRef.current?.setFieldValue('account_no', res);
          actionRef.current?.reload();
        }
      })
      .catch((err) => Util.error(err))
      .finally(() => setLoading(false));
  };

  /**
   * Next or previous account category
   *
   * @param dir -1 or 1
   */
  const hanldeAccountCategoryNavigation = (dir: number) => {
    const category = searchFormRef.current?.getFieldValue(['lo_account', 'category']);
    setLoading(true);
    getNextAccountCategory(dir, category)
      .then((res) => {
        if (res) {
          searchFormRef.current?.setFieldValue(['lo_account', 'category'], res);
          actionRef.current?.reload();
        }
      })
      .catch((err) => Util.error(err))
      .finally(() => setLoading(false));
  };

  const categories: string[] = useMemo(() => {
    return accountCategories.map((x) => x.category ?? '');
  }, [accountCategories]);

  const valuesInUpdateForm = useMemo(() => {
    return { account_no: currentRow?.account_no, ...currentRow?.lo_account };
  }, [currentRow?.account_no, currentRow?.lo_account]);

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<API.LoSusa>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            submitButtonProps: {
              htmlType: 'submit',
            },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => actionRef.current?.reload(),
            render: (form, dom) => {
              return [...dom];
            },
          }}
        >
          <SDatePicker
            name="date_from"
            label="Date"
            placeholder="From"
            width={120}
            addonAfter="~"
            fieldProps={{ picker: 'month' }}
          />
          <SDatePicker
            name="date_to"
            placeholder="To"
            width={120}
            fieldProps={{ picker: 'month' }}
          />
          <ProFormText
            name={'account_no'}
            label="Konto"
            width={120}
            placeholder={'Konto'}
            disabled={loading}
            addonAfter={
              <>
                <Button
                  onClick={() => hanldeAccountNoNavigation(-1)}
                  icon={<ArrowLeftOutlined />}
                  title="Previous Konto"
                  disabled={loading}
                />
                <Button
                  onClick={() => hanldeAccountNoNavigation(1)}
                  icon={<ArrowRightOutlined />}
                  disabled={loading}
                  title="Next Konto"
                />
              </>
            }
          />
          <ProFormText
            name={'account_name'}
            label="Kontobezeichung"
            width={'sm'}
            placeholder={'Kontobezeichung'}
          />
          <ProFormSelect
            name={['lo_account', 'category']}
            label="Category"
            width={'xs'}
            placeholder={'Category'}
            allowClear
            options={categories}
            addonAfter={
              <>
                <Button
                  onClick={() => hanldeAccountCategoryNavigation(-1)}
                  icon={<ArrowLeftOutlined />}
                  title="Previous category"
                  disabled={loading}
                />
                <Button
                  onClick={() => hanldeAccountCategoryNavigation(1)}
                  icon={<ArrowRightOutlined />}
                  disabled={loading}
                  title="Next conto"
                />
              </>
            }
          />
          <ProFormCheckbox
            name="noCategory"
            label="No Category"
            tooltip="No Category + Konto is 4 digit and starts with 2, 3 or 4."
          />
        </ProForm>
      </Card>
      <ProTable<API.LoSusa, API.PageParams>
        headerTitle={'SuSa list'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        size="small"
        params={{ pageSize: 50000 }}
        toolBarRender={() => [
          <Space key="new">
            {/* <ProFormUploadButton
              max={1}
              name="files"
              title="Select File"
              accept=".xls,.xlsx,.csv"
              required
              width={180}
              fileList={fileList}
              fieldProps={{
                beforeUpload: (file) => {
                  setFileList([file]);
                  return false;
                },
                onRemove(file) {
                  setFileList([]);
                },
                itemRender: (orgNode, file, fileListParam, actions) => (
                  <Row style={{ width: 150, fontSize: 12 }} wrap={false}>
                    <Col flex="auto">
                      <Typography.Text ellipsis>{file.name}</Typography.Text>
                    </Col>
                    <Col flex="20px">
                      <DeleteOutlined onClick={() => actions.remove()} />
                    </Col>
                  </Row>
                ),
              }}
              buttonProps={{ type: 'primary' }}
              formItemProps={{ style: { marginBottom: 0 } }}
            /> */}
            <Button
              type="primary"
              icon={<FileExcelOutlined />}
              onClick={() => setOpenImportModal(true)}
              title="Import by XLS or CSV"
            >
              CSV Import
            </Button>
            <div>or</div>
            <Button
              type="primary"
              ghost
              icon={<PlusOutlined />}
              onClick={() => {
                setOpenPreviewModal(true);
              }}
            >
              Paste & Import
            </Button>
          </Space>,
        ]}
        scroll={{ x: 800 }}
        pagination={false}
        /* pagination={{
          showSizeChanger: true,
          pageSize: DEFAULT_PER_PAGE_PAGINATION,
        }} */
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_lo_susa', searchFormValues);

          let newSort = { ...sort };
          if (
            !searchFormValues.account_no &&
            !searchFormValues.date_to &&
            !searchFormValues.account_name &&
            Object.keys(sort).length < 1
          ) {
            newSort = {
              ym: 'descend',
              account_name: 'ascend',
            };
          }

          return getLoSusaList({ ...params, ...searchFormValues }, newSort, filter).then((res) => {
            return res;
          });
        }}
        onRequestError={(err) => Util.error(err)}
        columns={columns}
        tableAlertRender={false}
      />
      <CreateForm
        modalVisible={openPreviewModal}
        handleModalVisible={setOpenPreviewModal}
        onSubmit={async (value) => {
          actionRef.current?.reload();
        }}
      />
      <ImportModalForm
        modalVisible={openImportModal}
        handleModalVisible={setOpenImportModal}
        onSubmit={async (value) => {
          actionRef.current?.reload();
          setOpenImportModal(false);
        }}
      />
      <UpdateAccountDataForm
        initialValues={valuesInUpdateForm}
        modalVisible={openUpdateAccountModal}
        handleModalVisible={setOpenUpdateAccountModal}
        onSubmit={async (value) => {
          if (currentRow && value) {
            setCurrentRow((prev) => ({ ...prev, lo_account: { ...prev?.lo_account, ...value } }));
          }
          actionRef.current?.reload();
          setOpenUpdateAccountModal(false);
        }}
        categories={categories}
        loadAccountCategories={loadAccountCategories}
      />
    </PageContainer>
  );
};

export default LoSusaListPage;
