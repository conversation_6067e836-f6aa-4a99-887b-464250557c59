/* eslint-disable */
import Util from '@/util';
import { request } from 'umi';
import { paramsSerializer } from './api';

/** user GET /api/user */
export async function users(params: API.PageParams, sort: any, filter: any) {
  return request<API.BaseResult>('/api/users-list', {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      // sort_fields: Object.keys(sort).join(','),
      // sort_dirs: Object.values(sort).join(','),
      sort_detail: JSON.stringify(sort),
      filter_detail: JSON.stringify(filter),
    },
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** Put PUT /api/users */
export async function updateUser(data: API.UserListItem, options?: { [key: string]: any }) {
  return request<API.UserListItem>('/api/users/' + data.user_id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** Put PUT /api/users/{id}/partial */
export async function updateUserPartial(
  id?: number,
  data?: Partial<API.UserListItem>,
): Promise<API.CurrentUser> {
  return request<API.UserListItem>(`/api/users/${id}/partial`, {
    method: 'PUT',
    data: data,
  });
}

/** post POST /api/users-list */
export async function addUser(data: API.UserListItem, options?: { [key: string]: any }) {
  return request<API.UserListItem>('/api/users-list', {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/user */
export async function removeUser(options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/users-list/' + (options ? options['user_id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** user GET /api/users-list */
export async function getUsersACList(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.Result<API.UserListItem>>('/api/users-list', {
    method: 'GET',
    params: {
      ...params,
      perPage: 500,
      page: params.current,
      sort_detail: JSON.stringify(sort),
      filter_detail: JSON.stringify(filter),
    },
    withToken: true,
  }).then((res) =>
    res.message?.data?.map((x: API.UserListItem) => ({
      value: x.user_id,
      label: `${x.username} - ${x.initials ?? Util.sInitials(x.name ?? '')}`,
    })),
  );
}
