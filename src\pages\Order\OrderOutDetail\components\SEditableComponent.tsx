import { EditOutlined } from '@ant-design/icons';
import { Button } from 'antd';

type SEditableComponentProps = {
  defaultIsEditing?: boolean;
  renderEdit: () => void;
};

const SEditableComponent: React.FC<SEditableComponentProps> = ({
  defaultIsEditing,
  renderEdit,
  children,
}) => {
  // const [isEditing, setIsEditing] = useState<boolean>(false);

  return (
    <div>
      {children}
      <Button
        type="link"
        title="Select customer"
        size="small"
        icon={<EditOutlined />}
        onClick={() => {}}
      />
    </div>
  );
};
export default SEditableComponent;
