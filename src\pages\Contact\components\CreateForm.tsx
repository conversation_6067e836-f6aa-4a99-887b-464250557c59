import type { Dispatch, SetStateAction } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { addContact } from '@/services/app/Sys/contact';
import { message } from 'antd';
import Util from '@/util';
import ContactPersonFormPartial from './ContactPersonFormPartial';
import { ContactType } from '@/constants';

const handleAdd = async (fields: API.Contact) => {
  const hide = message.loading('Adding...');
  const data = { ...fields };
  try {
    await addContact(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

// eslint-disable-next-line @typescript-eslint/ban-types
export type FormValueType = {};

export type CreateFormProps = {
  values?: Partial<API.Contact>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Contact) => Promise<boolean | void>;

  customer_id?: number;
  supplier_id?: number;
  address_id?: number;
  contact_type: ContactType;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const {
    modalVisible,
    handleModalVisible,
    onSubmit,
    customer_id,
    supplier_id,
    contact_type,
    address_id,
  } = props;
  const formRef = useRef<ProFormInstance>();

  return (
    <ModalForm
      title={'New contact'}
      width="500px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ flex: '0 0 120px' }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd({
          ...value,
          customer_id,
          supplier_id,
          address_id,
          contact_type,
        } as API.Contact);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ContactPersonFormPartial contact_type={ContactType.SUPPLIER_CONTACT} />
    </ModalForm>
  );
};

export default CreateForm;
