/* eslint-disable */
import { DateRangeType } from '@/util';
import { RequestConfig, request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/lo-susa';

/** rule GET /api/lo-susa */
export async function getLoSusaList(
  params: API.PageParams & Partial<API.LoSusa>,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.LoSusa>> {
  return request<API.Result<API.LoSusa>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    sqls: res.sqls,
  }));
}


/** 
 * GET /api/lo-susa/monthly-diff-report 
 * 
 * Get  the monthly diff lists.
 * */
export async function getLoSusaMonthlyDiffList(params: API.PageParams & Partial<{ months?: DateRangeType[] }>): Promise<API.PaginatedResult<API.LoSusa> & { sqls?: string[] }> {
  return request<API.ResultObject<any>>(`${urlPrefix}/monthly-diff-report`, {
    method: 'GET',
    params,
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message,
    success: true,
    total: 1,
    sqls: res.sqls
  }));
}

/** 
 * GET /api/lo-susa/ 
 * 
 * Get  the monthly diff lists.
 * */
export async function getLoSusaMonthlySummaryReport(params: API.PageParams & Partial<{ months?: DateRangeType[] }>): Promise<API.PaginatedResult<API.LoSusa>> {
  return request<API.ResultObject<any>>(`${urlPrefix}/monthly-summary-report`, {
    method: 'GET',
    params,
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.details,
    success: true,
    total: 1,
  }));
}

/** 
 * GET /api/lo-susa/next-account-no
 * 
 * Get next account No by current accountNo and direction.
 
*/
export async function getNextAccountNo(dir: number, currentUid?: string): Promise<number> {
  return request<API.ResultObject<number>>(`${urlPrefix}/next-account-no`, {
    method: 'GET',
    params: {
      dir,
      uid: currentUid,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}


/** 
 * GET /api/lo-susa/next-account-no
 * 
 * Get next account No by current accountNo and direction.
 
*/
export async function getNextAccountCategory(dir: number, currentUid?: string): Promise<number> {
  return request<API.ResultObject<number>>(`${urlPrefix}/next-account-category`, {
    method: 'GET',
    params: {
      dir,
      uid: currentUid,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}






/** POST POST /api/lo-susa/bulk */
export async function createLoSusaBulk(
  data: { data?: string },
  options?: { [key: string]: any },
) {
  return request<API.LoSusa>(`${urlPrefix}/bulk`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}


/** POST POST /api/lo-susa/check-existence */
export async function checkExistenceData(
  data: { date?: string; category?: string; },
  options?: { [key: string]: any },
) {
  return request<API.ResultObject<boolean>>(`${urlPrefix}/check-existence`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  }).then(res => res.message);
}

/** 
 * PUT /api/lo-susa/{account_no}/account 
 * 
 * */
export async function updateLoAccount(account_no: number, data: Partial<API.LoAccount>, options?: { [key: string]: any },
): Promise<API.LoAccount> {
  return request<API.ResultObject<API.LoAccount>>(`${urlPrefix}/${account_no}/account`, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then(res => res.message);
}

/** 
 * POST /api/lo-susa/import 
 * 
 */
export async function importLoSusa(
  data?: FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'POST',
    ...(options || {}),
  };

  config.body = data;

  return request<any>(`${urlPrefix}/import`, config);
}





/** 
 * GET /api/lo-susa/accounts 
 * 
 * Get Lo Accounts lists.
 * */
export async function getLoAccountsList(params: API.PageParams): Promise<API.PaginatedResult<API.LoSusa>> {
  return request<API.ResultObject<any>>(`${urlPrefix}/accounts`, {
    method: 'GET',
    params,
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** 
 * GET /api/lo-susa/accounts/planning 
 * 
 * Get Lo Account planning list.
 * */
export async function getLoAccountPlanningList(params: API.PageParams): Promise<API.PaginatedResult<API.LoSusa>> {
  return request<API.ResultObject<any>>(`${urlPrefix}/accounts/planning`, {
    method: 'GET',
    params,
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** 
 * POST /api/lo-susa/accounts/planning 
 * 
 * create or update Lo Account planning.
 * */
export async function createOrUpdateLoAccountPlanning(data: API.LoAccountPlanning) {
  return request<API.ResultObject<API.LoAccountPlanning>>(`${urlPrefix}/accounts/planning`, {
    method: 'POST',
    data,
    withToken: true,
  }).then((res) => res.message);
}