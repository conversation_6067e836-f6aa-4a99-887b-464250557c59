import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef } from 'react';
import { message } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import { ProFormText, ModalForm } from '@ant-design/pro-form';
import { updateEmailTemplate } from '@/services/app/Email/email-template';
import Util from '@/util';
import HtmlEditor from '@/components/HtmlEditor';
import MappingDefines from './MappingDefines';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...', 0);

  try {
    await updateEmailTemplate(fields);
    message.success('Update is successful');
    return true;
  } catch (error) {
    Util.error('Update failed, please try again!');
    return false;
  } finally {
    hide();
  }
};

export type FormValueType = Partial<API.EmailTemplate>;

export type UpdateFormProps = {
  initialValues?: Partial<API.EmailTemplate>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.EmailTemplate) => Promise<boolean | void>;
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (formRef && formRef.current) {
      const newValues = { ...(props.initialValues || {}) };
      formRef.current.setFieldsValue(newValues);
    }
  }, [formRef, props.initialValues]);

  return (
    <ModalForm
      title={
        <>
          <span>Update Email template</span>
          <span>
            &nbsp;
            <MappingDefines />
          </span>
        </>
      }
      width="800px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      labelAlign="left"
      initialValues={props.initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate({ ...value, id: props.initialValues?.id });

        if (success) {
          props.handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
          formRef.current?.resetFields();
        }
      }}
    >
      <ProFormText name="subject" label="Subject" />
      <ProForm.Item name={'text_html'} label="Body" style={{ width: '100%' }}>
        <HtmlEditor initialFocus enableTextModule height={400} />
      </ProForm.Item>
    </ModalForm>
  );
};

export default UpdateForm;
