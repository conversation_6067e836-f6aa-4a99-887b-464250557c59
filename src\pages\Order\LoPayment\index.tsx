import { But<PERSON>, Card, message, Modal, Popconfirm, Space, Typography } from 'antd';
import React, { useState, useRef } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { nf2, sn } from '@/util';

import {
  deleteLoPayment,
  getLoPaymentList,
  updateCustomerOrSupplierID,
  updateLoPaymentPartial,
} from '@/services/app/LexOffice/lo-payment';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import { PlusOutlined } from '@ant-design/icons';
import CreateForm from './components/CreateForm';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import { ProFormCheckbox } from '@ant-design/pro-form';
import ProForm, { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { getCustomerACList, getCustomerList } from '@/services/app/BasicData/customer';
import EditableCell from '@/components/EditableCell';
import { getSupplierACList } from '@/services/app/BasicData/supplier';
import SDatePicker from '@/components/SDatePicker';
import SProFormDigit2 from '@/components/SProFormDigit2';
import SProFormSelect from '@/components/SProFormSelect';
import CreateBalanceForm from './components/CreateBalanceForm';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons/lib/icons';
import UpdateBalanceForm from './components/UpdateBalanceForm';

/**
 *  bulk update customer ID.
 *
 * @param selectedRows
 */
const handleUpdateCustomerOrSupplierId = async (
  selectedRows: API.LoPayment[],
  customerId?: number,
  isSupplier?: boolean,
) => {
  const hide = message.loading(isSupplier ? 'Updating supplier...' : 'Updating customer...', 0);
  if (!selectedRows) return true;

  try {
    await updateCustomerOrSupplierID(
      selectedRows.map((row) => row.id as number),
      customerId,
      isSupplier,
    );
    hide();
    message.success('Updated successfully!');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

const LoPaymentListPage: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();
  const rowRef = useRef<API.LoPayment>({}); // used for note update

  const [customerId, setCustomerId] = useState<number>();
  const [selectedRowsState, setSelectedRows] = useState<API.LoPayment[]>([]);
  const [openCreateModal, setOpenCreateModal] = useState<boolean>(false);
  // Balance entries
  const [openCreateBalanceModal, setOpenCreateBalanceModal] = useState<boolean>(false);
  const [openUpdateBalanceModal, setOpenUpdateBalanceModal] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.LoPayment>();

  const columns: ProColumns<API.LoPayment>[] = [
    /* 
    // Disabled since @2023-06-23
    {
      title: '',
      dataIndex: 'action-check',
      sorter: false,
      width: 20,
      render(__, record) {
        return (
          <Typography.Link
            onClick={() => {
              const hide = message.loading('Updating checked status', 0);
              const updatedData = { is_checked: record.is_checked == 1 ? 0 : 1 };
              updateLoPaymentPartial(sn(record.id), updatedData)
                .then((res) => {
                  actionRef.current?.reload();
                })
                .catch((err) => Util.error(err))
                .finally(() => hide());
            }}
          >
            {!record.is_checked ? (
              <CheckSquareOutlined
                style={{ color: 'lightgrey', opacity: 0.4 }}
                title="Make checked."
              />
            ) : (
              <CheckSquareOutlined style={{ color: '#07c807' }} title="Make unchecked." />
            )}
          </Typography.Link>
        );
      },
    }, */
    {
      title: 'Wertstellung',
      sorter: true,
      dataIndex: 'date',
      valueType: 'date',
      search: false,
      width: 100,
      defaultSortOrder: 'descend',
      className: 'text-13',
      render: (dom, record) => Util.dtToDMY(record.date),
    },
    {
      title: 'Empfänger / Auftraggeber',
      dataIndex: 'actor',
      width: 150,
      sorter: true,
      className: 'text-13',
    },
    {
      title: 'Customer',
      dataIndex: ['customer_id'],
      width: 150,
      sorter: true,
      className: 'text-13',
      render: (dom, record) => {
        const customerName =
          record.customer_id == 0
            ? '_None'
            : record.customer
            ? `${record.customer?.name}${
                record.customer?.description
                  ? ` - ${record.customer?.description.substring(0, 20)}`
                  : ''
              }`
            : null;
        return (
          <EditableCell
            dataType="select"
            defaultValue={{
              value: record.customer_id ? `${record.customer_id}` : null,
              label: customerName,
            }}
            convertValue={(value) => `${value ?? ''}`}
            request={async (params) => {
              return getCustomerACList({ ...params, pageSize: 200, page: 1 }).then((res) => [
                ...res.map((x) => ({ ...x, value: `${x.value}` })),
                { value: 0, label: '_None' },
              ]);
            }}
            isTabSelectable
            triggerUpdate={function (
              value: any,
              cancelEdit?: (() => void) | undefined,
            ): Promise<void> {
              return handleUpdateCustomerOrSupplierId([record], value, false)
                .then(() => {
                  actionRef.current?.reload();
                  cancelEdit?.();
                })
                .catch((e) => Util.error(e));
            }}
          >
            {customerName}
          </EditableCell>
        );
      },
    },
    {
      title: 'Supplier',
      dataIndex: ['supplier_id'],
      width: 150,
      sorter: true,
      className: 'text-13',
      render: (dom, record) => {
        const name =
          record.supplier_id == 0
            ? '_None'
            : record.supplier
            ? `${record.supplier?.name}${
                record.supplier?.description
                  ? ` - ${record.supplier?.description.substring(0, 20)}`
                  : ''
              }`
            : null;
        return (
          <EditableCell
            dataType="select"
            defaultValue={{
              value: record.supplier_id ? `${record.supplier_id}` : null,
              label: name,
            }}
            convertValue={(value) => `${value ?? ''}`}
            request={async (params) => {
              return getSupplierACList({ ...params, pageSize: 200, page: 1 }).then((res) => [
                ...res.map((x) => ({ ...x, value: `${x.value}` })),
                { value: 0, label: '_None' },
              ]);
            }}
            isTabSelectable
            triggerUpdate={function (
              value: any,
              cancelEdit?: (() => void) | undefined,
            ): Promise<void> {
              return handleUpdateCustomerOrSupplierId([record], value, true)
                .then(() => {
                  actionRef.current?.reload();
                  cancelEdit?.();
                })
                .catch((e) => Util.error(e));
            }}
          >
            <Typography.Text ellipsis>{name}</Typography.Text>
          </EditableCell>
        );
      },
    },
    {
      title: 'Verwendungszweck',
      dataIndex: 'note',
      className: 'text-13',
      ellipsis: true,
      width: 150,
    },
    {
      title: 'Betrag ',
      dataIndex: 'amount',
      ellipsis: true,
      align: 'right',
      width: 100,
      className: 'text-13',
      render: (dom, record) => <span className="text-sm2">{nf2(record.amount)}</span>,
    },
    {
      title: 'Type',
      dataIndex: 'type',
      ellipsis: true,
      className: 'text-13 c-grey',
      width: 100,
      render: (dom, record) => (
        <>
          {record.type == 2 ? (
            <>
              Balance{' '}
              <EditOutlined
                className="cursor-pointer"
                onClick={(e) => {
                  setCurrentRow(record);
                  setOpenUpdateBalanceModal(true);
                }}
              />
            </>
          ) : (
            ''
          )}
        </>
      ),
    },
    {
      title: 'Note',
      dataIndex: 'note2',
      sorter: false,
      tooltip: 'Click to edit',
      className: 'text-13 c-grey',
      ellipsis: true,
      width: 200,
      onCell: (record) => {
        return {
          className: 'cursor-pointer',
          onClick: () => {
            const modal = Modal.confirm({
              title: 'Update note',
              icon: false,
              content: (
                <>
                  <ProFormTextArea
                    placeholder={'Enter your note'}
                    fieldProps={{
                      defaultValue: record.note2,
                      onChange: (e: any) => {
                        rowRef.current = { ...rowRef.current, note2: e.target.value };
                      },
                      autoFocus: true,
                    }}
                  />
                </>
              ),
              onOk: (close) => {
                const hide = message.loading('Updating note...', 0);
                modal.update({ okButtonProps: { disabled: true } });
                updateLoPaymentPartial(sn(record.id), { note2: rowRef.current?.note2 })
                  .then((res) => {
                    actionRef.current?.reload();
                    close();
                  })
                  .catch((err) => {
                    Util.error(err);
                    modal.update({ okButtonProps: { disabled: false } });
                  })
                  .finally(() => hide());
              },
            });
          },
        };
      },
    },
    {
      title: '',
      dataIndex: 'option',
      valueType: 'option',
      width: 40,
      render: (_, record) => [
        <Popconfirm
          key="delete"
          className="c-red"
          title={<>Are you sure you want to delete?</>}
          okText="Yes"
          cancelText="No"
          overlayStyle={{ width: 300 }}
          onConfirm={async () => {
            const hide = message.loading('Deleting a payment...', 0);
            deleteLoPayment(record.id)
              .then(() => {
                actionRef.current?.reload();
                message.success('Deleted successfully.');
              })
              .catch((reason) => Util.error(reason))
              .finally(() => hide());
          }}
        >
          <Button type="link" ghost danger style={{ marginRight: 48 }}>
            <DeleteOutlined />
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  return (
    <PageContainer>
      <Card className="sf-card" style={{ marginBottom: 16 }}>
        <ProForm<API.LoPayment>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_lo_payment', {
            no_allocation: true,
          })}
          submitter={{
            submitButtonProps: {
              htmlType: 'submit',
            },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => actionRef.current?.reload(),
            render: (form, dom) => {
              return [...dom];
            },
          }}
        >
          <SDatePicker
            name="date_from"
            label="Date"
            placeholder="From"
            width={120}
            addonAfter="~"
          />
          <SDatePicker name="date_to" placeholder="To" width={120} />
          <ProFormText name={'actor'} label="Empfänger" width={120} placeholder={'Empfänger'} />
          <SProFormSelect
            name="customer_id"
            label="Customer"
            width={170}
            mode="single"
            placeholder={'Customer'}
            fieldProps={{ onChange: () => actionRef.current?.reload() }}
            request={(params) =>
              getCustomerACList(params).then((res) => [...res, { value: 0, label: '_None' }]) as any
            }
            onTabKeyCallback={(value) => {
              searchFormRef.current?.setFieldValue('customer_id', +value);
              actionRef.current?.reload();
            }}
          />
          <SProFormSelect
            name="supplier_id"
            label="Supplier"
            width={170}
            mode="single"
            placeholder={'Supplier'}
            fieldProps={{ onChange: () => actionRef.current?.reload() }}
            request={(params) =>
              getSupplierACList(params).then((res) => [...res, { value: 0, label: '_None' }]) as any
            }
            onTabKeyCallback={(value) => {
              searchFormRef.current?.setFieldValue('supplier_id', +value);
              actionRef.current?.reload();
            }}
          />
          <ProFormText
            name={'note'}
            label="Verwendungszweck"
            width={120}
            placeholder={'Verwendungszweck'}
          />
          <SProFormDigit2
            name="amount_from"
            label="Betrag"
            addonAfter="~"
            width={110}
            zeroShow
            fieldProps={{ precision: 2 }}
          />
          <SProFormDigit2 name="amount_to" width={110} zeroShow fieldProps={{ precision: 2 }} />
          <ProFormCheckbox
            name="no_allocation"
            label="No allocation?"
            fieldProps={{ onChange: (e) => actionRef.current?.reload() }}
          />
        </ProForm>
      </Card>
      <ProTable<API.LoPayment, API.PageParams>
        headerTitle={'Payments list'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        size="small"
        toolBarRender={() => [
          <Button
            type="primary"
            key="new"
            icon={<PlusOutlined />}
            onClick={() => {
              setOpenCreateModal(true);
            }}
          >
            Paste & Import
          </Button>,
          <Button
            type="primary"
            key="new-balance"
            icon={<PlusOutlined />}
            ghost
            onClick={() => {
              setOpenCreateBalanceModal(true);
            }}
          >
            Create Balance
          </Button>,
        ]}
        pagination={{
          showSizeChanger: true,
          pageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        request={(params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_lo_payment', searchFormValues);
          return getLoPaymentList({ ...params, ...searchFormValues }, sort, filter).then((res) => {
            return res;
          });
        }}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        tableAlertRender={false}
        columnEmptyText=""
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              chosen{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              Payments &nbsp;&nbsp;
            </div>
          }
        >
          <Space id="customerSelectWrap">
            <ProFormSelect
              name={['customerId']}
              showSearch={true}
              fieldProps={{
                style: { width: 200 },
                filterOption: false,
                onChange: (value) => {
                  setCustomerId(value);
                },
                getPopupContainer: () =>
                  document.getElementById('customerSelectWrap') ?? document.body,
              }}
              formItemProps={{ style: { marginBottom: 0 } }}
              request={(params) =>
                getCustomerList(params, {}, {}).then((res) =>
                  res.data?.map((x) => ({
                    value: x.id,
                    label: `${x.name}${
                      x.description ? ` - ${x.description.substring(0, 20)}` : ''
                    }`,
                  })),
                )
              }
            />
            <Button
              type="primary"
              onClick={async () => {
                await handleUpdateCustomerOrSupplierId(selectedRowsState, customerId);
                setSelectedRows([]);
                actionRef.current?.reloadAndRest?.();
              }}
            >
              Set customer ID
            </Button>
          </Space>
        </FooterToolbar>
      )}
      <CreateForm
        modalVisible={openCreateModal}
        handleModalVisible={setOpenCreateModal}
        onSubmit={async () => {
          setOpenCreateModal(false);
          actionRef.current?.reload();
        }}
      />
      <CreateBalanceForm
        modalVisible={openCreateBalanceModal}
        handleModalVisible={setOpenCreateBalanceModal}
        onSubmit={async () => {
          setOpenCreateBalanceModal(false);
          actionRef.current?.reload();
        }}
      />
      <UpdateBalanceForm
        initialValues={currentRow ?? {}}
        modalVisible={openUpdateBalanceModal}
        handleModalVisible={setOpenUpdateBalanceModal}
        onSubmit={async () => {
          setOpenUpdateBalanceModal(false);
          actionRef.current?.reload();
        }}
      />
      {/* <UpdateCustomerIdBulkForm
        modalVisible={openUpdateCustomerIdModal}
        handleModalVisible={setOpenUpdateCustomerIdModal}
        onSubmit={async (value) => {
          setOpenUpdateCustomerIdModal(false);
          actionRef.current?.reload();
        }}
      /> */}
    </PageContainer>
  );
};

export default LoPaymentListPage;
