// import { addOrderOutWorkflowStep } from '@/services/whc-order/Order/order-out-workflow-step';
import { getSysWorkflowSectionList } from '@/services/app/Sys/sys-workflow-section';
import { getSysWorkflowStepList } from '@/services/app/Sys/sys-workflow-step';
import Util, { sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import { Col, Modal, Row, Spin, Tabs } from 'antd';
import { isArray } from 'lodash';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useMemo, useRef, useState } from 'react';
import OrderOutWorkflowStepLogList from '../../OrderOutWorkflowStepLog';
import WorkflowStepDataItem from './WorkflowStepDataItem';
import type { TabsProps } from 'antd';
import _ from 'lodash';
import { DictType } from '@/constants';
import { useModel } from 'umi';

/**
 *
 * Get order out workflow step value including default value
 *
 * NOTE: step.order_out_workflow_steps should be queried on backend!!!
 *
 */
export const getOrderOutWorkflowStepValue = (step: API.SysWorkflowStep) => {
  const field_type = step.field_type;
  const definedSteps = step.order_out_workflow_steps;
  const values =
    isArray(definedSteps) && definedSteps.length
      ? step.order_out_workflow_steps?.map((x) => x.value)
      : step.default_value || [];
  const firstValue = values?.[0];

  let fieldValue = firstValue;
  if (field_type == 'switch') {
    fieldValue = fieldValue == '1' || fieldValue == 'true';
  } else if (field_type == 'multiselect' || field_type == 'checkbox') {
    fieldValue = isArray(values) ? values : [values];
  }
  return fieldValue;
};

/**
 * Chcek if workflowStep is visible or not.
 * @param step
 * @param steps
 */
export const checkVisibleRule = (
  step: API.SysWorkflowStep,
  steps: API.SysWorkflowStep[],
  getDependencyValue?: (
    depStep: API.SysWorkflowStep,
    filterRule: API.SysWorkflowStepVisibleRule,
  ) => any,
  orderOut?: Partial<API.OrderOut>,
): boolean => {
  let isVisible = true;
  if (!step.visible_rules || !isArray(step.visible_rules) || !step.visible_rules?.length)
    return true;

  // if (step.id == 17) console.log('===============');
  for (const rule of step.visible_rules) {
    if (!rule.filters?.length) continue;

    let orGroupResult = false;
    for (const filter of rule.filters) {
      if (filter.field && filter.condition_type) {
        /* if (step.id == 17)
          console.log('** Filter', filter.field, filter.condition_type, filter.value); */
        let filterResult = false;
        const matchingType = filter.condition_type;
        let matchingValue = filter.value;

        // Get dependency step.
        const depStep = _.find(steps, { id: filter.field }) as API.SysWorkflowStep;
        // if (step.id == 18) console.log('   dep step: ', depStep, 'matching value: ', matchingValue);
        if (depStep) {
          let depValue = null;
          if (getDependencyValue) {
            depValue = getDependencyValue(depStep, filter);
          } else if (orderOut) {
            if (filter.field == DictType.OrderOutType) {
              depValue = orderOut?.type_code;
            } else if (filter.field == DictType.OrderOutCategory)
              depValue = orderOut?.categories?.[0]?.code;
            else depValue = getOrderOutWorkflowStepValue(depStep);
          }

          // if (step.id == 18) console.log('   dep value: ', depValue, matchingValue);

          if (matchingType == 'eq') {
            if (depStep.field_type == 'text' || depStep.field_type == 'textarea') {
            } else if (depStep.field_type == 'switch') {
            } else if (depStep.field_type == 'date') {
            } else if (depStep.field_type == 'radio' || depStep.field_type == 'select') {
            } else if (depStep.field_type == 'checkbox' || depStep.field_type == 'multiselect') {
              if (isArray(matchingValue)) matchingValue = matchingValue.sort();
              if (isArray(depValue)) depValue = depValue.sort();
            }
            filterResult = _.isEqual(matchingValue, depValue);
            // if (step.id == 17) console.log('   or EQ result: ', filterResult);
          } else if (matchingType == 'neq') {
            if (depStep.field_type == 'text' || depStep.field_type == 'textarea') {
            } else if (depStep.field_type == 'switch') {
            } else if (depStep.field_type == 'date') {
            } else if (depStep.field_type == 'radio' || depStep.field_type == 'select') {
            } else if (depStep.field_type == 'checkbox' || depStep.field_type == 'multiselect') {
              if (isArray(matchingValue)) matchingValue = matchingValue.sort();
              if (isArray(depValue)) depValue = depValue.sort();
            }
            filterResult = !_.isEqual(matchingValue, depValue);
          } else if (matchingType == 'null') {
            filterResult = depValue === null || depValue === undefined;
          } else if (matchingType == 'notnull') {
            filterResult = depValue !== null && depValue !== undefined;
            // if (step.id == 17) console.log('   dep value: ', depValue, matchingValue);
            // if (step.id == 17) console.log('   dep value: ', depValue, matchingValue);
            // if (step.id == 17) console.log('   or notnull result: ', filterResult);
          } else if (matchingType == 'like') {
            if (depStep.field_type == 'text' || depStep.field_type == 'textarea') {
              filterResult = (depValue || '').includes(matchingValue || '');
            } else if (depStep.field_type == 'date') {
              filterResult = (depValue || '').includes(matchingValue || '');
            } else if (depStep.field_type == 'radio' || depStep.field_type == 'select') {
              filterResult = (depValue || '').includes(matchingValue || '');
            }
          } else if (matchingType == 'nlike') {
            if (depStep.field_type == 'text' || depStep.field_type == 'textarea') {
              filterResult = !(depValue || '').includes(matchingValue || '');
            } else if (depStep.field_type == 'date') {
              filterResult = !(depValue || '').includes(matchingValue || '');
            } else if (depStep.field_type == 'radio' || depStep.field_type == 'select') {
              filterResult = !(depValue || '').includes(matchingValue || '');
            }
          } else if (matchingType == 'in') {
            if (depStep.field_type == 'text' || depStep.field_type == 'textarea') {
              filterResult = (depValue || '').includes(matchingValue || '');
            } else if (depStep.field_type == 'switch') {
              filterResult = (depValue || '').includes(matchingValue || '');
            } else if (depStep.field_type == 'date') {
              filterResult = (depValue || '').includes(matchingValue || '');
            } else if (depStep.field_type == 'radio' || depStep.field_type == 'select') {
              filterResult = (depValue || '').includes(matchingValue || '');
            } else if (depStep.field_type == 'checkbox' || depStep.field_type == 'multiselect') {
              if (isArray(matchingValue) && isArray(depValue)) {
                filterResult = _.difference(matchingValue, depValue).length === 0;
              }
            }
          } else if (matchingType == 'nin') {
            if (depStep.field_type == 'text' || depStep.field_type == 'textarea') {
              filterResult = !(depValue || '').includes(matchingValue || '');
            } else if (depStep.field_type == 'switch') {
              filterResult = !(depValue || '').includes(matchingValue || '');
            } else if (depStep.field_type == 'date') {
              filterResult = !(depValue || '').includes(matchingValue || '');
            } else if (depStep.field_type == 'radio' || depStep.field_type == 'select') {
              filterResult = !(depValue || '').includes(matchingValue || '');
            } else if (depStep.field_type == 'checkbox' || depStep.field_type == 'multiselect') {
              if (isArray(matchingValue) && isArray(depValue)) {
                filterResult = _.difference(matchingValue, depValue).length !== 0;
              }
            }
          }
        }

        orGroupResult = orGroupResult || filterResult;
        if (orGroupResult) break;
      } else {
        orGroupResult = true;
      }
    }

    isVisible = isVisible && orGroupResult;
    if (!isVisible) return isVisible;
  }

  return isVisible;
};

type WorkflowStepDataProps = {
  workflow_id: number;
  order_no: number;
  orderOut?: Partial<API.OrderOut>;
  setShowNotes?: Dispatch<SetStateAction<boolean>>;
  setRefreshTaskCommentsList?: Dispatch<SetStateAction<number>>;
};

const WorkflowStepData: React.FC<WorkflowStepDataProps> = (props) => {
  const { order_no, workflow_id, orderOut } = props;
  // App model
  const { getDictOptionsByType } = useModel('app-settings');

  const [refreshTick, setRefreshTick] = useState<number>(0); // for refreshing history table.

  const [loading, setLoading] = useState<boolean>(false);
  const [loadingSection, setLoadingSection] = useState<boolean>(false);
  const [sections, setSections] = useState<API.SysWorkflowSection[]>([]);
  const [steps, setSteps] = useState<API.SysWorkflowStep[]>([]);
  const formRef = useRef<ProFormInstance>();

  // history modal
  const [currentStepId, setCurrentStepId] = useState<number>();
  const [openHistoryModal, setOpenHistoryModal] = useState<boolean>(false);

  // default data set?
  // We disable this feature
  // const [isDefaultDataSet, setIsDefaultDataSet] = useState<boolean>(true);

  const systemSteps = useMemo(() => {
    const arr: API.SysWorkflowStep[] = [];
    arr.push({
      field_type: 'select',
      id: DictType.OrderOutType as any, // fake typing
      desc: 'SYS | Order Out Type',
      options: getDictOptionsByType(DictType.OrderOutType),
    });
    arr.push({
      field_type: 'select',
      id: DictType.OrderOutCategory as any, // fake typing
      desc: 'SYS | Order Out Category',
      options: getDictOptionsByType(DictType.OrderOutCategory),
    });
    return arr;
  }, [getDictOptionsByType]);

  const stepsAll = useMemo(() => {
    return [...steps, ...systemSteps];
  }, [steps, systemSteps]);

  useEffect(() => {
    if (!order_no) return;
    if (!sections?.length) return;

    setLoading(true);
    getSysWorkflowStepList({
      pageSize: 500,
      with: 'orderOutWorkflowSteps,latestOrderOutWorkflowStepLog',
      order_no,
      workflow_id,
    })
      .then((res) => {
        setSteps(res.data ?? []);
        const initialValues: any = {};
        let existAnyWorkflowStepInfo = false;

        (res.data ?? []).forEach((step) => {
          const field_type = step.field_type;
          const definedSteps = step.order_out_workflow_steps;
          const values =
            isArray(definedSteps) && definedSteps.length
              ? step.order_out_workflow_steps?.map((x) => x.value)
              : step.default_value || [];
          const firstValue = values?.[0];

          let fieldValue = firstValue;
          if (field_type == 'switch') {
            fieldValue = fieldValue == '1' || fieldValue === true || fieldValue === 'true';
          } else if (field_type == 'multiselect' || field_type == 'checkbox') {
            fieldValue = isArray(values) ? values : [values];
          } else if (field_type == 'radio') {
          } else if (field_type == 'daterange') {
            initialValues[`${step.id}_2`] = definedSteps?.[0]?.value2;
          }
          initialValues[`${step.id}`] = fieldValue;

          if (!existAnyWorkflowStepInfo && isArray(definedSteps) && definedSteps.length) {
            existAnyWorkflowStepInfo = true;
          }
        });
        // setIsDefaultDataSet(existAnyWorkflowStepInfo);
        formRef.current?.setFieldsValue(initialValues);
      })
      .finally(() => setLoading(false));
  }, [order_no, workflow_id, sections]);

  useEffect(() => {
    if (!workflow_id || !order_no) return;

    setLoadingSection(true);
    getSysWorkflowSectionList({
      pageSize: 500,
      workflow_id,
    })
      .then((res) => {
        setSections(res.data ?? []);
      })
      .finally(() => setLoadingSection(false));
  }, [order_no, workflow_id]);

  /**
   * Tabs
   */
  const sectionItems: TabsProps['items'] = useMemo(() => {
    return sections.map((section) => ({
      label: section.name,
      key: `${section.id}`,
      children: stepsAll
        .filter(
          // (step) => step.section_id == section.id && checkVisibleRule(step, stepsAll, orderOut),
          (step) =>
            step.section_id == section.id && checkVisibleRule(step, stepsAll, undefined, orderOut),
        )
        .map((step) => (
          <Row key={step.id} gutter={8} className="workflow">
            <Col span={step?.field_type !== 'divider' ? 22 : 24}>
              <WorkflowStepDataItem
                step={step}
                order_no={sn(order_no)}
                updateCallback={(res: any) => {
                  console.log('Update cb: ', res);
                  if ('value2' in res) {
                    formRef.current?.setFieldsValue({
                      [`${res.step_id}_2`]: res.value2,
                    });
                  } else {
                    formRef.current?.setFieldsValue({
                      [`${res.step_id}`]: res.value,
                    });
                  }

                  setSteps((prev) => {
                    const newDS = [...prev];
                    const row = _.find(newDS, { id: res.step_id }) as API.SysWorkflowStep;
                    if (row) {
                      row.latest_order_out_workflow_step_log =
                        res.step.latest_order_out_workflow_step_log;
                      row.order_out_workflow_steps = res.step.order_out_workflow_steps;
                    }
                    return newDS;
                  });

                  // request to reload comment list
                  props.setRefreshTaskCommentsList?.((prev) => prev + 1);
                }}
              />
            </Col>
            {step?.field_type !== 'divider' && (
              <Col span={2}>
                {step?.latest_order_out_workflow_step_log && (
                  <div className="text-xxs c-grey">
                    <div
                      title="View history"
                      className="cursor-pointer"
                      onClick={() => {
                        setOpenHistoryModal(true);
                        setCurrentStepId(step?.id);
                      }}
                    >
                      {Util.dtToDMYHHMM(step?.latest_order_out_workflow_step_log?.created_on)}{' '}
                      {step?.latest_order_out_workflow_step_log?.user?.username}
                    </div>
                  </div>
                )}
              </Col>
            )}
          </Row>
        )),
    }));
  }, [orderOut, order_no, props, sections, stepsAll]);

  return orderOut?.categories?.length && orderOut.type_code ? (
    <>
      <Spin spinning={loading || loadingSection} style={{ minHeight: 400 }}>
        <ProForm
          formRef={formRef}
          layout="horizontal"
          labelCol={{ span: 9 }}
          labelAlign="left"
          submitter={false}
          onValuesChange={(__) => {
            setRefreshTick((prev) => prev + 1);
          }}
        >
          <Tabs
            className="section-tabs"
            defaultActiveKey={`${sections?.[0]?.id}`}
            type="card"
            size={'small'}
            items={sectionItems}
            onChange={(activeKey?: string) => {
              const firstIndex = sections.findIndex((x) => `${x.id}` === activeKey);
              props?.setShowNotes?.(firstIndex === 0);
            }}
          />
        </ProForm>
      </Spin>
      <Modal
        width="700px"
        wrapProps={{ paddingTop: 50 }}
        open={openHistoryModal}
        onCancel={() => setOpenHistoryModal(false)}
        footer={false}
      >
        {currentStepId && order_no && (
          <OrderOutWorkflowStepLogList
            hidePageContainer
            order_no={order_no}
            step_id={currentStepId}
            open={openHistoryModal}
            refreshTick={refreshTick}
          />
        )}
      </Modal>
    </>
  ) : (
    <></>
  );
};

export default WorkflowStepData;
