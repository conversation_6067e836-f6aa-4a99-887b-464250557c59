import Util from '@/util';
import { ProFormItem } from '@ant-design/pro-form';
import type { ProFormDigitProps } from '@ant-design/pro-form/lib/components/Digit';
import { InputNumber } from 'antd';
import { useRef } from 'react';

const SProFormDigit2 = (props: ProFormDigitProps & { zeroShow?: boolean }) => {
  const newProps = { ...props /* , zeroShow: undefined, fieldProps: undefined */ };
  delete newProps.zeroShow;
  delete newProps.fieldProps;
  delete newProps.formItemProps;

  const valueRef = useRef(null);

  return (
    <ProFormItem {...newProps} style={{ ...props.formItemProps?.style }}>
      <InputNumber
        size={props.fieldProps?.size}
        placeholder={props.fieldProps?.placeholder ?? ''}
        formatter={(value) => {
          return value !== undefined &&
            value !== null &&
            value !== 0 &&
            value !== '0' &&
            value !== ''
            ? '' + Util.numberFormat(value, props.zeroShow, props?.fieldProps?.precision, true)
            : (value as any);
        }}
        onBlur={(e) => {
          props?.fieldProps?.onBlur?.(valueRef.current as any);
        }}
        onChange={(e) => {
          valueRef.current = e as any;
          props?.fieldProps?.onChange?.(e);
        }}
        controls={props.fieldProps?.controls}
        precision={props?.fieldProps?.precision}
        parser={(x) => parseFloat(`${x}`.replace(/,/, '#').replace(/\./g, '').replace(/#/, '.'))}
      />
    </ProFormItem>
  );
};

export default SProFormDigit2;
