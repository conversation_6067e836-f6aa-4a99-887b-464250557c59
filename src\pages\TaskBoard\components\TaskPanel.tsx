import { TaskBlockLayout } from '@/constants';
import { getTaskList } from '@/services/app/Task/task';
import { updateTaskBlock } from '@/services/app/Task/task-block';
import Util, { sn } from '@/util';
import {
  CompressOutlined,
  ExpandOutlined,
  PlusSquareOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import type { ProFormInstance } from '@ant-design/pro-form';
import { Button, Col, Row, Spin, Typography } from 'antd';
import type { Dispatch, MutableRefObject, SetStateAction } from 'react';
import { useCallback, useEffect, useState } from 'react';
import { Droppable } from 'react-beautiful-dnd';
import { useModel } from 'umi';
import type { onTaskClickType, onTaskPanelClickType } from '..';
import PanelLayoutActions from './Sub/PanelLayoutActions';
import Task from './Task';

type TaskPanelProps = {
  groupId: number | undefined;
  block: API.TaskBlock;
  droppableId: string; // block ID
  tasks?: API.Task[];
  setTasks: Dispatch<SetStateAction<Record<string, API.Task[]>>>;
  clsTaskList?: string;
  getItemStyle?: (isDragging: any, draggableStyle: any) => any;
  getListStyle?: (isDraggingOver: any) => any;
  onTaskClick?: onTaskClickType;
  onTaskPanelClick?: onTaskPanelClickType;
  searchFormRef?: MutableRefObject<ProFormInstance<any> | undefined>;

  // For subgroup, we pass this ID.
  parentGroupId: number;
  parentBlockId: number;
};

const TaskPanel: React.FC<TaskPanelProps> = (props) => {
  const {
    droppableId,
    tasks,
    clsTaskList,
    setTasks,
    onTaskClick,
    onTaskPanelClick,
    block,
    groupId,
    parentBlockId,
    parentGroupId,
  } = props;
  const { filters, isOrderOutHeadGroup } = useModel('task-group');
  const { setTaskContext } = useModel('task');
  const [fullMode, setFullMode] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [blockName, setBlockName] = useState(block.name);

  const reloadList = useCallback(() => {
    if (!droppableId || filters === undefined) return;
    setLoading(true);
    getTaskList({
      block_id: sn(droppableId), // block ID
      group_id: groupId,
      pageSize: 500,
      // eslint-disable-next-line @typescript-eslint/dot-notation
      filters: {
        ...filters,
        // eslint-disable-next-line @typescript-eslint/dot-notation
        keywordHead: isOrderOutHeadGroup(groupId) ? filters['keywordHead'] ?? undefined : undefined,
        userIds: filters?.userIds
          ? Object.keys(filters?.userIds).filter((x) => filters?.userIds?.[x])
          : undefined,
      },
    })
      .then((res) => {
        setTasks((prev) => ({ ...prev, [droppableId]: res.data }));
      })
      .catch((reason) => Util.error(reason))
      .finally(() => setLoading(false));
  }, [droppableId, filters, groupId, isOrderOutHeadGroup, setTasks]);

  useEffect(() => {
    reloadList();
  }, [reloadList]);

  useEffect(() => {
    if (droppableId && reloadList)
      setTaskContext((prev) => ({
        ...prev,
        reloadLists: { ...prev.reloadLists, [droppableId]: reloadList },
      }));
  }, [droppableId, reloadList, setTaskContext]);

  return (
    <Spin spinning={loading}>
      <div
        className={`panel${
          block.layout == TaskBlockLayout.LAYOUT_A || block.layout == TaskBlockLayout.LAYOUT_T_DATE
            ? ' narrow'
            : ''
        }`}
      >
        <Row className="panel-header ant-row-middle" wrap={false} gutter={8}>
          <Col flex={'auto'}>
            <div title={blockName ?? '-'}>
              <Typography.Paragraph
                editable={{
                  tooltip: 'Click to edit text',
                  onChange: (value: string) => {
                    updateTaskBlock({
                      name: value,
                      id: block.id,
                      group_id: block.group_id,
                      position: block.position,
                    })
                      .then((res) => {
                        if (res) {
                          setBlockName(res.name);
                        }
                      })
                      .catch((reason) => Util.error(reason));
                  },
                  triggerType: ['text'],
                }}
                ellipsis
              >
                {blockName ?? '-'}
              </Typography.Paragraph>
            </div>
            <div
              className="absolute c-grey text-sm italic font-normal"
              style={{ bottom: 0, left: 10 }}
            >
              ID: {block.id}
            </div>
          </Col>
          {/* <Col flex={'0 0 80px'}>
            <span className="text-sm c-grey">
              #{block.id} ({block.layout})
            </span>
          </Col> */}
          <Col flex={'0 0 140px'} style={{ textAlign: 'right' }}>
            <PanelLayoutActions blockId={block.id} layout={block.layout} />
            <Button
              size="small"
              type="link"
              style={{ paddingRight: 0, marginBottom: '1em' }}
              onClick={() => {
                reloadList();
              }}
            >
              <ReloadOutlined title="Reload" />
            </Button>
            <Button
              size="small"
              type="link"
              style={{ paddingRight: 0, marginBottom: '1em' }}
              onClick={() => {
                setFullMode((prev) => !prev);
              }}
            >
              {fullMode ? <CompressOutlined title="Compress" /> : <ExpandOutlined title="Expand" />}
            </Button>
            <Button
              size="small"
              type="link"
              style={{ paddingRight: 0, marginBottom: '1em' }}
              onClick={() => {
                onTaskPanelClick?.('create', block.id);
              }}
            >
              <PlusSquareOutlined title="Create a new task" />
            </Button>
          </Col>
        </Row>
        <Droppable droppableId={droppableId}>
          {(provided, snapshot) => (
            <>
              <div
                ref={provided.innerRef}
                className={`task-list ${snapshot.isDraggingOver ? ' dragging' : ''}${
                  fullMode ? ' full' : ''
                } ${clsTaskList || ''}`}
                {...provided.droppableProps}
              >
                {tasks?.map((task, ind) => (
                  <Task
                    key={task.id}
                    index={ind}
                    task={task}
                    block={block}
                    onTaskClick={onTaskClick}
                    droppableId={droppableId}
                    groupId={groupId}
                    parentGroupId={parentGroupId}
                    parentBlockId={parentBlockId}
                  />
                ))}
              </div>
            </>
          )}
        </Droppable>
      </div>
    </Spin>
  );
};

export default TaskPanel;
