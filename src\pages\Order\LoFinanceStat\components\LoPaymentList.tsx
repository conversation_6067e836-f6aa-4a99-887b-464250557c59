import React, { useRef, useEffect, useState } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { nf2, sn } from '@/util';

import { getLoPaymentList, updateLoPaymentPartial } from '@/services/app/LexOffice/lo-payment';
import { CheckSquareOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Modal, Typography, message } from 'antd';
import CreateBalanceForm from './CreateBalanceForm';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import useCustomerOrSupplier from './useCustomerOrSupplier';
import UpdateBalanceForm from './UpdateBalanceForm';
import { updateLoInvoiceCheck } from '@/services/app/LexOffice/lo-voucher-list';

type LoPaymentListPropsType = {
  customerOrSupplier?: Partial<(API.Customer | API.Supplier) & { uid?: string }>;
  loadFinanceStat?: () => void;
  searchFormRef?: React.MutableRefObject<ProFormInstance<any> | undefined>;
  refreshTick?: number;
  extraParams?: any;
};

const LoPaymentList: React.FC<LoPaymentListPropsType> = ({
  customerOrSupplier,
  searchFormRef,
  loadFinanceStat,
  refreshTick,
  extraParams,
}) => {
  const actionRef = useRef<ActionType>();
  const rowRef = useRef<(API.LoPayment | API.LoInvoiceCheck) & { note2?: string }>({}); // used for note update

  const { isSupplier, selectedId } = useCustomerOrSupplier(customerOrSupplier);

  useEffect(() => {
    actionRef.current?.reload();
  }, [customerOrSupplier?.uid]);

  useEffect(() => {
    if (refreshTick) {
      actionRef.current?.reload();
    }
  }, [refreshTick]);

  // Balance entries
  const [openCreateBalanceModal, setOpenCreateBalanceModal] = useState<boolean>(false);
  const [openUpdateBalanceModal, setOpenUpdateBalanceModal] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.LoPayment>();
  const [type, setType] = useState<number>();

  const columns: ProColumns<API.LoPayment>[] = [
    {
      title: '',
      dataIndex: 'action-check',
      sorter: false,
      width: 20,
      render(__, record) {
        return (
          <Typography.Link
            onClick={() => {
              const hide = message.loading('Updating checked status', 0);
              const updatedData = { is_checked: record.is_checked == 1 ? 0 : 1 };
              const funcAction: Promise<any> =
                record.lo_type == 'creditnote'
                  ? updateLoInvoiceCheck(`${record.id}`, updatedData)
                  : updateLoPaymentPartial(sn(record.id), updatedData);

              funcAction
                .then((res) => {
                  actionRef.current?.reload();
                  loadFinanceStat?.();
                })
                .catch((err) => Util.error(err))
                .finally(() => hide());
            }}
          >
            {!record.is_checked ? (
              <CheckSquareOutlined
                style={{ color: 'lightgrey', opacity: 0.4 }}
                title="Make checked."
              />
            ) : (
              <CheckSquareOutlined style={{ color: '#07c807' }} title="Make unchecked." />
            )}
          </Typography.Link>
        );
      },
    },
    {
      title: 'Wertstellung',
      sorter: true,
      dataIndex: 'date',
      valueType: 'date',
      search: false,
      width: 80,
      ellipsis: true,
      defaultSortOrder: 'descend',
      className: 'text-13',
      render: (dom, record) => Util.dtToDMY(record.date),
    },
    {
      title: 'Empfänger / Auftraggeber',
      dataIndex: 'actor',
      width: 120,
      sorter: true,
      ellipsis: true,
      className: 'text-13',
    },
    /* {
      title: 'Customer',
      dataIndex: ['customer_id'],
      width: 150,
      sorter: true,
      ellipsis: true,
      render: (dom, record) => {
        const customerName = record.customer
          ? `${record.customer?.name}${
              record.customer?.description
                ? ` - ${record.customer?.description.substring(0, 20)}`
                : ''
            }`
          : null;
        return customerName;
      },
    },
    {
      title: 'Supplier',
      dataIndex: ['supplier_id'],
      width: 150,
      sorter: true,
      ellipsis: true,
      render: (dom, record) => {
        const name = record.supplier
          ? `${record.supplier?.name}${
              record.supplier?.description
                ? ` - ${record.supplier?.description.substring(0, 20)}`
                : ''
            }`
          : null;
        return name;
      },
    }, */
    {
      title: 'Verwendungszweck',
      dataIndex: 'note',
      ellipsis: true,
      className: 'text-13',
      width: 90,
    },
    {
      title: 'Betrag ',
      dataIndex: 'amount',
      ellipsis: true,
      align: 'right',
      width: 100,
      // fixed: 'right',
      className: 'text-13',
      render(dom, record, index, action, schema) {
        return nf2(record.amount);
      },
    },
    {
      title: 'Note',
      dataIndex: 'note2',
      sorter: false,
      tooltip: 'Click to edit',
      className: 'text-13 c-grey',
      ellipsis: true,
      width: 100,
      onCell: (record) => {
        return {
          className: 'cursor-pointer',
          onClick: () => {
            const modal = Modal.confirm({
              title: 'Update note',
              icon: false,
              content: (
                <>
                  <ProFormTextArea
                    placeholder={'Enter your note'}
                    fieldProps={{
                      defaultValue: record.note2,
                      onChange: (e: any) => {
                        rowRef.current = { ...rowRef.current, note2: e.target.value };
                      },
                    }}
                  />
                </>
              ),
              onOk: (close) => {
                const hide = message.loading('Updating note...', 0);
                modal.update({ okButtonProps: { disabled: true } });

                const funcAction: Promise<any> =
                  record.lo_type == 'creditnote'
                    ? updateLoInvoiceCheck(`${record.id}`, { note: rowRef.current?.note2 })
                    : updateLoPaymentPartial(sn(record.id), { note2: rowRef.current?.note2 });

                funcAction
                  .then((res) => {
                    actionRef.current?.reload();
                    close();
                  })
                  .catch((err) => {
                    Util.error(err);
                    modal.update({ okButtonProps: { disabled: false } });
                  })
                  .finally(() => hide());
              },
            });
          },
        };
      },
    },
    {
      title: '',
      dataIndex: 'type',
      ellipsis: true,
      width: 40,
      fixed: 'right',
      align: 'center',
      className: 'text-13 c-grey p-0',
      render: (dom, record) => (
        <>
          {record.type == 2 ? (
            <>
              <EditOutlined
                className="cursor-pointer"
                onClick={(e) => {
                  setCurrentRow(record);
                  setOpenUpdateBalanceModal(true);
                }}
              />
            </>
          ) : (
            ''
          )}
        </>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.LoPayment, API.PageParams>
        headerTitle={'Payments list'}
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={{ fullScreen: false, density: false, search: false, setting: false }}
        size="small"
        search={false}
        scroll={{ x: 600 }}
        cardProps={{ bodyStyle: { padding: 0 } }}
        pagination={{
          showSizeChanger: true,
          pageSize: 10,
        }}
        params={
          {
            ...extraParams,
            customerId: isSupplier ? null : selectedId,
            supplierId: isSupplier ? selectedId : null,
            srcPage: 'financialSummary',
          } as any
        }
        request={async (params, sort) => {
          const searchFilters = searchFormRef?.current?.getFieldsValue();
          return selectedId
            ? getLoPaymentList({ ...searchFilters, ...params, type }, sort)
            : Promise.resolve([]);
        }}
        columns={columns}
        rowClassName={(record) =>
          (record.lo_status == 'voided' ? 'stroke-all' : '') +
          (record.is_checked == 1 ? ' c-grey-all' : '')
        }
        toolBarRender={() => [
          <ProFormSelect
            key="type"
            name="type"
            width={130}
            placeholder="All payments"
            formItemProps={{ style: { margin: 0 } }}
            options={[
              { value: 1, label: 'Payment' },
              { value: 2, label: 'Balance' },
            ]}
            fieldProps={{
              size: 'small',
              onChange: (v) => {
                setType(v);
                actionRef.current?.reload();
              },
            }}
          />,
          <Button
            type="primary"
            key="new-balance"
            size="small"
            icon={<PlusOutlined />}
            ghost
            onClick={() => {
              setOpenCreateBalanceModal(true);
            }}
          >
            Create Balance
          </Button>,
        ]}
        columnEmptyText=""
      />
      <CreateBalanceForm
        customerOrSupplier={customerOrSupplier}
        modalVisible={openCreateBalanceModal}
        handleModalVisible={setOpenCreateBalanceModal}
        onSubmit={async () => {
          setOpenCreateBalanceModal(false);
          loadFinanceStat?.();
          actionRef.current?.reload();
        }}
      />
      <UpdateBalanceForm
        customerOrSupplier={customerOrSupplier}
        initialValues={currentRow ?? {}}
        modalVisible={openUpdateBalanceModal}
        handleModalVisible={setOpenUpdateBalanceModal}
        onSubmit={async () => {
          setOpenUpdateBalanceModal(false);
          loadFinanceStat?.();
          actionRef.current?.reload();
        }}
      />
    </>
  );
};

export default LoPaymentList;
