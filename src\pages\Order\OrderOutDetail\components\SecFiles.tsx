import { FileCategory, LS_TOKEN_NAME } from '@/constants';
import { deleteFile } from '@/services/app/file';
import { getOrderOut } from '@/services/app/Order/order-out';
import Util from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, { ProFormUploadDragger } from '@ant-design/pro-form';
import type { UploadFile } from 'antd';
import { Modal } from 'antd';
import type { UploadChangeParam } from 'antd/lib/upload';
import { useCallback, useEffect, useRef, useState } from 'react';
import _ from 'lodash';

type SecFilesProps = {
  orderNo: number;
};

const SecFiles: React.FC<SecFilesProps> = (props) => {
  const { orderNo } = props;
  const formRef = useRef<ProFormInstance>();

  const [loading, setLoading] = useState<boolean>(false);
  const [orderOut, setOrderOut] = useState<API.OrderOut>({});
  const loadOrderOut = useCallback(() => {
    setLoading(true);
    getOrderOut(orderNo, { with: 'secFiles' })
      .then((res) => {
        setOrderOut(res);
        formRef.current?.setFieldValue('files', res && res.sec_files ? res.sec_files : []);
      })
      .catch((e) => Util.error(e))
      .finally(() => setLoading(false));
  }, [orderNo]);

  useEffect(() => {
    loadOrderOut();
  }, [loadOrderOut]);

  return (
    <>
      <ProForm<{ files?: (UploadFile & { tmp?: boolean })[] }>
        formRef={formRef}
        layout="horizontal"
        labelCol={{ span: 14 }}
        labelAlign="left"
        disabled={loading}
        onValuesChange={async (changedValues, formValues) => {
          /* console.log(changedValues);
          return; */
          /* if ('files' in changedValues && changedValues.files.length) {
            const lastFile = changedValues.files[changedValues.files.length - 1];
            if (lastFile.originFileObj) {
              const formData = new FormData();
              formData.set('order_no', `${orderOut.order_no}`);
              formData.set('file', lastFile.originFileObj);
              formData.set('category', FileCategory.CAT_ORDER_OUT_SEC_FILE);

              const hide = message.loading('Uploading...', 0);
              uploadOrderOutDetailFile(formData)
                .then((res) => {
                  message.success('Uploaded successfully.');
                  console.log(res);
                  formRef.current?.setFieldValue('files', [res]);
                  setOrderOut((prev) => ({ ...prev, detail_file_id: res.id, detail_file: res }));
                })
                .catch((e) => Util.error(e))
                .finally(() => hide());
            }
          } */
        }}
        submitter={false}
      >
        <ProFormUploadDragger
          name="files"
          label=""
          title="Select Files"
          description="Select or drag & drop files here."
          required
          icon={false}
          formItemProps={{ style: { marginBottom: 0 } }}
          fieldProps={{
            name: 'file',
            method: 'post',
            multiple: true,
            action: `${API_URL}/api/order/order-out/detail-file/upload`,
            data: {
              order_no: orderOut.order_no,
              category: FileCategory.CAT_ORDER_OUT_SEC_FILE,
            },
            headers: {
              Authorization: `Bearer ${localStorage.getItem(LS_TOKEN_NAME)}`,
            },
            iconRender: undefined,
            onChange: (info: UploadChangeParam, updateState = true) => {
              if (info.file.status == 'done') {
                const res = info.file.response.message;
                info.file.url = res.url;
                info.file.uid = res.uid;
                (info.file as any).id = res.uid;
                (info.file as any).file_name = res.file_name;
                (info.file as any).clean_file_name = res.clean_file_name;
                (info.file as any).path = res.path;
                (info.file as any).org_path = res.org_path;

                const newFiles = [...info.fileList];
                formRef.current?.setFieldsValue({ files: newFiles });
              } else if (typeof info.file.status === 'undefined') {
                const newFiles = formRef.current?.getFieldValue('files') ?? [];
                const index = _.findIndex(newFiles, { uid: info.file.uid });

                if (index > -1) newFiles.splice(index, 1);
                formRef.current?.setFieldsValue({ files: newFiles });
              }
            },
            onRemove: async (file: API.File) => {
              const { confirm } = Modal;
              return new Promise((resolve, reject) => {
                confirm({
                  title: 'Are you sure you want to delete?',
                  onOk: async () => {
                    if (file.id) {
                      await deleteFile(file.id).catch((e) => Util.error(e));
                      setOrderOut((prev) => ({
                        ...prev,
                        sec_files: prev.sec_files?.filter((x) => x.id != file.id),
                      }));
                    }
                    resolve(true);
                    return true;
                  },
                  onCancel: () => {
                    reject(true);
                  },
                });
              });
            },
          }}
        />
      </ProForm>
    </>
  );
};

export default SecFiles;
