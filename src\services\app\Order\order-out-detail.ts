/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/order/order-out-detail';

/**
 * XLS data: order_out_xls_detail_local table.
 *
 *
 * @param params
 * @param sort
 * @param filter
 * @returns
 */
/** orderOut GET /api/order/order-out-detail */
export async function getOrderOutDetailList(
  params: API.PageParams & { order_no?: number },
  sort: any,
  filter: any,
): Promise<API.PaginatedResult<API.OrderOutDetail>> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** Get Aggregation GET /api/order/order-out-detail/aggregation */
export async function getOrderOutXlsDetailLocalAggregation(
  params: API.PageParams & { order_no?: number },
): Promise<API.OrderOutDetail> {
  return request<API.ResultObject<API.OrderOutDetail>>(`${urlPrefix}/aggregation`, {
    method: 'GET',
    params,
    paramsSerializer,
    withToken: true,
  }).then((res) => res.message);
}

/** Get hs codes history lists 
GET /api/order/order-out-detail/hs-code-history */
export async function getSupplierHsCodesList(
  params: API.PageParams & { order_no?: number; supplier_id?: number },
  sort: any,
  filter: any,
): Promise<API.PaginatedResult<any>> {
  return request<API.BaseResult>(`${urlPrefix}/hs-code-history`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** put PUT /api/order/order-out-detail/{id} */
export async function updateOrderOutXlsDetailLocal(
  id?: number,
  data?: Partial<API.OrderOutDetail>,
) {
  return request<API.ResultObject<API.OrderOutDetail>>(`${urlPrefix}/${id}`, {
    method: 'PUT',
    data: data,
  });
}
