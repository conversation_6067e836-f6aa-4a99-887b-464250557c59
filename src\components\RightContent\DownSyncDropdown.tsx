/* eslint-disable @typescript-eslint/dot-notation */
import React, { useCallback } from 'react';
import { SyncOutlined } from '@ant-design/icons';
import { Menu } from 'antd';
import HeaderDropdown from '../HeaderDropdown';
import styles from './index.less';
import type { MenuInfo } from 'rc-menu/lib/interface';

export type GlobalHeaderRightProps = {
  menu?: boolean;
};

const DownSyncDropdown: React.FC<GlobalHeaderRightProps> = ({ menu }) => {
  // const { initialState, setInitialState } = useModel('@@initialState');

  const onMenuClick = useCallback((event: MenuInfo) => {
    const { key } = event;
    if (key === 'ds-store') {
      /* const hide = message.loading('Down Syncing magento store configuration info...', 0);
        dsStoreConfig()
          .then((res) => {
            setAppSettings((prev) => ({ ...prev, ...res }));
            hide();
            message.success('Successfully synced!');
          })
          .catch((e) => Util.error(e))
          .finally(() => hide());
        return; */
    }
    return;
  }, []);

  const menuHeaderDropdown = (
    <Menu className={styles.menu} selectedKeys={[]} onClick={onMenuClick} items={[]} />
  );

  return (
    <HeaderDropdown overlay={menuHeaderDropdown}>
      <span className={`${styles.action} ${styles.account}`}>
        <SyncOutlined /> &nbsp;&nbsp;
        <span className={`${styles.name} anticon`}>Down Sync</span>
      </span>
    </HeaderDropdown>
  );
};

export default DownSyncDropdown;
