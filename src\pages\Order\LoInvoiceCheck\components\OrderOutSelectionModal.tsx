import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import {
  addOrderOutToLoInvoiceCheck,
  deleteOrderOutToLoInvoiceCheck,
} from '@/services/app/LexOffice/lo-voucher-list';
import { getOrderOutList } from '@/services/app/Order/order-out';
import Util, { getLexOfficeLink, urlFull } from '@/util';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Modal, Tag, message } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useRef } from 'react';

export type OrderOutSelectionModalProps = {
  invoiceCheck: Partial<API.LoInvoiceCheck>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  orderOutNos: number[];
  setOrderOutNos: Dispatch<SetStateAction<number[]>>;
};

const OrderOutSelectionModal: React.FC<OrderOutSelectionModalProps> = (props) => {
  const { invoiceCheck, handleModalVisible, modalVisible, orderOutNos, setOrderOutNos } = props;

  const actionRef = useRef<ActionType>();

  const columns: ProColumns<API.OrderOut>[] = [
    {
      title: 'Order No',
      dataIndex: 'order_no',
      sorter: true,
      width: 80,
      defaultSortOrder: 'descend',
      render: (dom, record) => {
        return (
          <a
            href={urlFull(`/order-out-detail/${record.order_no}?task_id=${record.task?.id}`)}
            target="_blank"
            rel="noreferrer"
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: 'Customer',
      dataIndex: 'customer',
      sorter: true,
      hideInForm: true,
    },
    {
      title: 'Invoices',
      dataIndex: 'lo_order_out_invoices',
      hideInForm: true,
      hideInSearch: true,
      width: 200,
      render(dom, record, index, action, schema) {
        return record.lo_order_out_invoices?.map((x) => (
          <span key={x.lo_id}>
            <a href={getLexOfficeLink(x.lo_type, x.lo_id)} target="_blank" rel="noreferrer">
              <Tag>
                <span className={x.lo_status == 'draft' ? 'c-orange' : ''}>
                  {x.lo_no ?? x.lo_id?.substring(0, 6)}
                </span>
              </Tag>
            </a>
          </span>
        ));
      },
    },
    {
      title: 'Description',
      dataIndex: 'desc',
      sorter: true,
      hideInForm: true,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 70,
      render: (_, record) => {
        if (!record.order_no) return [];

        const isAdded = orderOutNos.findIndex((x) => x == record.order_no) >= 0;
        return [
          <Button
            key="config"
            type="default"
            size="small"
            danger={isAdded}
            onClick={() => {
              if (!record.order_no) return;
              const hide = message.loading('Updating...', 0);
              if (isAdded) {
                deleteOrderOutToLoInvoiceCheck(invoiceCheck.lo_id, {
                  orderOutNos: [record.order_no],
                })
                  .then((res) => setOrderOutNos(res.order_out_nos || []))
                  .catch((err) => Util.error(err))
                  .finally(() => hide());
              } else {
                addOrderOutToLoInvoiceCheck(invoiceCheck.lo_id, { orderOutNos: [record.order_no] })
                  .then((res) => setOrderOutNos(res.order_out_nos || []))
                  .catch((err) => Util.error(err))
                  .finally(() => hide());
              }
            }}
            title="Add or remove order out to Invoice stat"
          >
            {isAdded ? 'Remove' : 'Add'}
          </Button>,
        ];
      },
    },
  ];

  return (
    <Modal
      title={<div>Add or Remove Order Out</div>}
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      width={900}
      bodyStyle={{ padding: '0 8px' }}
    >
      <ProTable<API.OrderOut, API.PageParams>
        headerTitle={'Order Out list'}
        actionRef={actionRef}
        rowKey="order_no"
        revalidateOnFocus={false}
        cardProps={{ bodyStyle: { padding: '0 8px' } }}
        options={{ fullScreen: true }}
        size="small"
        search={{
          labelWidth: 'auto',
          searchText: 'Search',
          span: 6,
          filterType: 'query',
        }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        params={{ with: 'loOrderOutInvoices' }}
        request={getOrderOutList}
        columns={columns}
        columnEmptyText=""
      />
    </Modal>
  );
};

export default OrderOutSelectionModal;
