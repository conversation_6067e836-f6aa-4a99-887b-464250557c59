/* eslint-disable */
import { request, RequestConfig } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/sys/workflow-section';

/** rule GET /api/sys/workflow-section */
export async function getSysWorkflowSectionList(
  params: API.PageParams & Partial<API.SysWorkflowSection>,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.SysWorkflowSection>> {
  return request<API.Result<API.SysWorkflowSection>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}
/** post POST /api/sys/workflow-section */
export async function addSysWorkflowSection(
  workflowId: number,
  data: API.SysWorkflowSection | FormData,
  options?: { [key: string]: any },
): Promise<API.SysWorkflowSection> {
  const config: RequestConfig = {
    method: 'POST',
    data: {
      ...data,
      workflow_id: workflowId,
    },
    ...(options || {}),
  };
  return request<API.ResultObject<API.SysWorkflowSection>>(`${urlPrefix}`, config).then((res) => {
    return res.message;
  });
}

/** post PUT /api/sys/workflow-section/{workflowId} */
export async function updateSysWorkflowSection(
  id: number,
  data: API.SysWorkflowSection | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.SysWorkflowSection>>(`${urlPrefix}/${id}`, config).then(
    (res) => res.message,
  );
}

/** post PUT /api/sys/workflow-section/update-sort */
export async function updateSysWorkflowSectionsSort(
  data: { sortInfo: any[]; workflow_id?: number },
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };
  return request<API.BaseResult>(`${urlPrefix}/update-sort`, config);
}

/** delete DELETE /api/sys/workflow-section/{id} */
export async function deleteSysWorkflowSection(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}
