import React from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { <PERSON>, Al<PERSON>, Button, message } from 'antd';
import { exportOrderOutToTask } from '@/services/app/Order/order-out';
import { SyncOutlined } from '@ant-design/icons';
import Util from '@/util';

const Welcome: React.FC = () => {
  return (
    <PageContainer>
      <Card>
        <Alert
          message={'Welcome to dashboard!'}
          type="success"
          showIcon
          banner
          style={{
            margin: -12,
            marginBottom: 24,
          }}
        />
        <Button
          type="primary"
          key="export-task"
          title="Export Order Out into task "
          onClick={() => {
            const hide = message.loading('Syncing Order Out entries with tasks...', 0);
            exportOrderOutToTask()
              .then((res) => {
                if (res) {
                  message.success('Synced successfully.');
                }
              })
              .catch((err) => Util.error(err))
              .finally(() => hide());
          }}
        >
          <SyncOutlined /> Sync with tasks
        </Button>
      </Card>
    </PageContainer>
  );
};

export default Welcome;
