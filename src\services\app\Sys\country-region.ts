/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/country-region';

/** rule GET /api/country-region */
export async function getCountryRegionList(
  params: API.PageParams & Partial<API.CountryRegion>,
  sort?: any,
  filter?: any,
): Promise<API.PaginatedResult<API.CountryRegion>> {
  return request<API.Result<API.CountryRegion>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** put PUT /api/country-region */
export async function updateCountryRegion(
  data: Partial<API.CountryRegion>,
  options?: { [key: string]: any },
) {
  return request<API.CountryRegion>(`${urlPrefix}/` + data.id, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/country-region */
export async function addCountryRegion(data: API.CountryRegion, options?: { [key: string]: any }) {
  return request<API.CountryRegion>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** delete DELETE /api/country-region/{id} */
export async function deleteCountryRegion(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}
