/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';
import { DefaultOptionType } from 'antd/lib/select';

const urlPrefix = '/api/order/order-in';

/** rule GET /api/order/order-in */
export async function getOrderInList(
  params: API.PageParams,
  sort: any,
  filter: any,
): Promise<API.PaginatedResult<API.OrderIn>> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** put PUT /api/order/order-in */
export async function updateOrderIn(
  data: API.OrderIn & { old_order_no?: number },
  options?: { [key: string]: any },
) {
  return request<API.OrderIn>(`${urlPrefix}/` + data.old_order_no, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/order/order-in */
export async function addOrderIn(data: API.OrderIn, options?: { [key: string]: any }) {
  return request<API.OrderIn>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/order/order-in/export-as-task */
export async function exportOrderInToTask(options?: { [key: string]: any }) {
  return request<API.OrderIn>(`${urlPrefix}/export-as-task`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** delete DELETE /api/order/order-in/{id} */
export async function deleteOrderIn(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}



/** orderOut GET /api/order/order-in/acList */
export async function getOrderInACList(params: API.PageParams, sort?: any) {
  return request<API.ResultObject<(API.OrderOut & DefaultOptionType)[]>>(`${urlPrefix}/acList`, {
    method: 'GET',
    params: {
      ...params,
      sort
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => res.message);
}

export async function getSupplierFieldACList(
  params?: API.PageParams,
  sort?: Record<string, any>,
): Promise<(DefaultOptionType)[]> {
  return request<API.BaseResult>(`${urlPrefix}/acSupplierFieldList`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params?.pageSize,
      page: params?.current,
      sort
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => res.message?.map((x: any) => ({
    value: x.supplier,
    label: x.supplier,
  })));



}