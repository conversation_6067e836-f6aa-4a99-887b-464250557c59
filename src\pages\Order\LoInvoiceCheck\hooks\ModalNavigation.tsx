import React from 'react';
import type { ButtonProps } from 'antd';
import { Button, Space } from 'antd';
import { ArrowLeftOutlined, ArrowRightOutlined } from '@ant-design/icons/lib/icons';
import type { HandleNavFuncType } from './useModalNavigation';

const DefaultButtonProps: ButtonProps = {
  type: 'default',
  size: 'small',
  //   style: { width: 24, height: 24, fontSize: 12, margin: 0 },
};

export type ModalNavigationProps = {
  loId?: string;
  style?: any;
  handleNavigation?: HandleNavFuncType;
};

const ModalNavigation: React.FC<ModalNavigationProps> = ({
  loId,
  style,
  handleNavigation: handleNavigation,
}) => {
  return (
    <Space style={style} size={0}>
      <Button
        {...DefaultButtonProps}
        title="Load prev Invoice"
        onClick={() => handleNavigation?.('prev', loId)}
        icon={<ArrowLeftOutlined />}
      />
      <Button
        {...DefaultButtonProps}
        title="Load next Invoice"
        onClick={() => handleNavigation?.('next', loId)}
        icon={<ArrowRightOutlined />}
      />
    </Space>
  );
};

export default ModalNavigation;
