declare namespace API {
  type BaseResult = {
    status?: 'success' | 'error' | 'info';
    code?: number;
    message?: any;
    sqls?: any[];
    messageFlash?: Record<string, any>;
  };

  type PaginatedResult<T> = {
    data: T[];
    summary?: T;
    success: boolean;
    total: number;
  };

  type Result<T> = BaseResult & {
    message: PaginatedResult<T>;
  };

  type ResultObject<T> = Omit<BaseResult, 'message'> & {
    message: T;
  };

  type Downloadable = {
    file: string;
    sqls?: any[];
    file_url?: string;
  } & {
    url: string;
  }

  type ResultDownloadable = ResultObject<Downloadable>;

  type CurrentUser = {
    user_id?: number;
    username?: string;
    email?: string;
    name?: string;
    initials?: string;
    avatar?: string;
    status?: number;
    role?: number;
    settings?: Record<string, any>;
    created_on?: string;
    updated_on?: string;
    last_login_on?: string;
    nonce?: string;
    client_detail?: string;
    password?: string;
    confirmPassword?: string;
  };

  type Pagination = {
    totalRows: number;
    totalPages: number;
    currentPage: number;
    perPage: number;
    hasMore?: boolean;
  };

  type LoginResult = {
    Authorization?: string;
    type?: string;
  };

  type UserListItem = CurrentUser;
  type UserList = {
    data: UserListItem[];
    success: boolean;
    total: number;
  };
  type userParams = {
    page?: number;
    perPage?: number;
  };

  type PageParams = {
    current?: number;
    pageSize?: number;
    perPage?: number;
    keyword?: string;

    with?: string;
  };

  type PageParamsExt = {
    current?: number;
    pageSize?: number;
    perPage?: number;
    keyword?: string;
  } & Record<string, any>;

  // ------------------------------------------------------------------------------ //
  // XLS extra data type
  // ------------------------------------------------------------------------------ //
  type ExcelCellMeta = {
    width?: number;
    color?: string;
    fillType?: string;
    fillColor?: string;
    fillColorEnd?: string;
    dataType?: string;
    align?: string;
    header?: string;
    numberFormat?: Record<string, any> & { formatCode?: string };
  };

  type ExcelData = {
    cols?: any[];
    header?: any[];
    highestRow?: number;
    highestColumn?: number;
    col2meta?: Record<string, ExcelCellMeta>;
  };

  // ------------------------------------------------------------------------------ //
  type FileSettingSettings = {
    col2field?: Record<string, number>;
    col2field2?: Record<string, API.SysXlsColNameMap>;
    col2meta?: Record<string, ExcelCellMeta>;
    headerRowNo?: number;
    dataStartRowNo?: number;
    xlsMeta?: ExcelData;
    defaultFillings?: Record<string, any> & { order_no?: number };
  };

  type FileSetting = {
    file_id?: number;
    settings?: FileSettingSettings;
  };

  type File = {
    id?: number;
    file_name?: string;
    clean_file_name?: string;
    path?: string;
    org_path?: string;
    size?: number;
    type?: string;

    // relation
    file_setting?: FileSetting;

    // pivot
    pivot?: {
      ean_id?: number;
      file_id?: number;
      is_main?: number;
      types?: string;
      label?: string;
      position?: number;
      is_parent_file?: number;
    };
  } & UploadFile & {
    // appended URL
    url?: string;
  };

  type FileList = PaginatedResult<API.File>;

  type Customer = {
    id?: number;
    name?: string;
    description?: string;
    notes?: string;
    pallet?: string;
    default_payment_term?: string;

    addresses?: Address[];
    accounts?: CustomerAccount[];
  } & CreatorData &
    UpdaterData;

  type CustomerList = PaginatedResult<Customer>;

  type CustomerAccount = {
    customer_id?: number;
    account_no?: string;

    customer?: Customer;
  };

  type Supplier = {
    id?: number;
    name?: string;
    description?: string;
    notes?: string;
    pallet?: string;
    org_a?: string;
    org_b?: number;
    supp_supplier_id?: number;

    addresses?: Address[];
  } & CreatorData &
    UpdaterData;

  type SupplierList = PaginatedResult<Supplier>;

  type OrderIn = {
    order_no?: number;
    supplier?: string;
    desc?: string;
    lotus_notes_id?: string;
    task?: Task;
  } & CreatorData &
    UpdaterData & {
      sec_files?: File[];     // order-in-sec-file.
      scan_files?: File[];    // scan category files.
    };

  type OrderInList = PaginatedResult<OrderIn>;

  type OrderOut = {
    order_no?: number;
    customer?: string;
    desc?: string;
    lotus_notes_id?: string;
    type_code?: string;
    customer_id?: number | null;
    supplier_id?: number | null;

    introduction?: string;
    issue_date?: string;
    exp_date?: string;
    pickup?: string;
    net_weight?: number;
    gross_weight?: number;
    is_eur_statement1?: number; // 0 or 1
    restriction?: string;
    invoice_date?: string;
    shipping_date?: string;
    payment_term?: string;
    payment_term_duration?: number;
    remark?: string;
    tax_rate?: number;

    workflow_id?: number;
    detail_file_id?: number;
  } & CreatorData &
    UpdaterData & {
      // relations
      supplier_obj?: Supplier;
      customer_obj?: Customer;
      categories?: Dict[];
      addresses?: Address[];
      task?: Task;
      order_out_items?: OrderOutItem[];
      lo_order_out_invoices?: LoInvoice[];
      lo_order_out_invoices_final_invoices?: LoInvoice[];   // final invoices list
      workflow?: SysWorkflow;
      order_out_details?: OrderOutDetail[];
      detail_file?: File;
      sec_files?: File[];
      scan_files?: File[];
      order_out_head?: OrderOutHead;
    } & {
      order_out_details_sum_vk?: number;
    } & {
      order_out_workflow_steps?: API.OrderOutWorkflowStep[];  // by relation
    };

  type OrderOutList = PaginatedResult<OrderOut>;

  type OrderOutHead = {
    id?: number;
    name?: string;
    order_out_head_detail?: OrderOutHeadDetail[];
  } & CreatorData &
    UpdaterData;

  type OrderOutHeadList = PaginatedResult<OrderOutHead>;

  type OrderOutHeadDetail = {
    order_out_no?: number; // PK & FK
    order_out_head_id?: number; // FK
    settings?: Record<string | number, any>;

    order_out?: OrderOut;
    order_out_head?: OrderOutHead;
  } & CreatorData;

  type OrderOutItem = {
    id?: number | React.Key;
    order_no?: number;
    qty?: number;
    unit?: string;
    desc?: string;
    price?: number;
    parent_id?: number;
    type?: 'desc';

    order_out?: OrderOut;
    children?: OrderOutItem[];
  } & CreatorData &
    UpdaterData;

  type OrderOutItem = PaginatedResult<OrderOutItem>;

  type Task = {
    id: number;
    title?: string;
    desc?: string;
    status?: number;
    date?: string;
    settings?: Record<string, any>;
    parent_id?: number;
    ref_type?: 'OrderIn' | 'OrderOut';
    ref_id?: number;

    ref?: OrderIn | OrderOut;
    parent?: Task;
    children?: Task[];
    task_blocks?: TaskBlock[];
    task_blocks_count?: number;
    task_comments?: TaskComment[];
    children_count?: number;
    sub_task_groups?: TaskGroup[];
    sub_task_groups_count?: number;
    sub_task_groups_count_hidden?: number;  // number of hidden subgroups

    order_out_extra?: OrderOut; // an extra orderOut
    order_in_extra?: OrderIn; // an extra orderOut

    email_ref?: Email;

    users?: API.UserListItem[];

    latest_task_comment?: TaskComment; // latest task comment
  } & CreatorData &
    UpdaterData & {
      // virtual column: Group or block Id may be included!
      block_id?: number; // pivot
      group_id?: number;
      sort?: number; // pivot
      visible?: number; // pivot
    } & {
      order_out_head_id?: number;
      order_out_head_name?: string;
    } & {
      ws_date_from?: string;
      ws_date_to?: string;
      ui_type?: 'exact' | 'range' | 'range_general';
      ui_type_key?: string;
    };

  type TaskList = PaginatedResult<Task>;

  type TaskComment = {
    id: number;
    task_id?: number;
    comment?: string;

    user?: UserListItem;
    task?: Task;
  } & CreatorData &
    UpdaterData;

  type TaskCommentList = PaginatedResult<TaskComment>;

  type TaskGroup = {
    id: number;
    name?: string;
    code?: string | 'OrderIn' | 'OrderOut' | 'OrderOutHead';
    task_id?: number;
    sort?: number;
    reserved?: number;
    status?: number;
    settings?: {
      useInitials?: boolean;
      layout?: string;
      updateTaskLayout?: string;        // Update Task modal layout
      updateTaskLayoutWSId?: string;    // Update Task modal's workflow ID
      wfStepIds?: string;
    };

    parent_task?: Task;
    task_blocks?: TaskBlock[];
    users?: User[];
  } & CreatorData &
    UpdaterData;

  type TaskGroupList = PaginatedResult<TaskGroup>;


  type TaskBlockSettingsPanelMenu = { id?: string, label?: string, orgLabel?: string; destGroupId?: number, destBlockId?: number | null, position?: number };
  type TaskBlockSettings = { panelMenus?: TaskBlockSettingsPanelMenu[] };
  type TaskBlock = {
    id: number;
    group_id?: number;
    name?: string;
    position?: number;
    layout?: TaskBlockLayout;
    // settings?: Record<string, any>;
    settings?: TaskBlockSettings;

    task_group?: TaskGroup;
    tasks?: Task[];
  } & CreatorData &
    UpdaterData;

  type TaskBlockList = PaginatedResult<TaskBlock>;

  type SysNotification = {
    id: number;
    type?: 'board';
    title?: string;
    desc?: string;
    ref_id?: number;
    options?: Record<string, any>;
    status?: 0 | 1;
    start_date?: string;
    end_date?: string;
    layout?: SysNotificationLayout;
  } & CreatorData &
    UpdaterData;

  type SysNotificationList = PaginatedResult<SysNotification>;

  type SysXlsColNameMap = {
    id: number;
    table_name?: string;
    db_field?: string;
    db_field_type?: string;
    db_field_comment?: string;
    xls_column?: any[];
  } & {
    xls_column_list?: { id?: number; value?: string }[];
  };
  type SysXlsColNameMapList = PaginatedResult<SysXlsColNameMap>;

  type SysTextModule = {
    number: number;
    text?: string;
  };

  type SysTextModuleList = PaginatedResult<SysTextModule>;

  type SysAddressXlsTpl = {
    id: number;
    name?: string;
    type?: 'loading address';
    file_type?: string;
    file_name?: string;
    file_path?: string;
    settings?: Record<string, any>;

    details?: SysAddressXlsTplDetail[];
  };

  type SysAddressXlsTpl = PaginatedResult<SysAddressXlsTpl>;

  type SysAddressXlsTplDetail = {
    id: number | string;
    tpl_id?: number;
    mapping_type?: 'default' | 'custom' | 'list';
    src_field?: string;
    xls_field?: string;
    xls_field2?: string;
    is_conditional?: number;
    condition_field?: string;
    condition_type?: string;
    condition_value?: string;
    settings?: Record<string, any>;

    tpl?: SysAddressXlsTpl;
  };

  type SysAddressXlsTplDetail = PaginatedResult<SysAddressXlsTplDetail>;

  type Dict = {
    code?: string; //PK
    type?: string;
    label?: string;
    sort?: number;
    value?: any;
    status?: 0 | 1;
    settings?: Record<string, any>;
    parent_code?: string;

    casted_value?: any;
    parent?: Dict;
  } & { category_code?: string };

  type DictList = PaginatedResult<Dict>;

  type CountryRegion = {
    id?: number;
    country_code?: string;
    code?: string;
    default_name?: string;

    // relations
    country?: Country;
  };

  type CountryRegionList = PaginatedResult<CountryRegion>;

  type Country = {
    id?: number;
    code?: string;
    name?: string;
    dial_code?: string;

    // relations
    country_regions?: CountryRegion[];
  };

  type CountryList = PaginatedResult<Country>;

  type CustomerAdressType = 'invoice' | 'shipping';
  type SupplierAdressType = 'loading';
  type AdressType = CustomerAdressType | SupplierAdressType;

  type Address = {
    id?: number;
    status?: 1 | 0;
    street?: string;
    street2?: string;
    city?: string;
    region?: string;
    region_id?: number;
    postcode?: string;
    country_code?: string;
    firstname?: string;
    middlename?: string;
    lastname?: string;
    email?: string;
    telephone?: string;
    fax?: string;
    company?: string;
    company2?: string;
    cust_no?: string;
    tax_no?: string;
    vat_id?: string;
    opening_hours?: string;
    detail?: string;
    lo_cust_no?: string;
    lo_contact_id?: string;
    lo_tax_type?: string;

    // relations
    country_region?: CountryRegion;
    country?: Country;
    suppliers?: Supplier[];
  } & CreatorData &
    UpdaterData & {
      // appended fields
      full?: string;
      address_type?: string;
    };

  type AddressList = PaginatedResult<Address>;

  type Contact = {
    id?: number;
    first_name?: string;
    last_name?: string;
    email?: string;
    telephone?: string;
  };

  type ContactList = PaginatedResult<Contact>;

  type AppSettings = {
    dict?: Record<[key: string | number], Partial<Dict>>;
    users?: Partial<API.UserListItem>[];
    drSelection: Record<string, string[]>;
  };

  type LoVoucherType = 'invoice' | 'quotation' | 'order_confirmation' | 'creditnote';

  type LoInvoice = {
    id?: number;
    order_no?: number;
    lo_id?: string;
    lo_no?: string;
    lo_type?: API.LoVoucherType;
    lo_status?: 'open' | 'draft' | 'closed';
    lo_version?: number;
    lo_document_file_id?: string;
    lo_organization_id?: string;
    file_path?: string;
    file_url?: string;
    file_size?: string;
    file_type?: string;
    detail?: Record<string, any>;

    // relations
    order_out?: OrderOut;
  } & CreatorData &
    UpdaterData;

  type LoInvoiceList = PaginatedResult<LoInvoice>;

  // lo_invoice_check table on backend
  type LoInvoiceCheck = {
    lo_id?: string;
    lo_no?: string;
    lo_type?: LoVoucherType;
    lo_status?: string;
    lo_document_file_id?: string;
    lo_version?: number;
    org_customer_id?: number;
    lo_cust_no?: number;
    lo_contact_id?: string;
    lo_voucher_date?: string;
    total_gross_amount?: number;
    detail?: Record<string, any>;
    lo_created_on?: string;
    lo_updated_on?: string;
    lo_contact_name?: string;
    lo_archived?: boolean;
    lo_currency?: string;
    is_checked?: number;  // Checked status
    is_stat_checked?: number;  // Checked status in Invoice Stat.
    is_exported?: number;  // is it exported to stat table(fin_detail)
    note?: string;  // user's note
    order_out_nos?: number[];

    org_customer?: Customer;
    order_out?: OrderOut;
  } & {
    children?: any[];
  }
  type LoInvoiceCheckList = PaginatedResult<LoInvoiceCheck>;

  type LoInvoiceCheckWithFinDetailAggregation = API.LoInvoiceCheck & {
    fd_gross_total?: number;
    fd_net_total?: number;
    fd_gp_total?: number;

    fd_hs_code_no_count?: number;
    fd_net_weight_no_count?: number;
    fd_net_total_no_count?: number;
    fd_gross_total_no_count?: number;
    fd_supplier_no_count?: number;
    fd_item_no_no_count?: number; // Artikelnummer
    fd_summe_diff_count?: number; // 
    fd_ek_summe_diff_count?: number;
    fd_count?: number; // total count of fin_details
  };

  type LoPayment = {
    id?: number;
    date?: string;
    actor?: string;
    note?: string;
    category?: string;
    amount?: number;
    standing_amount?: number;
    customer_id?: number;
    supplier_id?: number;
    type?: number;  // 1: payment, 2: balance
    is_checked?: number;  // Checked status
    note2?: string; // User's note

    // relations
    customer?: Customer;
    supplier?: Supplier;
  } & { lo_status?: string, lo_type?: string };

  type LoPaymentList = PaginatedResult<LoPayment>;

  type LoSusa = {
    id?: number;
    ym?: string;
    category?: string;
    created_on?: string;
    created_by?: number;
    created_by_username?: string;
    account_no?: number;
    account_name?: string;
    eb_wert_debit?: number;
    eb_wert_credit?: number;
    sum_january_debit?: number;
    sum_january_credit?: number;
    balance_all_debit?: number;
    balance_all_credit?: number;
    balance?: number;

    lo_account?: LoAccount;
    lo_account_plannings?: LoAccountPlanning[];
  };

  type LoSusa = PaginatedResult<LoSusa>;

  type LoAccount = {
    account_no?: number;
    category?: string;
    desc?: string;
    notes?: string;

    lo_susas?: LoSusa[];
    lo_account_plannings?: LoAccountPlanning[];
  };

  type LoAccountPlanning = {
    id?: number;
    account_no?: number;
    year?: number;
    value?: number;
  };

  type FinSinfo = {
    id?: number;
    Artikelnummer?: number;
    uvp?: number;
    customs_tariff_no?: string;
    item_designation?: string;
    size?: string;
    color?: string;
    weight?: number;
  }

  type FinIventurAll = {
    Artikelnummer?: number;
    Kategorie?: string;
    Angebotsgruppe?: string;
    Herkunft?: string;
    FremdArtikelnummer?: string;
    Bezeichnung?: string;
    Anzahl?: number;
    Einheit?: string;
    Inv?: number;
    SummeInv?: number;
    EK?: number;
    SummeEK?: number;
    VK?: number;
    SonderVK?: number;
    SummeVK?: number;
    LVK?: string;
    SummeLVK?: number;
    Feld18?: string;
    WarenAlter?: number;
    Feld20?: string;
    FreigabeA?: number;
    FreigabeB?: number;
    VE?: number;
    Pal?: string;
  }

  type FinDetail = {
    id?: number;

    Field1?: string;
    Re_Nr?: number;
    voucher_no?: string;
    Kunde?: string;
    Datum?: string;
    Artikelnummer?: number;
    Beschreibung?: string;
    Gepackt?: number;
    Order?: number;
    Preis?: number;
    Summe?: number;
    EK?: number;
    EK_Summe?: number;
    Ertrag?: number;
    Kategorie?: string;

    supplier_name?: string | null;
    supplier_org_a?: string | null;
    hs_code?: string;
    net_weight?: number;
    net_unit_weight?: number;

    ek_kg_price?: number;
    ek_kg_sum?: number;
    vk_kg_price?: number;
    vk_kg_sum?: number;

    import_source?: string;
    import_source_id?: number;
    import_ref?: string;
    import_ref_id?: number;
    order_ref?: string;
    order_ref_id?: number;

    // relation
    order_ref?: OrderOut | OrderIn;
  } & { uid?: string };


  /* Email related
   * ------------------------------------------------------------------ */

  type EmailAccountSettings = {
    imapSince?: string;
  };

  type EmailAccount = {
    id?: number;
    email?: string;
    server_id?: number;
    status?: number;
    pop_type?: 'IMAP' | 'POP';
    settings?: EmailAccountSettings;
    email_server?: EmailServer;
  } & CreatorData &
    UpdaterData;

  type EmailAccountList = PaginatedResult<EmailAccount>;

  type EmailAttachment = {
    id?: string;
    org_name?: string;
    name?: string;
    ext?: string;
    mime_type?: string;
    size?: number;
    subtype?: string;
    file_path?: string;
  };

  type EmailServer = {
    id?: number;
    domain?: string;
    imap_host?: string;
    imap_port?: number;
    imap_port_ssl?: number;
    imap_ssl?: number;
    pop_host?: string;
    pop_port?: number;
    pop_port_ssl?: number;
    smtp_host?: string;
    smtp_port?: number;
    settings?: Record<string, any>;
  };

  type EmailServerList = PaginatedResult<EmailServer>;

  type Email = {
    id?: number;
    email_account_id?: number;
    message_id?: string;
    mail_id?: number;
    box?: string;
    sender?: string;
    sender_host?: string;
    sender_name?: string;
    receiver?: string;
    subject?: string;
    date?: string;
    date_str?: string;
    text_html?: string;
    text_plain?: string;
    has_attachments?: boolean;
    attachments?: EmailAttachment[];
    is_seen?: boolean;
    is_answered?: boolean;
    is_recent?: boolean;
    is_flagged?: boolean;
    is_deleted?: boolean;
    is_draft?: boolean;
    from_host?: string;
    from_name?: string;
    to?: string[];
    reply_to?: string[];
    cc?: string[];
    bcc?: string[];
    is_hidden?: number;  // Hidden status
    is_attachment_moved?: number;  // Are attachments moved to misc files directory? 
    created_on?: string;

    // relations
    email_account?: EmailAccount;
    tasks?: Task[];
  };

  type EmailList = PaginatedResult<Email>;

  type EmailTemplate = {
    id?: number;
    subject?: string;
    status?: number;
    text_html?: string;
    text_plain?: string;
  } & { text_html_short?: string };

  type EmailTemplateList = PaginatedResult<EmailTemplate>;

  type SysWorkflow = {
    id?: number;
    name?: string;
    type?: 'update task modal' | 'OrderOut Supplier';
    settings?: Record<string, any>;

  } & {
    // relations
    steps?: SysWorkflowStep[];
    sections?: SysWorkflowSection[];
  };

  type SysWorkflowList = PaginatedResult<SysWorkflow>;

  type SysWorkflowSection = {
    id?: number;
    workflow_id?: number;
    name?: string;
    position?: number;
    settings?: Record<string, any>;

    // relations
    workflow?: SysWorkflow[];
    steps?: SysWorkflowStep[];
  } & {
    parent_name?: string;
    name_alias?: string;
  };

  type SysWorkflowSectionList = PaginatedResult<SysWorkflowSection>;

  type HtmlFieldType =
    | 'number'
    | 'text'
    | 'textarea'
    | 'select'
    | 'switch'
    | 'multiselect'
    | 'checkbox'
    | 'radio'
    | 'date'
    | 'daterange'
    | 'divider';

  /* Visible rules
   * ------------------------------------------------------------------ */
  type SysWorkflowStepVisibleRuleConditionType =
    | 'eq'
    | 'neq'
    | 'null'
    | 'notnull'
    | 'like'
    | 'nlike'
    | 'in'
    | 'nin'
    | '';

  type SysWorkflowStepVisibleRule = {
    uid?: string | number | React.Key; // virtual key
    parent_id?: string | number | React.Key;

    field?: string | number | 'order out' | 'order out cat'; // normally stepId: SysWorkflowStep.id
    value?: any;
    condition_type?: SysWorkflowStepVisibleRuleConditionType;
  };

  type SysWorkflowStepVisibleRuleGroup = {
    filters?: SysWorkflowStepVisibleRule[];
  } & SysWorkflowStepVisibleRule;
  /* -------------------------------------------------------------------- */

  type SysWorkflowStep = {
    id?: number;
    workflow_id?: number;
    section_id?: number;
    status?: boolean;
    position?: number;
    desc?: string;
    field_type?: HtmlFieldType;
    options?: any[];
    default_value?: any;
    settings?: Record<string, any>;
    visible_rules?: SysWorkflowStepVisibleRuleGroup[];

    // relations
    workflow?: SysWorkflow;
    section?: SysWorkflowSection;
    // order out main workflow
    order_out_workflow_steps?: OrderOutWorkflowStep[];
    latest_order_out_workflow_step_log?: OrderOutWorkflowStepLog;
    // order out workflow per supplier 
    order_out_sup_workflow_steps?: OrderOutSupWorkflowStep[];
    latest_order_out_sup_workflow_step_log?: OrderOutSupWorkflowStepLog;
    // order out workflow per task
    task_workflow_steps?: TaskWorkflowStep[];
    latest_task_workflow_step_log?: TaskWorkflowStepLog;
  };

  type OrderOutWorkflowStep = {
    id?: number; // PK
    step_id?: number;
    order_no?: number;
    value?: any;
    value2?: any;

    // relations
    step?: SysWorkflowStep;
    order_out?: OrderOut;
  };

  type OrderOutWorkflowStepLog = {
    id?: number; // PK
    step_id?: number;
    order_no?: number;
    old_value?: any;
    new_value?: any;

    // relations
    step?: SysWorkflowStep;
    order_out?: OrderOut;
    user?: CurrentUser;
  } & CreatorData;

  type OrderOutWorkflowStepLogList = PaginatedResult<OrderOutWorkflowStepLog>;

  type OrderOutSupWorkflowStep = {
    id?: number; // PK
    step_id?: number;
    order_no?: number;
    supplier_id?: number;
    value?: any;
    value2?: any;

    // relations
    step?: SysWorkflowStep;
    order_out?: OrderOut;
    supplier?: Supplier;
  };

  type OrderOutSupWorkflowStepLog = {
    id?: number; // PK
    step_id?: number;
    order_no?: number;
    supplier_id?: number;
    old_value?: any;
    new_value?: any;

    // relations
    step?: SysWorkflowStep;
    order_out?: OrderOut;
    supplier?: Supplier;
    user?: CurrentUser;
  } & CreatorData;

  type OrderOutSupWorkflowStepLogList = PaginatedResult<OrderOutSupWorkflowStepLog>;

  type TaskWorkflowStep = Omit<OrderOutWorkflowStep, 'order_no' | 'order_out'> & {
    task_id?: number;
    task?: Task;
  }

  type TaskWorkflowStepLog = Omit<OrderOutWorkflowStepLog, 'order_no' | 'order_out'> & {
    task_id?: number;
    task?: Task;
  }
  type TaskWorkflowStepLogList = PaginatedResult<TaskWorkflowStepLog>;

  type OrderOutDetailCrm = {
    id?: number; // PK
    order_no?: number;
    author?: string;
    settings?: any;

    // relations
    order_out_detail_crm_items?: OrderOutDetailCrmItem[];
    order_out?: OrderOut;
  };

  type OrderOutDetailCrmItem = {
    id?: number; // PK
    crm_id?: number;
    desc?: string;
    hs_code?: string;
    weight?: number;

    // relations
    order_out_detail_crm?: OrderOutDetailCrm;
  };

  type OrderOutDetail = {
    id: number;
    order_no?: number;
    article_no?: string;
    article?: string;
    net_unit_weight?: number;
    net_weight?: number;
    exp?: string;
    qty?: number;
    case_qty?: number;
    box_qty?: number;
    pal?: number;
    ek_unit_price?: number;
    ek_kg_price?: number;
    ek?: number;
    vk_unit_price?: number;
    vk_kg_price?: number;
    vk?: number;
    picture_file_id?: number;
    hs_code?: string;

    //relations
    order_out?: OrderOut;
  };
  type OrderOutDetailList = PaginatedResult<OrderOutDetail>;

  /* ---------------------------------- File Manager ---------------------------------- */
  type FmCapability = {
    canDelete?: boolean;
    canRename?: boolean;
    canCopy?: boolean;
    canEdit?: boolean;
    canDownload?: boolean;
    canListChildren?: boolean;
    canAddChildren?: boolean;
    canRemoveChildren?: boolean;
  };

  type FmFile = {
    id?: string;
    name?: string;
    createdTime?: string;
    modifiedTime?: string;
    capabilities?: FmCapability;
    type?: 'dir' | 'file';
    parentId?: string;
    isDir?: boolean;
    // ancestors?: FmFile[];
  };

  /* ---------------------------------- ---------------------------------- */

  type FakeCaptcha = {
    code?: number;
    status?: string;
  };

  type LoginParams = {
    username?: string;
    password?: string;
    autoLogin?: boolean;
    type?: string;
  };

  type ErrorResponse = {
    /** business contract error code */
    errorCode: string;
    /** business misinformation */
    errorMessage?: string;
    /** Whether the business request is successful */
    success?: boolean;
  };

  type getFakeCaptchaParams = {
    phone?: string;
  };

  type CreatorData = {
    created_by?: any;
    created_on?: string;
  };
  type UpdaterData = {
    updated_by?: any;
    updated_on?: string;
  };
}
