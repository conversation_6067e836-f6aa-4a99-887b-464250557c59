import { MailOutlined } from '@ant-design/icons/lib/icons';
import { Dropdown, <PERSON>u, Popconfirm, Popover, Tag } from 'antd';
import { useModel } from 'umi';
import { TaskClickAction } from '../..';
import type { TaskProps } from '../Task';

type TaskFlagsProps = TaskProps;

const TaskFlags: React.FC<TaskFlagsProps> = (props) => {
  const { task, droppableId, index } = props;
  const { appSettings } = useModel('app-settings');
  const { getTaskGroupInModel } = useModel('task-group');

  const user = task.users?.[0];
  const isSubGroup =
    !getTaskGroupInModel(props.groupId) || !!getTaskGroupInModel(props.groupId)?.task_id;

  const useUserInitials = getTaskGroupInModel(props.groupId)?.settings?.useInitials;

  return (
    <>
      {task.email_ref && (
        <Popover
          overlayStyle={{ maxWidth: 400 }}
          content={
            <>
              <h4>{task.email_ref.subject}</h4>
              <div className="text-sm" style={{ marginBottom: 16 }}>
                {task.email_ref?.sender}
              </div>
              <div>{task.email_ref?.text_plain}</div>
            </>
          }
        >
          <MailOutlined
            className="c-grey cursor-pointer"
            style={{ fontSize: 11, marginRight: 4 }}
            onClick={(e) => {
              props?.onTaskClick?.(task, droppableId, index, e, TaskClickAction.openEmailModal);
            }}
          />
        </Popover>
      )}
      {!isSubGroup &&
        useUserInitials &&
        (user ? (
          <Popconfirm
            key={user.user_id}
            title={<>Are you sure you want to delete user?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 300, minWidth: 300 }}
            onConfirm={async () => {
              props.onTaskClick?.(
                task,
                props.droppableId,
                index,
                undefined,
                TaskClickAction.deleteUser,
                { userIds: user.user_id },
              );
            }}
          >
            <Tag
              className="c-grey cursor-pointer ant-tag-small fixed"
              style={{ width: 20 }}
              title="click to remove"
            >
              {user.initials}
            </Tag>
          </Popconfirm>
        ) : (
          <Dropdown
            trigger={['click']}
            overlay={
              <Menu
                onClick={(data) => {
                  props?.onTaskClick?.(
                    props.task,
                    props.droppableId,
                    props.index,
                    null,
                    TaskClickAction.addUser,
                    { userIds: data.key },
                  );
                }}
                items={
                  (appSettings.users?.map((u) => ({
                    key: u.user_id,
                    label: u.initials ?? u.user_id,
                  })) ?? []) as any
                }
              />
            }
          >
            <Tag
              className="c-grey cursor-pointer ant-tag-small fixed empty"
              title="click to assign"
            >
              &nbsp;
            </Tag>
          </Dropdown>
        ))}
    </>
  );
};
export default TaskFlags;
