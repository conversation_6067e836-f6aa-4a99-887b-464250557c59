/* eslint-disable react/no-array-index-key */
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { DragSortTable } from '@ant-design/pro-table';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, message, Popconfirm, Space } from 'antd';
import { DeleteOutlined, MenuOutlined, PlusOutlined } from '@ant-design/icons';
import Util, { sn } from '@/util';
import {
  addSysWorkflowStep,
  deleteSysWorkflowStep,
  getSysWorkflowStepList,
  updateSysWorkflowStep,
  updateSysWorkflowStepsSort,
} from '@/services/app/Sys/sys-workflow-step';
import EditableCell from '@/components/EditableCell';
import WorkflowStepOptionsModalForm from './WorkflowStepOptionsModalForm';
import _, { isArray } from 'lodash';
import Tag from 'antd/lib/tag';
import { YNOptionsStr } from '@/constants';
import WorkflowStepOptionsCell from './WorkflowStepOptionsCell';
import { getSysWorkflowSectionList } from '@/services/app/Sys/sys-workflow-section';
import WorkflowStepRuleGroupsForm from './WorkflowStepRuleGroupsForm';

const fieldTypeMap: Record<string, string> = {
  text: 'Text',
  textarea: 'Textarea',
  select: 'Select',
  multiselect: 'Multi Select',
  radio: 'Radio',
  checkbox: 'Checkbox',
  switch: 'Switch',
  date: 'Date',
  daterange: 'Date Range',
  divider: 'Divider',
};

const fieldTypeOptions = Object.keys(fieldTypeMap).map((x) => ({
  value: x,
  label: fieldTypeMap[x],
}));

type TableRowType = Omit<API.SysWorkflowStep, 'id'> & {
  id?: number | string;
};

type WorkflowStepListProps = {
  workflow_id: number;
  workflow_name?: string;
  workflow_settings?: Record<string, any>;
  open?: boolean;
};
const WorkflowStepList: React.FC<WorkflowStepListProps> = ({
  workflow_id,
  open,
  workflow_name,
  workflow_settings,
}) => {
  const actionRef = useRef<ActionType>();
  const [dataSource, setDatasource] = useState<TableRowType[]>([]);

  const [dataSourceSection, setDatasourceSection] = useState<API.SysWorkflowSection[]>([]);
  const sectionsOptions = useMemo(() => {
    return dataSourceSection.map((x) => ({ value: x.id, label: x.name }));
  }, [dataSourceSection]);

  // options modal form
  const [openOptionsModal, setOpenOptionsModal] = useState<boolean>(false);
  // rules modal form
  const [openRulesModal, setOpenRulesModal] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<TableRowType>();

  const columns: ProColumns<TableRowType>[] = [
    {
      title: '',
      dataIndex: 'position',
      align: 'center',
      width: 40,
    },
    {
      title: 'Section',
      dataIndex: 'section_id',
      width: 200,
      render: (dom, record) => (
        <EditableCell
          dataType="select"
          defaultValue={record.section_id}
          options={sectionsOptions}
          fieldProps={{ dropdownMatchSelectWidth: false }}
          formItemProps={{ style: { width: 175, marginBottom: 0 } }}
          triggerUpdate={async function (
            value: any,
            cancelEdit?: (() => void) | undefined,
          ): Promise<void> {
            const data: API.SysWorkflowStep = { section_id: value };

            return updateSysWorkflowStep(sn(record.id), data).then((res) => {
              // actionRef.current?.reload(),
              setDatasource((prev: TableRowType[]) => {
                const newDS = [...prev];
                const row = _.find(newDS, { id: record.id });
                if (row) {
                  row.section_id = value;
                }
                return newDS;
              });
            });
          }}
        >
          {_.get(_.find(sectionsOptions, { value: record.section_id }), 'label', '')}
        </EditableCell>
      ),
    },
    {
      title: 'Description',
      dataIndex: 'desc',
      width: 300,
      render: (dom, record) => (
        <EditableCell
          dataType="textarea"
          defaultValue={record.desc}
          triggerUpdate={function (
            value: any,
            cancelEdit?: (() => void) | undefined,
          ): Promise<void> {
            return updateSysWorkflowStep(sn(record.id), { desc: value }).then((res) =>
              actionRef.current?.reload(),
            );
          }}
        >
          <span className={`${record.field_type == 'divider' ? 'c-grey' : ''}`}>{record.desc}</span>
        </EditableCell>
      ),
    },
    {
      title: 'Field Type',
      dataIndex: 'field_type',
      width: 150,
      render: (dom, record) => (
        <EditableCell
          dataType="select"
          defaultValue={record.field_type}
          options={fieldTypeOptions}
          triggerUpdate={async function (
            value: any,
            cancelEdit?: (() => void) | undefined,
          ): Promise<void> {
            const data: API.SysWorkflowStep = { field_type: value };
            if (value == 'switch') {
              data.options = YNOptionsStr;
            } else if (value == 'radio') {
              data.options = YNOptionsStr;
            } else if (value == 'text' || value == 'textarea') {
              data.options = [];
            }
            const res = await updateSysWorkflowStep(sn(record.id), data);
            return actionRef.current?.reload();
          }}
        >
          {fieldTypeMap[record.field_type ?? 'text'] ?? '-'}
        </EditableCell>
      ),
    },
    {
      title: 'Options',
      dataIndex: 'options',
      tooltip: 'Drag & Drop sorting available',
      onCell: (record, index) => {
        if (
          record.field_type == 'text' ||
          record.field_type == 'textarea' ||
          record.field_type == 'switch' ||
          record.field_type == 'date' ||
          record.field_type == 'divider'
        ) {
          return {};
        }
        return {
          className: 'cursor-pointer',
          onClick: (e) => {
            setCurrentRow(record);
            setOpenOptionsModal(true);
          },
        };
      },
      render: (dom, record) => (
        <WorkflowStepOptionsCell
          step={record as API.SysWorkflowStep}
          onSaveCallback={async (id, options) => {
            setDatasource((prev: TableRowType[]) => {
              const newDS = [...prev];
              const row = _.find(newDS, { id });
              if (row) {
                row.options = options || [];
              }
              return newDS;
            });
          }}
        />
      ),
    },
    {
      title: 'Default value',
      dataIndex: 'default_value',
      width: 200,
      render: (dom, record) => {
        if (record.field_type == 'divider') return undefined;

        let children = record.default_value;
        if (record.field_type == 'text' || record.field_type == 'textarea') {
          children = children?.[0];
        } else if (
          record.field_type == 'radio' ||
          record.field_type == 'switch' ||
          record.field_type == 'select'
        ) {
          const optionObj = _.find(record.options, { value: children?.[0] });
          children = optionObj ? <Tag>{optionObj.label}</Tag> : undefined;
        } else {
          children = (children || []).map((x: any, index: number) => (
            <Tag key={`${x}-${index}`}>{_.get(_.find(record.options, { value: x }), 'label')}</Tag>
          ));
        }

        let dataType = record.field_type;
        if (dataType == 'radio') dataType = 'select';
        else if (dataType == 'checkbox') dataType = 'multiselect';

        return (
          <EditableCell
            dataType={dataType as any}
            defaultValue={record.default_value}
            options={record.options}
            allowClear
            triggerUpdate={function (
              value: any,
              cancelEdit?: (() => void) | undefined,
            ): Promise<void> {
              let default_value = value ? (isArray(value) ? value : [value]) : value;
              if (record.field_type == 'switch') {
                default_value = value === true || value === false ? (value ? [1] : [0]) : [];
              }
              return updateSysWorkflowStep(sn(record.id), {
                default_value,
              }).then((res) => actionRef.current?.reload());
            }}
          >
            {children}
          </EditableCell>
        );
      },
    },
    {
      title: 'Visible rules',
      dataIndex: 'visible_rules',
      width: 100,
      render: (dom, record) => {
        return (
          <>
            {record.visible_rules && record.visible_rules?.length
              ? record.visible_rules?.length
              : ''}
          </>
        );
      },
      onCell: (record, index) => {
        return {
          className: 'cursor-pointer',
          onClick: (e) => {
            setCurrentRow(record);
            setOpenRulesModal(true);
          },
        };
      },
    },
    {
      title: 'Commenting?',
      dataIndex: ['settings', 'isComment'],
      tooltip: 'Ability to log changes as a comment.',
      width: 100,
      render: (dom, record) => {
        return (
          <EditableCell
            dataType="switch"
            defaultValue={record.settings?.isComment}
            isDefaultEditing
            triggerUpdate={function (
              value: any,
              cancelEdit?: (() => void) | undefined,
            ): Promise<void> {
              return updateSysWorkflowStep(sn(record.id), {
                settings: { ...record.settings, isComment: value },
              }).then((res) => actionRef.current?.reload());
            }}
          />
        );
      },
    },
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      align: 'center',
    },
    {
      title: '',
      valueType: 'option',
      width: 50,
      render: (text, record, __, action) => (
        <Space>
          {!record.settings?.cannot_delete ? (
            <Popconfirm
              key="delete"
              className="c-red"
              title={<>Are you sure you want to delete?</>}
              okText="Yes"
              cancelText="No"
              overlayStyle={{ width: 300 }}
              onConfirm={async (e) => {
                const hide = message.loading('Deleting...', 0);
                deleteSysWorkflowStep({
                  id: record.id,
                })
                  .then((res) => {
                    actionRef.current?.reload();
                  })
                  .catch((err) => {
                    Util.error(err);
                  })
                  .finally(() => hide());
              }}
            >
              <DeleteOutlined />
            </Popconfirm>
          ) : null}
        </Space>
      ),
    },
  ];

  const handleDragSortEnd = async (newDataSource: any) => {
    const hide = message.loading('Update sort...', 0);
    const prevDS = [...dataSource];
    setDatasource(newDataSource);
    updateSysWorkflowStepsSort({
      sortInfo: newDataSource.map((x: TableRowType, index: number) => ({
        id: x.id,
        position: index + 1,
      })),
    })
      .then((res) => {})
      .catch((e) => {
        setDatasource(prevDS);
        Util.error(e);
      })
      .finally(() => hide());
  };

  const dragHandleRender = (rowData: any, idx: any) => (
    <MenuOutlined style={{ cursor: 'grab', color: '#999' }} />
  );

  const addNewRecord = async (isEnd?: boolean) => {
    if (!workflow_id) return;
    const hide = message.loading('Adding...', 0);
    const newRow: API.SysWorkflowStep = {
      workflow_id,
      position: isEnd ? dataSource.length + 1 : 1,
      field_type: 'text',
    };

    addSysWorkflowStep(sn(workflow_id), newRow)
      .then((res) => {
        setDatasource((prev) => (isEnd ? [...prev, res] : [res, ...prev]));
      })
      .catch((e) => Util.error(e))
      .finally(() => hide());
  };

  const loadStepsList = useCallback(
    async (params?: any) => {
      if (!workflow_id) return Promise.resolve({});

      const res = await getSysWorkflowStepList({ ...params, pageSize: 500, workflow_id });
      setDatasource(res.data);
      return res;
    },
    [workflow_id],
  );

  const loadSectionsList = useCallback(
    async (params?: any) => {
      if (!workflow_id) return Promise.resolve({});

      const res = await getSysWorkflowSectionList({ ...params, pageSize: 500, workflow_id });
      setDatasourceSection(res.data);
      return res;
    },
    [workflow_id],
  );

  useEffect(() => {
    if (open) {
      loadSectionsList();
    }
  }, [loadSectionsList, open]);

  useEffect(() => {
    if (open) {
      actionRef.current?.reload();
    }
  }, [open]);

  return workflow_id ? (
    <>
      <DragSortTable
        headerTitle={`Workflow steps in ${workflow_id} - ${workflow_name || 'N/A'}`}
        columns={columns}
        rowKey="id"
        actionRef={actionRef}
        search={false}
        size="small"
        pagination={false}
        dataSource={dataSource}
        dragSortKey="position"
        dragSortHandlerRender={dragHandleRender}
        onDragSortEnd={handleDragSortEnd}
        columnEmptyText=""
        toolBarRender={() => [
          <Button
            type="primary"
            key="newAfter"
            onClick={() => {
              addNewRecord(true);
            }}
            icon={<PlusOutlined />}
          >
            New
          </Button>,
        ]}
        request={(params) => {
          return loadStepsList(params);
        }}
      />
      {currentRow?.id && (
        <WorkflowStepOptionsModalForm
          modalVisible={openOptionsModal}
          handleModalVisible={setOpenOptionsModal}
          initialValues={(currentRow || {}) as API.SysWorkflowStep}
          onSaveCallback={async (id?: number, data?: Partial<API.SysWorkflowStep>) => {
            setDatasource((prev) => {
              const newDS = [...prev];
              const row = _.find(newDS, { id });
              if (row) {
                row.options = data?.options || [];
              }
              return newDS;
            });
          }}
        />
      )}

      {currentRow?.id && (
        <WorkflowStepRuleGroupsForm
          modalVisible={openRulesModal}
          handleModalVisible={setOpenRulesModal}
          initialValues={
            {
              id: currentRow.id,
              workflow_id: currentRow.workflow_id,
              visible_rules: currentRow.visible_rules,
              section_id: currentRow.section_id,
            } as API.SysWorkflowStep
          }
          onSaveCallback={async (id?: number, data?: Partial<API.SysWorkflowStep>) => {
            setDatasource((prev) => {
              const newDS = [...prev];
              const row = _.find(newDS, { id });
              if (row) {
                row.visible_rules = data?.visible_rules || [];
              }
              return newDS;
            });
          }}
        />
      )}
    </>
  ) : (
    <></>
  );
};
export default WorkflowStepList;
