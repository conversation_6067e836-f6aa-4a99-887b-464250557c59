import { EURO2 } from '@/constants';
import type { LoFinanceStatType } from '@/services/app/LexOffice/lo-finance';
import Util, { nf2, sn } from '@/util';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

type DoubleCheckCardProps = {
  data?: Partial<LoFinanceStatType>;
};

const DoubleCheckCard: React.FC<DoubleCheckCardProps> = ({ data, ...rest }) => {
  const columns: ProColumns<any>[] = [
    {
      dataIndex: 'category',
    },
    {
      dataIndex: 'sum',
      title: 'ALL',
      width: 100,
      align: 'right',
      render(dom, record, index) {
        return index == 4 ? (
          <span className={Util.isZero(record.sum) ? 'bold' : 'bold c-orange'}>
            {nf2(record.sum, true) + EURO2}
          </span>
        ) : (
          nf2(record.sum, true) + EURO2
        );
      },
    },
    {
      dataIndex: 'sum_unchecked',
      title: 'Not marked',
      width: 100,
      align: 'right',
      render(dom, record) {
        return nf2(record.sum_unchecked, true) + EURO2;
      },
    },
    {
      dataIndex: 'sum_checked',
      title: 'Marked',
      width: 100,
      align: 'right',
      render(dom, record) {
        return nf2(record.sum_checked, true) + EURO2;
      },
    },
  ];

  const datasource = [
    {
      uid: 0,
      category: 'All invoices',
      sum: data?.inv_sum,
      sum_checked: data?.inv_sum_checked,
      sum_unchecked: data?.inv_sum_unchecked,
    },
    {
      uid: 1,
      category: 'All creditnotes',
      sum: data?.creditnote_sum,
      sum_checked: data?.creditnote_sum_checked,
      sum_unchecked: data?.creditnote_sum_unchecked,
    },
    {
      uid: 2,
      category: 'All payments',
      sum: data?.payment_sum,
      sum_checked: data?.payment_sum_checked,
      sum_unchecked: data?.payment_sum_unchecked,
    },
    {
      uid: 3,
      category: 'All allocations',
      sum: data?.allocation_sum,
      sum_checked: data?.allocation_sum,
      sum_unchecked: data?.allocation_sum,
    },
    {
      uid: 4,
      category: 'Total open',
      sum:
        sn(data?.inv_sum) -
        sn(data?.creditnote_sum) -
        sn(data?.payment_sum) -
        sn(data?.allocation_sum),
      sum_checked:
        sn(data?.inv_sum_checked) -
        sn(data?.creditnote_sum_checked) -
        sn(data?.payment_sum_checked) -
        sn(data?.allocation_sum_checked),
      sum_unchecked:
        sn(data?.inv_sum_unchecked) -
        sn(data?.creditnote_sum_unchecked) -
        sn(data?.payment_sum_unchecked) -
        sn(data?.allocation_sum_unchecked),
    },
  ];

  return (
    <ProTable<any, API.PageParams>
      rowKey="uid"
      options={false}
      search={false}
      size="small"
      style={{ width: 400 }}
      bordered={false}
      cardProps={{ bodyStyle: { padding: 0 } }}
      pagination={false}
      columns={columns as any}
      dataSource={datasource}
      columnEmptyText=""
    />
  );
};
export default DoubleCheckCard;
