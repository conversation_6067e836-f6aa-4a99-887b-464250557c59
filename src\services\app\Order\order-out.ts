/* eslint-disable */
import { request, RequestConfig } from 'umi';
import { paramsSerializer } from '../api';
import { DefaultOptionType } from 'antd/lib/select';

const urlPrefix = '/api/order/order-out';

/** orderOut GET /api/order/order-out */
export async function getOrderOutList(
  params: API.PageParams & { order_nos?: number[] },
  sort: any,
  filter: any,
): Promise<API.PaginatedResult<API.OrderOut>> {
  return request<API.BaseResult>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** orderOut GET /api/order/order-out/{id} */
export async function getOrderOut(
  id?: number,
  params?: API.PageParams & { lotus_notes_id?: string },
): Promise<API.OrderOut> {
  const url = params?.lotus_notes_id ? `${urlPrefix}/lotus` : `${urlPrefix}/${id}`;

  return request<API.ResultObject<API.OrderOut>>(url, {
    method: 'GET',
    params: {
      ...params,
    },
    paramsSerializer,
    withToken: true,
  })
    .then((res) => res.message);
}

/** put PUT /api/order/order-out */
export async function updateOrderOut(
  data: API.OrderOut & { old_order_no?: number },
  options?: { [key: string]: any },
) {
  return request<API.OrderOut>(`${urlPrefix}/` + (data.old_order_no ?? data.order_no), {
    method: 'PUT',
    data: data,
    ...(options || {}),
  });
}

/** create or update PUT /api/order/order-out/lo-quotation/{orderNo} */
export async function createOrderOutQuotationInLo(
  order_no?: number,
  data?: API.OrderOut & { old_order_no?: number; action?: string; isUpdate?: boolean },
  options?: { [key: string]: any },
) {
  return request<API.BaseResult>(`${urlPrefix}/lo-quotation/` + order_no, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** put PUT /api/order/order-out/lo-order-confirmation/{id} */
export async function createOrderOutOrderConfirmationInLo(
  order_no?: number,
  data?: API.OrderOut & { old_order_no?: number; action?: string },
  options?: { [key: string]: any },
) {
  return request<API.BaseResult>(`${urlPrefix}/lo-order-confirmation/` + order_no, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** put PUT /api/order/order-out/lo-invoice/{id} */
export async function createOrderOutInvoiceInLo(
  order_no?: number,
  data?: API.OrderOut & { old_order_no?: number; action?: string },
  options?: { [key: string]: any },
) {
  return request<API.BaseResult>(`${urlPrefix}/lo-invoice/` + order_no, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/** post POST /api/order/order-out */
export async function addOrderOut(data: API.OrderOut, options?: { [key: string]: any }) {
  return request<API.OrderOut>(`${urlPrefix}`, {
    method: 'POST',
    data: data,
    ...(options || {}),
  });
}

/** post POST /api/order/order-out/export-as-task */
export async function exportOrderOutToTask(options?: { [key: string]: any }) {
  return request<API.OrderIn>(`${urlPrefix}/export-as-task`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** delete DELETE /api/order/order-out/{id} */
export async function deleteOrderOut(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/` + (options ? options['id'] : ''), {
    method: 'DELETE',
    ...(options || {}),
  });
}


/*
--------------------------------------------------------------------------------------
----------------    Create a Lex Office Contact
--------------------------------------------------------------------------------------
*/
/** create contact in Lex Office
 * 
 * PUT /api/order/order-out/create-lo-contact/{orderNo} */
export async function createContactInLo(
  order_no?: number,
  data?: API.OrderOut & { old_order_no?: number; action?: string; isUpdate?: boolean },
  options?: { [key: string]: any },
): Promise<API.Address> {
  return request<API.ResultObject<API.Address>>(`${urlPrefix}/create-lo-contact/` + order_no, {
    method: 'PUT',
    data: data,
    ...(options || {}),
  }).then((res) => res.message);
}

/*
--------------------------------------------------------------------------------------
----------------    Detail File Management
--------------------------------------------------------------------------------------
*/
/** post POST /api/order/order-out/detail-file/upload */
export async function uploadOrderOutDetailFile(
  data: (API.OrderOut & { file?: File }) | FormData,
  options?: { [key: string]: any },
): Promise<API.File> {
  const config: RequestConfig = {
    method: 'POST',
    ...(options || {}),
  };
  if (data instanceof FormData) {
    config.body = data;
  } else {
    config.data = data;
  }

  return request<API.ResultObject<API.File>>(`${urlPrefix}/detail-file/upload`, config).then(
    (res) => res.message,
  );
}

/** get GET /api/order/order-out/detail-file/xls/{fileId} */
export async function getOrderOutDetailFileFromXls(
  params: API.PageParams & { fileId?: number },
  sort: any,
  filter: any,
) {
  if (!params?.fileId) {
    throw 'FileId invalid';
  }
  return request<API.Result<API.OrderOut> & API.ExcelData>(
    `${urlPrefix}/detail-file/xls/${params.fileId}`,
    {
      method: 'GET',
      params: {
        ...params,
        perPage: params.pageSize,
        page: params.current,
        sort,
        filter,
      },
      paramsSerializer,
      withToken: true,
    },
  ).then((res) => ({
    data: res.message.rows,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,

    // extra
    xlsMeta: {
      cols: res.message.cols,
      header: res.message.header,
      highestRow: res.message.highestRow,
      highestColumn: res.message.highestColumn,
      col2meta: res.message.col2meta,
    },
  }));
}

/** import PUT /api/order/order-out/detail-file/xls/{fileId} */
export async function importOrderOutDetailFileFromXls(
  fileId?: number,
  params?: { order_no?: number; settings?: any },
) {
  return request<API.BaseResult>(`${urlPrefix}/detail-file/xls/${fileId}`, {
    method: 'PUT',
    data: {
      ...params,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => res.message);
}


/** 
 * GET /api/order/order-out/next-order-no
 * 
 * Get next orderout No by current orderout No and direction.
 
*/
export async function getNextOrderOutNo(dir: number, currentUid: number): Promise<API.OrderOut> {
  return request<API.ResultObject<API.OrderOut>>(`${urlPrefix}/next-order-no`, {
    method: 'GET',
    params: {
      dir,
      uid: currentUid,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}

/** orderOut GET /api/order/order-out/acList */
export async function getOrderOutACList(params: API.PageParams, sort?: any) {
  return request<API.ResultObject<(API.OrderOut & DefaultOptionType)[]>>(`${urlPrefix}/acList`, {
    method: 'GET',
    params: {
      ...params,
      sort
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => res.message);
}

/** orderOut GET /api/order/order-out/document */
export async function getOrderOutDocumentList(
  params: API.PageParams & { order_nos?: number[] },
  sort: any,
  filter: any,
): Promise<API.PaginatedResult<API.OrderOut>> {
  return request<API.BaseResult>(`${urlPrefix}/document`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}