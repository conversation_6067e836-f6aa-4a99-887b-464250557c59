import { PlusOutlined } from '@ant-design/icons';
import { But<PERSON>, message, Drawer, Space, Tag } from 'antd';
import React, { useState, useRef, useEffect } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import type { FormValueType } from './components/UpdateForm';
import UpdateForm from './components/UpdateForm';
import { addUser, updateUser, removeUser } from '@/services/app/user';
import { users } from '@/services/app/user';
import Util from '@/util';
import type * as ConstVar from '@/constants';
import CreateForm from './components/CreateForm';

export const UserStatus = (status: ConstVar.UserStatus) => {
  let statusText = 'Enabled';
  let color = 'green';

  switch (Util.safeInt(status)) {
    case 0:
      statusText = 'Disabled';
      color = 'red';
      break;
  }
  return (
    <Space>
      <Tag color={color}>{statusText}</Tag>
    </Space>
  );
};

/**
 * @en-US Add node
 * @param fields
 */
const handleAdd = async (fields: API.UserListItem) => {
  const hide = message.loading('Adding...');
  const data = { ...fields };
  delete data.confirmPassword;
  try {
    await addUser(data);
    message.success('Added successfully');
    return true;
  } catch (error: any) {
    Util.error('Adding failed, please try again!', error);
    return false;
  } finally {
    hide();
  }
};
/**
 * @en-US Update node
 *
 * @param fields
 */

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...');

  try {
    await updateUser(fields);
    hide();
    message.success('Updated successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};
/**
 *  Delete node
 * @zh-CN 删除节点
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.UserListItem[]) => {
  const hide = message.loading('Deleting');
  if (!selectedRows) return true;

  try {
    await removeUser({
      user_id: selectedRows.map((row) => row.user_id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

const UsersList: React.FC = () => {
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);

  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const createFormRef = useRef<ProFormInstance>();
  const updateFormRef = useRef<ProFormInstance>();
  const [currentRow, setCurrentRow] = useState<API.UserListItem>();
  const [selectedRowsState, setSelectedRows] = useState<API.UserListItem[]>([]);

  const columns: ProColumns<API.UserListItem>[] = [
    {
      title: 'User Name',
      dataIndex: 'username',
      tip: 'The username is the unique key',
      colSize: 1,
      filtered: true,
      render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow(entity);
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: 'Name',
      dataIndex: 'name',
      sorter: true,
      filtered: true,
    },
    {
      title: 'Initials',
      dataIndex: 'initials',
      sorter: true,
      filtered: true,
    },
    {
      title: 'Email',
      dataIndex: 'email',
      sorter: true,
      hideInForm: true,
      renderText: (val: string) => `${val}`,
    },
    {
      title: 'Role',
      dataIndex: 'role',
      hideInForm: false,
      sorter: true,
      filters: true,
      valueEnum: {
        '0': {
          text: 'User',
        },
        '1': {
          text: 'Admin',
        },
      },
    },
    {
      title: 'Status',
      dataIndex: 'status',
      hideInForm: false,
      sorter: true,
      filters: true,
      valueEnum: {
        '0': {
          text: 'Disabled',
        },
        '1': {
          text: 'Enabled',
        },
      },
      render: (_, record) => (record ? UserStatus(record.status as ConstVar.UserStatus) : ''),
    },
    {
      title: 'Created on',
      sorter: true,
      dataIndex: 'created_on',
      valueType: 'dateTime',
      search: false,
      renderFormItem: (item, { defaultRender, ...rest }, form) => {
        /* const status = form.getFieldValue('status');

        if (`${status}` === '0') {
          return false;
        }

        if (`${status}` === '3') {
          return <Input {...rest} placeholder={'Please enter the reason for the exception!'} />;
        } */

        return defaultRender(item);
      },
    },
    {
      title: 'Last Login on',
      sorter: true,
      dataIndex: 'last_login_on',
      valueType: 'dateTime',
      search: false,
      renderFormItem: (item, { defaultRender, ...rest }, form) => {
        return defaultRender(item);
      },
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          Edit
        </a>,
      ],
    },
  ];

  useEffect(() => {
    if (updateFormRef && updateFormRef.current) {
      const newValues = { ...(currentRow || {}) };
      newValues.password = '';
      newValues.confirmPassword = '';
      updateFormRef.current.setFieldsValue(newValues);
    }
  }, [updateFormRef, currentRow]);

  return (
    <PageContainer>
      <ProTable<API.UserListItem, API.PageParams>
        headerTitle={'Users list'}
        actionRef={actionRef}
        rowKey="user_id"
        revalidateOnFocus={false}
        search={{
          labelWidth: 'auto',
          searchText: 'Search',
          span: 6,
          filterType: 'query',
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        request={users}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        columnEmptyText=""
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              chosen{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              user &nbsp;&nbsp;
            </div>
          }
        >
          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            Bacth deletion
          </Button>
          {/* <Button type="primary">batch approval</Button> */}
        </FooterToolbar>
      )}

      <ModalForm
        title={'New user'}
        width="500px"
        visible={createModalVisible}
        onVisibleChange={handleModalVisible}
        layout="horizontal"
        labelAlign="left"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 12 }}
        formRef={createFormRef}
        onFinish={async (value) => {
          const success = await handleAdd(value as API.UserListItem);

          if (success) {
            handleModalVisible(false);
            if (createFormRef.current) createFormRef.current.resetFields();

            if (actionRef.current) {
              actionRef.current.reload();
            }
          }
        }}
      >
        <CreateForm onCancel={() => handleModalVisible(false)} formRef={createFormRef} />
      </ModalForm>

      <ModalForm
        title={'Update user'}
        width="500px"
        visible={updateModalVisible}
        onVisibleChange={handleUpdateModalVisible}
        layout="horizontal"
        labelAlign="left"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 12 }}
        initialValues={currentRow || {}}
        formRef={updateFormRef}
        onFinish={async (value) => {
          if (!currentRow?.user_id) return;
          const success = await handleUpdate({ ...value, user_id: currentRow?.user_id });

          if (success) {
            handleUpdateModalVisible(false);
            setCurrentRow(undefined);

            if (actionRef.current) {
              actionRef.current.reload();
            }
          }
        }}
      >
        <UpdateForm
          values={currentRow || {}}
          onCancel={() => {
            handleUpdateModalVisible(false);

            if (!showDetail) {
              setCurrentRow(undefined);
            }
          }}
        />
      </ModalForm>

      {/* <UpdateForm
        onSubmit={async (value) => {
          const success = await handleUpdate(value);

          if (success) {
            handleUpdateModalVisible(false);
            setCurrentRow(undefined);

            if (actionRef.current) {
              actionRef.current.reload();
            }
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
        updateModalVisible={updateModalVisible}
        values={currentRow || {}}
      /> */}
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.name && (
          <ProDescriptions<API.UserListItem>
            column={2}
            title={currentRow?.name}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.name,
            }}
            columns={columns as ProDescriptionsItemProps<API.UserListItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default UsersList;
