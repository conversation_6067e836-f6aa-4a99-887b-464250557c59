import type { RequestConfig } from 'umi';
import { request } from 'umi';
import { paramsSerializer } from './api';

const urlPrefix = '/api/file';

/** 
 * Get all files list
 * 
 * GET /api/file */
export async function getFileList(
  params: Partial<API.PageParams & API.File>,
  sort?: any,
  filter?: any,
) {
  return request<API.Result<API.File>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    paramsSerializer,
    withToken: true,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
  }));
}

/** 
 * Get a file url.
 * 
 * GET /api/file */
export async function getFile(
  id?: number,
  params?: any,
  options?: Record<string, any>,
): Promise<API.File> {
  return request<API.ResultObject<API.File>>(`${urlPrefix}/${id}`, {
    method: 'GET',
    params,
    ...(options || {}),
    withToken: true,
  }).then((res) => res.message);
}

/** delete file DELETE /api/file */
export async function deleteFile(id?: number, options?: Record<string, any>): Promise<API.File> {
  return request<API.BaseResult>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    ...(options || {}),
    withToken: true,
    paramsSerializer,
  });
}



/*
--------------------------------------------------------------------------------------
----------------    scanned misc File Management
--------------------------------------------------------------------------------------
*/
/** 
 * 
 * POST /api/file/moveToAssignFile */
export async function moveToAssignFile(
  data: ({ cat1?: string, cat2?: string, order_out_order_no?: number, order_in_order_no?: number, serverFileId?: string }),
  options?: Record<string, any>,
): Promise<API.File> {
  const config: RequestConfig = {
    method: 'POST',
    data,
    ...(options || {}),
  };

  return request<API.ResultObject<API.File>>(`${urlPrefix}/moveToAssignFile`, config).then(
    (res) => res.message,
  );
}
