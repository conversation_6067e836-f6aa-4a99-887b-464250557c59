import WorkflowStepDataItem from '@/pages/Order/OrderOutDetail/Workflow/WorkflowStepDataItem';
import OrderOutWorkflowStepLogList from '@/pages/Order/OrderOutWorkflowStepLog';
import Util, { sn } from '@/util';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm from '@ant-design/pro-form';
import { Col, Modal, Row } from 'antd';
import _ from 'lodash';
import { isArray } from 'lodash';
import { useEffect, useRef, useState } from 'react';

export type WorkflowOrderOutIncomingInvoiceType = {
  wf?: API.SysWorkflow;
  order_no?: number;
  steps?: API.SysWorkflowStep[];
};

const WorkflowOrderOutIncomingInvoice: React.FC<WorkflowOrderOutIncomingInvoiceType> = (props) => {
  const { wf, order_no } = props;

  const formRef = useRef<ProFormInstance>();

  const [steps, setSteps] = useState<API.SysWorkflowStep[]>([]);

  // history modal
  const [currentStepId, setCurrentStepId] = useState<number>();
  const [openHistoryModal, setOpenHistoryModal] = useState<boolean>(false);
  const [refreshTick, setRefreshTick] = useState<number>(0); // for refreshing history table.

  useEffect(() => {
    const initialValues: any = {};
    let existAnyWorkflowStepInfo = false;

    setSteps(props?.steps ?? []);

    (props?.steps ?? []).forEach((step) => {
      const field_type = step.field_type;
      const definedSteps = step.order_out_workflow_steps;
      const values =
        isArray(definedSteps) && definedSteps.length
          ? step.order_out_workflow_steps?.map((x) => x.value)
          : step.default_value || [];
      const firstValue = values?.[0];

      let fieldValue = firstValue;
      if (field_type == 'switch') {
        fieldValue = fieldValue == '1' || fieldValue === true || fieldValue === 'true';
      } else if (field_type == 'multiselect' || field_type == 'checkbox') {
        fieldValue = isArray(values) ? values : [values];
      } else if (field_type == 'radio') {
      } else if (field_type == 'daterange') {
        initialValues[`${step.id}_2`] = definedSteps?.[0]?.value2;
      }
      initialValues[`${step.id}`] = fieldValue;

      if (!existAnyWorkflowStepInfo && isArray(definedSteps) && definedSteps.length) {
        existAnyWorkflowStepInfo = true;
      }
    });
    formRef.current?.setFieldsValue(initialValues);
  }, [props?.steps]);

  return (
    <>
      {wf?.name ? <h4>{wf.name}</h4> : null}
      <ProForm
        formRef={formRef}
        layout="horizontal"
        labelCol={{ span: 9 }}
        labelAlign="left"
        submitter={false}
        onValuesChange={(__) => {
          setRefreshTick((prev) => prev + 1);
        }}
      >
        {steps?.map((step) => {
          return (
            <Row key={step.id} gutter={8} className="workflow">
              <Col span={step?.field_type !== 'divider' ? 22 : 24}>
                <WorkflowStepDataItem
                  step={step}
                  order_no={sn(order_no)}
                  fieldProps={{ precision: 3, width: 'md' }}
                  updateCallback={(res: any) => {
                    console.log('Update cb: ', res);
                    if ('value2' in res) {
                      formRef.current?.setFieldsValue({
                        [`${res.step_id}_2`]: res.value2,
                      });
                    } else {
                      formRef.current?.setFieldsValue({
                        [`${res.step_id}`]: res.value,
                      });
                    }

                    setSteps((prev) => {
                      const newDS = [...prev];
                      const row = _.find(newDS, { id: res.step_id }) as API.SysWorkflowStep;
                      if (row) {
                        row.latest_order_out_workflow_step_log =
                          res.step.latest_order_out_workflow_step_log;
                        row.order_out_workflow_steps = res.step.order_out_workflow_steps;
                      }
                      return newDS;
                    });
                  }}
                />
              </Col>
              {step?.field_type !== 'divider' && (
                <Col span={2}>
                  {step?.latest_order_out_workflow_step_log && (
                    <div className="text-xxs c-grey">
                      <div
                        title="View history"
                        className="cursor-pointer"
                        onClick={() => {
                          setOpenHistoryModal(true);
                          setCurrentStepId(step?.id);
                        }}
                      >
                        {Util.dtToDMYHHMM(step?.latest_order_out_workflow_step_log?.created_on)}{' '}
                        {step?.latest_order_out_workflow_step_log?.user?.username}
                      </div>
                    </div>
                  )}
                </Col>
              )}
            </Row>
          );
        })}
      </ProForm>
      <Modal
        width="700px"
        wrapProps={{ paddingTop: 50 }}
        open={openHistoryModal}
        onCancel={() => setOpenHistoryModal(false)}
        footer={false}
      >
        {currentStepId && order_no && (
          <OrderOutWorkflowStepLogList
            hidePageContainer
            order_no={order_no}
            step_id={currentStepId}
            open={openHistoryModal}
            refreshTick={refreshTick}
          />
        )}
      </Modal>
    </>
  );
};

export default WorkflowOrderOutIncomingInvoice;
