import { But<PERSON>, Card, message, Modal, Popover, Space, Tag } from 'antd';
import React, { useState, useRef, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

import Util, { getLexOfficeLink, getOrderTaskNo, nf2, sn, urlFull } from '@/util';

import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import {
  dsLatestLoVoucherList,
  updateLoInvoiceCheck,
} from '@/services/app/LexOffice/lo-voucher-list';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormCheckbox, ProFormDependency, ProFormTextArea } from '@ant-design/pro-form';
import ProForm, { ProFormText } from '@ant-design/pro-form';
import {
  CheckSquareOutlined,
  ExportOutlined,
  ImportOutlined,
  LinkOutlined,
  SyncOutlined,
} from '@ant-design/icons/lib/icons';
import type { WarningType } from './components/ExportToStatModal';
import ExportToStatModal, { getWarningText } from './components/ExportToStatModal';
import {
  exportLoInvoiceCheckStatList,
  getLoInvoiceCheckStatList,
} from '@/services/app/LexOffice/lo-voucher-stats-list';
import SProFormDateRangeFull, { DRSelection } from '@/components/SProFormDateRangeFull';
import useModalNavigation from './hooks/useModalNavigation';
import ImportFinDetailModalForm from './components/ImportFinDetailModalForm';

type TableRowType = API.LoInvoiceCheckWithFinDetailAggregation;
type SearchFormType = API.LoInvoiceCheck & {
  initialDrSelection?: DRSelection;
  dr_selection?: DRSelection;
  start_date?: string;
  end_date?: string;
  isSummarized?: boolean;
};

const LoInvoiceCheckStatList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const rowRef = useRef<API.LoInvoiceCheck>({}); // used for note update

  const [dataSource, setDataSource] = useState<TableRowType[]>(() => []);
  const [totalRow, setTotalRow] = useState<TableRowType>();
  const [currentRow, setCurrentRow] = useState<Partial<TableRowType>>();
  const [openExportToStatModal, setOpenExportToStatModal] = useState<boolean>(false);
  // Selection of date range selection
  const [dtRangeInitialSelction, setDtRangeInitialSelction] = useState<DRSelection>();

  // Summarized view
  const [isSummarized, setIsSummarized] = useState<boolean>(false);

  const searchFormRef = useRef<ProFormInstance<SearchFormType>>();

  // import fin_details
  const [openImportModal, setOpenImportModal] = useState<boolean>(false);

  useEffect(() => {
    const lastSf = Util.getSfValues('sf_lo_invoice_check_stat', {});
    if (!lastSf.dr_selection) {
      lastSf.dr_selection = DRSelection.DR_THIS_MONTH;
    }
    searchFormRef.current?.setFieldsValue(lastSf);
    setDtRangeInitialSelction(lastSf.dr_selection);

    setIsSummarized(lastSf.isSummarized || false);

    Util.setSfValues(lastSf);
  }, []);

  const columns: ProColumns<TableRowType>[] = [
    {
      title: 'Kategorie',
      dataIndex: 'Kategorie',
      sorter: true,
      hideInForm: true,
      width: 150,
      hideInTable: !isSummarized,
    },
    {
      title: 'Voucher Date',
      dataIndex: 'lo_voucher_date',
      sorter: true,
      hideInForm: true,
      width: 80,
      render: (dom, record) => Util.dtToDMY(record.lo_voucher_date),
    },
    {
      title: 'No',
      dataIndex: 'lo_no',
      sorter: true,
      hideInForm: true,
      width: 70,
    },
    {
      title: '',
      dataIndex: 'link',
      hideInForm: true,
      width: 20,
      className: 'p-0',
      render: (dom, record) =>
        record.lo_id ? (
          <a href={getLexOfficeLink(record.lo_type, record.lo_id)} target="_blank" rel="noreferrer">
            <LinkOutlined />
          </a>
        ) : null,
    },
    {
      title: 'Contact name',
      dataIndex: 'lo_contact_name',
      sorter: true,
      hideInForm: true,
      ellipsis: true,
      width: 220,
    },
    {
      title: 'Gross Amount',
      dataIndex: 'total_gross_amount',
      sorter: true,
      hideInForm: true,
      align: 'right',
      width: 80,
      hideInTable: isSummarized,
      render(dom, record) {
        return <span>{nf2(record.total_gross_amount)}</span>;
      },
    },
    {
      title: 'Summe total',
      dataIndex: 'fd_gross_total',
      sorter: true,
      hideInForm: true,
      align: 'right',
      width: 80,
      render(dom, record) {
        const isDiff = sn(record.fd_gross_total) != sn(record.total_gross_amount);
        return (
          <>
            <div className={isDiff ? 'c-red' : ''}>
              {record.fd_gross_total == null ? (
                record.lo_status != 'voided' ? (
                  'xxxx'
                ) : (
                  <>&nbsp;</>
                )
              ) : (
                nf2(record.fd_gross_total, true)
              )}
            </div>
            {isDiff && <div className="c-grey text-xs">{nf2(record.total_gross_amount)}</div>}
          </>
        );
      },
    },
    {
      title: 'EK Summe total',
      dataIndex: 'fd_net_total',
      sorter: true,
      hideInForm: true,
      align: 'right',
      width: 80,
      render(dom, record) {
        return <span>{nf2(record.fd_net_total)}</span>;
      },
    },
    {
      title: 'Ertrag total',
      dataIndex: 'fd_gp_total',
      sorter: true,
      hideInForm: true,
      align: 'right',
      width: 80,
      render(dom, record) {
        const value = sn(record.fd_gp_total);
        const cls = value < 0 ? 'c-red' : value > 0 ? 'c-green' : '';
        return <span className={cls}>{nf2(record.fd_gp_total)}</span>;
      },
    },

    {
      title: '',
      dataIndex: 'action-check',
      sorter: false,
      width: 20,
      render(__, record) {
        return (
          <span
            className="cursor-pointer"
            onClick={() => {
              const hide = message.loading('Updating checked status', 0);
              const updatedData = { is_stat_checked: record.is_stat_checked == 1 ? 0 : 1 };
              const funcAction: Promise<any> = updateLoInvoiceCheck(`${record.lo_id}`, updatedData);
              funcAction
                .then((res) => {
                  message.success('Updated successfully.');
                  actionRef.current?.reload();
                })
                .catch((err) => Util.error(err))
                .finally(() => hide());
            }}
          >
            {record.is_stat_checked != 1 ? (
              <CheckSquareOutlined style={{ color: 'lightgrey' }} title="Make checked." />
            ) : (
              <CheckSquareOutlined style={{ color: '#07c807' }} title="Make unchecked." />
            )}
          </span>
        );
      },
    },

    {
      title: 'Warnings',
      dataIndex: 'fd_hs_code_no_count',
      sorter: true,
      hideInForm: true,
      width: 200,
      className: 'text-11',
      render(__, record) {
        const warnings: Record<keyof WarningType, number> = {
          Artikelnummer: 0,
          hs_code: 0,
          net_weight: 0,
          EK_Summe: 0,
          Summe: 0,
          Supplier: 0,
          SummeDiff: 0,
          EkSummeDiff: 0,
        };
        if (sn(record.fd_item_no_no_count)) {
          warnings.Artikelnummer = sn(record.fd_item_no_no_count);
        }
        if (sn(record.fd_hs_code_no_count)) {
          warnings.hs_code = sn(record.fd_hs_code_no_count);
        }
        if (sn(record.fd_net_weight_no_count)) {
          warnings.net_weight = sn(record.fd_net_weight_no_count);
        }
        if (sn(record.fd_net_total_no_count)) {
          warnings.EK_Summe = sn(record.fd_net_total_no_count);
        }
        if (sn(record.fd_gross_total_no_count)) {
          warnings.Summe = sn(record.fd_gross_total_no_count);
        }
        if (sn(record.fd_supplier_no_count)) {
          warnings.Supplier = sn(record.fd_supplier_no_count);
        }
        if (sn(record.fd_summe_diff_count)) {
          warnings.SummeDiff = sn(record.fd_summe_diff_count);
        }
        if (sn(record.fd_ek_summe_diff_count)) {
          warnings.EkSummeDiff = sn(record.fd_ek_summe_diff_count);
        }

        return Object.keys(warnings)
          .filter((x) => (warnings as any)[x] > 0)
          .map((x) => (
            <div key={x} className="c-red">
              {(warnings as any)[`${x}`]} rows {getWarningText(x)}
            </div>
          ));
      },
    },

    {
      title: 'Type',
      dataIndex: 'lo_type',
      sorter: false,
      hideInForm: true,
      width: 90,
      ellipsis: true,
    },
    {
      title: 'Status',
      dataIndex: 'lo_status',
      sorter: false,
      hideInForm: true,
      width: 90,
      ellipsis: true,
    },
    {
      title: 'Order No',
      dataIndex: ['order_out', 'order_no'],
      sorter: false,
      hideInForm: true,
      width: 70,
      render: (dom, record) =>
        record.order_out ? (
          <>
            <Popover
              placement="topLeft"
              overlayStyle={{ maxWidth: 400 }}
              content={
                <Space direction="vertical" size={8} style={{ maxWidth: 250 }}>
                  <div>
                    <Tag color="lime">{getOrderTaskNo('OrderOut', record.order_out?.order_no)}</Tag>
                    <span>{record.order_out?.customer}</span>
                  </div>
                  <div
                    dangerouslySetInnerHTML={{
                      __html: record.order_out?.desc?.replaceAll?.('\n', '<br />') || '',
                    }}
                  />
                </Space>
              }
            >
              <a
                href={urlFull('/order-out-detail/' + record.order_out?.order_no)}
                target="_blank"
                rel="noreferrer"
                style={{ marginLeft: 8 }}
              >
                {record.order_out?.order_no}
              </a>
            </Popover>
          </>
        ) : null,
    },
    {
      title: 'OrderOut Desc',
      dataIndex: 'order_desc_dummy',
      width: 180,
      tooltip: 'Order out supplier & description',
      render(dom, record) {
        return record.order_out?.order_no ? (
          <Space direction="vertical" size={2} style={{ maxWidth: 250 }}>
            <div>{record.order_out?.supplier_obj?.name}</div>
            <div
              dangerouslySetInnerHTML={{
                __html: record.order_out?.desc?.replaceAll?.('\n', '<br />') || '',
              }}
            />
          </Space>
        ) : null;
      },
    },

    /* {
      title: 'Cust No',
      dataIndex: 'lo_cust_no',
      sorter: true,
      hideInForm: true,
      width: 70,
      className: 'text-11',
    },
    {
      title: 'Customer',
      dataIndex: ['order_out', 'customer_obj'],
      sorter: true,
      hideInForm: true,
      width: 130,
      tooltip: 'Customer in Order Out',
      ellipsis: true,
      className: 'text-11',
      render(dom, record, index, action, schema) {
        const cust = record.order_out?.customer_obj;
        return cust?.id
          ? `${cust.name}${cust.description ? ` - ${cust.description.substring(0, 20)}` : ''}`
          : null;
      },
    },
    {
      title: 'Type',
      dataIndex: 'lo_type',
      sorter: true,
      hideInForm: true,
      width: 90,
      ellipsis: true,
      className: 'text-11',
    },
    {
      title: 'Order No',
      dataIndex: ['order_out', 'order_no'],
      sorter: true,
      hideInForm: true,
      className: 'text-11',
      width: 90,
      render: (dom, record) => (
        <>
          <Popover
            placement="topLeft"
            overlayStyle={{ maxWidth: 400 }}
            content={
              <Space direction="vertical" size={8} style={{ maxWidth: 250 }} className="text-sm">
                <div>
                  <Tag color="lime">{getOrderTaskNo('OrderOut', record.order_out?.order_no)}</Tag>
                  <span>{record.order_out?.customer}</span>
                </div>
                <div
                  dangerouslySetInnerHTML={{
                    __html: record.order_out?.desc?.replaceAll?.('\n', '<br />') || '',
                  }}
                />
              </Space>
            }
          >
            <a
              href={urlFull('/order-out-detail/' + record.order_out?.order_no)}
              target="_blank"
              rel="noreferrer"
              style={{ marginLeft: 8 }}
            >
              {record.order_out?.order_no}
            </a>
          </Popover>
        </>
      ),
    },

    {
      title: 'Order Desc',
      dataIndex: 'order_desc_dummy',
      className: 'text-11',
      width: 300,
      render(dom, record, index, action, schema) {
        return record.order_out?.order_no ? (
          <Space direction="vertical" size={2} style={{ maxWidth: 250 }} className="text-sm">
            <div>
              <Tag color="lime">{getOrderTaskNo('OrderOut', record.order_out?.order_no)}</Tag>
              <span>{record.order_out?.customer}</span>
            </div>
            <div
              dangerouslySetInnerHTML={{
                __html: record.order_out?.desc?.replaceAll?.('\n', '<br />') || '',
              }}
            />
          </Space>
        ) : null;
      },
    }, */
    {
      title: 'Note',
      dataIndex: 'note',
      sorter: false,
      tooltip: 'Click to edit',
      className: 'text-11 c-grey',
      ellipsis: true,
      width: 180,
      onCell: (record) => {
        return record.children?.length
          ? {}
          : {
              className: 'cursor-pointer',
              onClick: () => {
                const modal = Modal.confirm({
                  title: 'Update note',
                  icon: false,
                  content: (
                    <>
                      <ProFormTextArea
                        placeholder={'Enter your note'}
                        fieldProps={{
                          defaultValue: record.note,
                          onChange: (e: any) => {
                            rowRef.current = { ...rowRef.current, note: e.target.value };
                          },
                        }}
                      />
                    </>
                  ),
                  onOk: (close) => {
                    const hide = message.loading('Updating note...', 0);
                    modal.update({ okButtonProps: { disabled: true } });
                    updateLoInvoiceCheck(record.lo_id, { note: rowRef.current?.note })
                      .then((res) => {
                        actionRef.current?.reload();
                        close();
                      })
                      .catch((err) => {
                        Util.error(err);
                        modal.update({ okButtonProps: { disabled: false } });
                      })
                      .finally(() => hide());
                  },
                });
              },
            };
      },
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 90,
      fixed: 'right',
      render: (_, record) =>
        record.lo_id
          ? [
              <Button
                key="export-to-stat"
                icon={<ExportOutlined />}
                size="small"
                type={record.is_exported ? 'default' : 'primary'}
                ghost={record.is_exported ? false : true}
                title="Export to Stat..."
                onClick={() => {
                  setCurrentRow(record);
                  setOpenExportToStatModal(true);
                }}
              >
                Export
              </Button>,
            ]
          : null,
    },
  ];

  const { handleNavigation } = useModalNavigation(dataSource, {
    setCurrentRow,
  });

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_lo_invoice_check_stat', {})}
          submitter={{
            submitButtonProps: {
              htmlType: 'submit',
            },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => actionRef.current?.reload(),
            render: (form, dom) => {
              return [...dom];
            },
          }}
        >
          <SProFormDateRangeFull
            selectionFieldName="dr_selection"
            initialDrSelection={dtRangeInitialSelction}
            label="Date"
            startDateName="start_date"
            endDateName="end_date"
            formRef={searchFormRef}
            selectorOptions={[
              DRSelection.DR_TODAY,
              DRSelection.DR_YESTERDAY,
              DRSelection.DR_THIS_WEEK,
              DRSelection.DR_LAST_WEEK,
              DRSelection.DR_THIS_MONTH,
              DRSelection.DR_LAST_MONTH,
              DRSelection.DR_FYSCAL_THIS_YEAR,
              DRSelection.DR_FYSCAL_LAST_YEAR,
              DRSelection.DR_THIS_YEAR,
              DRSelection.DR_LAST_YEAR,
              DRSelection.DR_SINCE_BEGINNING,
              DRSelection.DR_CUSTOM,
            ]}
          />
          <ProFormText name={'lo_no'} label="No" width={120} placeholder={'No'} />
          <ProFormText name={'lo_contact_name'} label="Contact name" placeholder={'Contact name'} />
          <ProFormCheckbox
            name="isSummarized"
            label="Summarized View"
            tooltip="Summarized View. Filters will be ignored."
            fieldProps={{
              onChange(e) {
                setIsSummarized(e.target.checked);
                actionRef.current?.reload();
              },
            }}
          />
          <ProFormDependency name={[`isSummarized`]}>
            {(depValues) => {
              const isSummarizedLocal = depValues.isSummarized;
              return (
                isSummarizedLocal && (
                  <ProFormText
                    name={'kategorie'}
                    label="kategorie"
                    width={150}
                    placeholder={'kategorie'}
                  />
                )
              );
            }}
          </ProFormDependency>
        </ProForm>
      </Card>
      <ProTable<API.LoInvoiceCheckWithFinDetailAggregation, API.PageParams>
        headerTitle={'Invoice stats'}
        actionRef={actionRef}
        rowKey="uid"
        size="small"
        className="size-sm"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        sticky
        search={false}
        scroll={{
          x: 700,
        }}
        pagination={{
          showSizeChanger: true,
          pageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        // params={{ nlo_statuses: ['draft'] } as any}
        // params={{ with: 'orderOut,orderOut.customerObj,orderOut.supplierObj' }}
        params={{ with: 'orderOut,orderOut.supplierObj,orderOut.customerObj' }}
        dataSource={dataSource}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_lo_invoice_check_stat', searchFormValues);
          return getLoInvoiceCheckStatList(
            {
              ...params,
              ...searchFormValues,
              // nlo_statuses: ['draft'],
              lo_types: ['invoice', 'creditnote'],
            },
            Object.keys(sort).length < 1 ? { lo_no: 'descend', lo_voucher_date: 'descend' } : sort,
            filter,
          ).then((res) => {
            setDataSource(res.data);
            setTotalRow(res.summary);
            return res;
          });
        }}
        onRequestError={(err) => Util.error(err)}
        columns={columns}
        columnEmptyText=""
        toolBarRender={() => [
          <Button
            key="import-2003"
            type="primary"
            ghost
            icon={<ImportOutlined />}
            style={{ marginRight: 64 }}
            onClick={async () => {
              setOpenImportModal(true);
            }}
          >
            Import FinDetails_Until2023
          </Button>,
          <Button
            key="export"
            type="primary"
            icon={<ExportOutlined />}
            disabled={!isSummarized}
            onClick={async () => {
              const searchFormValues = searchFormRef.current?.getFieldsValue();
              Util.setSfValues('sf_lo_invoice_check_stat', searchFormValues);
              return exportLoInvoiceCheckStatList(
                {
                  ...{ with: 'orderOut,orderOut.supplierObj,orderOut.customerObj' },
                  ...searchFormValues,
                  lo_types: ['invoice', 'creditnote'],
                },
                { lo_no: 'descend', lo_voucher_date: 'descend' },
              )
                .then((res) => {
                  if (res.url) {
                    window.open(`${API_URL}/api/${res.url}`, '_blank');
                  }
                })
                .catch(Util.error);
            }}
          >
            Export
          </Button>,
          <Button
            type="primary"
            key="ds-lo"
            className="btn-green"
            icon={<SyncOutlined />}
            title="Sync latest invoices & credit notes & order confirmation from Lex Office"
            onClick={() => {
              const hide = message.loading(
                'Down syncing invoices and credit notes from Lex office...',
                0,
              );
              dsLatestLoVoucherList()
                .then((res) => actionRef.current?.reload())
                .catch((res) => {
                  message.error('Failed to sync data.');
                })
                .finally(() => hide());
            }}
          >
            Sync LO Invoices
          </Button>,
        ]}
        rowClassName={(record) => {
          let rowCls = '';
          if (record.lo_status == 'voided') {
            rowCls += ' stroke-all';
          }
          /*  +
          (record.is_checked == 1 ? ' c-grey-all' : '') */
          if (record.lo_type == 'creditnote') {
            rowCls += ' italic-all';
          }
          if (record.fd_count && !record.is_stat_checked) {
            rowCls += ' bg-light-yellow';
          }
          return rowCls;
        }}
        summary={(dataParam) => {
          // dataParam is based on dataSource, so we don't use this param.
          // const data = editableFormRef.current?.getRowsData?.() || [];
          // console.log('summary render: ', data, 'dataparam: ', dataParam);
          // const totalRowLocal = calcTotal(data);

          const totalRowLocal = totalRow ?? {};

          return (
            <ProTable.Summary fixed="top">
              <ProTable.Summary.Row style={{ fontWeight: 'bold', borderBottom: '2px solid #666' }}>
                {columns
                  .filter((c) => {
                    if (c.hideInTable) return false;
                    return true;
                  })
                  .map((c, index) => {
                    const tmpKey =
                      typeof c.dataIndex === 'string'
                        ? c.dataIndex
                        : ((c.dataIndex || []) as any[]).join('.');
                    let value: any = null;
                    let align: any = 'left';

                    if (index == 0) value = 'Total';
                    else if (
                      tmpKey == 'fd_gross_total' ||
                      tmpKey == 'fd_net_total' ||
                      tmpKey == 'fd_gp_total' //||
                      // tmpKey == 'total_gross_amount'
                    ) {
                      value = nf2(totalRowLocal[tmpKey]);
                      align = 'right';
                    }

                    // CSS class
                    let cls = '';
                    if (tmpKey == 'fd_gp_total') {
                      const dValue = sn(totalRowLocal[tmpKey]);
                      cls = dValue < 0 ? 'c-red' : dValue > 0 ? 'c-green' : '';
                      value = <span className={cls}>{nf2(dValue)}</span>;
                    }

                    return (
                      <ProTable.Summary.Cell key={tmpKey} index={index} align={align}>
                        {value}
                      </ProTable.Summary.Cell>
                    );
                  })}
              </ProTable.Summary.Row>
            </ProTable.Summary>
          );
        }}
      />
      {currentRow && (
        <ExportToStatModal
          invoiceCheck={currentRow}
          modalVisible={openExportToStatModal}
          handleModalVisible={setOpenExportToStatModal}
          handleNavigation={handleNavigation}
          updateParentDs={(data: Partial<API.LoInvoiceCheck>) => {
            setDataSource((prev) => {
              const newDs = [...prev];
              const found = newDs.find((x) => x.lo_id == data.lo_id);
              if (found) Object.assign(found, data);
              return newDs;
            });
          }}
          reloadParentTable={() => actionRef.current?.reload()}
        />
      )}

      <ImportFinDetailModalForm
        modalVisible={openImportModal}
        handleModalVisible={setOpenImportModal}
        onSubmit={async (value) => {
          // actionRef.current?.reload();
          setOpenImportModal(false);
        }}
      />
    </PageContainer>
  );
};

export default LoInvoiceCheckStatList;
