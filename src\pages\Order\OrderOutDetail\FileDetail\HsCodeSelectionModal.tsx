import { getSupplierHsCodesList } from '@/services/app/Order/order-out-detail';
import Util from '@/util';
import type { ActionType, ProColumnType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Modal } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useEffect, useRef } from 'react';

type RecordType = {
  article_no?: string;
  article?: string;
  hs_code?: string;
  exp_date?: string;
};

export type HsCodeSelectionModalProps = {
  order_no?: number;
  supplier_id?: number | null;
  record: Partial<API.OrderOutDetail>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.OrderOutDetail) => Promise<boolean | void>;
};

const HsCodeSelectionModal: React.FC<HsCodeSelectionModalProps> = (props) => {
  const actionRef = useRef<ActionType>();

  const columns: ProColumnType<RecordType>[] = [
    {
      title: 'Exp. Date',
      dataIndex: ['exp_date'],
      search: false,
      width: 100,
      render(dom, record, index, action, schema) {
        return Util.dtToDMY(record.exp_date);
      },
    },
    {
      title: 'Article No',
      dataIndex: ['article_no'],
      valueType: 'text',
      search: false,
      width: 150,
    },
    {
      title: 'Article',
      dataIndex: ['article'],
      valueType: 'text',
      search: false,
      ellipsis: true,
    },
    {
      title: 'HS Code',
      dataIndex: ['hs_code'],
      valueType: 'text',
      search: false,
      width: 150,
    },
  ];

  useEffect(() => {
    if (props.modalVisible) {
      actionRef.current?.reload();
    }
  }, [props.modalVisible]);

  return (
    <Modal
      title={'Latest HS Codes'}
      width="800px"
      open={props.modalVisible}
      onCancel={() => props.handleModalVisible(false)}
      footer={false}
    >
      <ProTable<RecordType, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        revalidateOnFocus={false}
        options={false}
        size="small"
        search={false}
        toolBarRender={() => []}
        pagination={{
          hideOnSinglePage: true,
          showSizeChanger: false,
        }}
        // scroll={{ x: 800 }}
        cardProps={{
          bodyStyle: { padding: 0 },
        }}
        locale={{ emptyText: <></> }}
        params={{ order_no: props.order_no, supplier_id: props.supplier_id } as any}
        request={getSupplierHsCodesList}
        columns={columns as any}
        columnEmptyText=""
      />
    </Modal>
  );
};

export default HsCodeSelectionModal;
