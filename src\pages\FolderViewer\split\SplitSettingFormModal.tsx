import type { ProFormInstance } from '@ant-design/pro-form';
import { ModalForm, ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { Space, message } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef, useState } from 'react';
import Util from '@/util';
import { splitFile } from '@/services/app/FolderViewer/folder-viewer';

export type SplitOptionType = {
  split_pages?: string;
  delete_pages?: string;
};

export type SplitSettingFormModalProps = {
  fmFile?: API.FmFile;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (res: any) => void;
};

const SplitSettingFormModal: React.FC<SplitSettingFormModalProps> = (props) => {
  const formRef = useRef<ProFormInstance<SplitOptionType>>();

  const { fmFile, modalVisible, handleModalVisible, onSubmit } = props;

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (modalVisible) {
      formRef.current?.resetFields();
    }
  }, [modalVisible]);

  return (
    <ModalForm<SplitOptionType>
      title={
        <Space style={{ alignItems: 'center' }} size={24}>
          <span>Split Settings</span>
        </Space>
      }
      width={600}
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelCol={{ span: 8 }}
      labelAlign="left"
      wrapperCol={{ span: 16 }}
      formRef={formRef}
      colon={false}
      onFinish={async (values) => {
        setLoading(true);

        const hide = message.loading('Splitting a PDF...', 0);
        setLoading(true);
        return splitFile(fmFile?.id, {
          ...values,
        })
          .then((res) => {
            message.success('Splitted successfully.');
            handleModalVisible(false);
            onSubmit?.(res);
          })
          .catch(Util.error)
          .finally(() => {
            hide();
            setLoading(false);
          });
      }}
      submitter={{
        searchConfig: { resetText: 'Cancel', submitText: 'Split' },
        onReset(value) {
          props.handleModalVisible(false);
        },
        resetButtonProps: { disabled: loading, loading: loading },
        submitButtonProps: { disabled: loading, loading: loading },
      }}
      modalProps={{
        confirmLoading: loading,
      }}
    >
      <ProFormTextArea
        name="split_pages"
        label="Split Pages"
        fieldProps={{ rows: 5 }}
        help="You can separate documents by ';' or new line. e.g.1-4;6,8\n\r2-4,12\n9,10,11"
      />
      <ProFormText
        name="delete_pages"
        label="Delete Pages"
        help="Specify page numbers to be excluded by ',' and '-'. e.g.3,4-6"
      />
    </ModalForm>
  );
};

export default SplitSettingFormModal;
