import type { Dispatch, SetStateAction } from 'react';
import { useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { Button, Col, message, Row } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormSelect } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { getTask, updateTaskPartial } from '@/services/app/Task/task';
import Util from '@/util';
import { useModel } from 'umi';
import _ from 'lodash';
import { SelectOutlined } from '@ant-design/icons';
import type { onTaskClickType } from '..';

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading('Updating...');

  try {
    const res = await updateTaskPartial(fields);
    hide();
    message.success('Update is successful');
    return res;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type FormValueType = Partial<API.Task>;

export type GroupAssignmentFormProps = {
  task?: Partial<API.Task>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Task) => Promise<boolean | void>;
  onTaskClick?: onTaskClickType;
};

const GroupAssignmentForm: React.FC<GroupAssignmentFormProps> = (props) => {
  const { task } = props;
  const { taskGroups, blockInfoMap } = useModel('task-group');
  const formRef = useRef<ProFormInstance>();

  const [blockIds, setBlockIds] = useState<number[]>([]);

  useEffect(() => {
    if (!props.modalVisible) return;

    /* if (formRef.current) {
      const newValues = { ...(task || {}) };
      formRef.current.setFieldsValue(newValues);
    } */

    if (task?.id) {
      getTask(task?.id, { with: 'taskBlocks' })
        .then((res) => {
          const ids = (res?.task_blocks || []).map((x: API.Task) => x.id);
          setBlockIds(ids);
          formRef.current?.setFieldValue('block_ids', ids);
        })
        .catch((reason) => Util.error(reason));
    }
  }, [props.modalVisible, task?.id]);

  return (
    <ModalForm
      title={
        <>
          <SelectOutlined className="c-blue" /> Assign Task Groups into task #{task?.id}
        </>
      }
      width="600px"
      visible={props.modalVisible}
      onVisibleChange={props.handleModalVisible}
      layout="vertical"
      labelAlign="left"
      formRef={formRef}
      submitter={{
        render(__) {
          return [
            <Button key="cancel" onClick={() => props.handleModalVisible(false)}>
              {' '}
              Cancel
            </Button>,
            <Button key="submit" type="primary" onClick={() => formRef.current?.submit()}>
              Save
            </Button>,
          ];
        },
      }}
      onFinish={async (value) => {
        const data = {
          ...value,
          id: task?.id,
        };
        const res = await handleUpdate(data);

        if (res) {
          if (props.onSubmit) props.onSubmit(data);
        }
      }}
    >
      <Row gutter={32}>
        <Col span={12}>
          <ProFormSelect
            name="block_ids"
            label="Blocks"
            required
            mode="multiple"
            rules={[
              {
                required: true,
                message: 'Block is required',
              },
            ]}
            formItemProps={{ style: { width: '500px' } }}
            fieldProps={{
              onChange: (ids) => {
                setBlockIds(ids || []);
              },
            }}
            options={taskGroups?.map((x: API.TaskGroup) => ({
              value: x.id,
              label: `${x.name} (${x.code})`,
              children: x.task_blocks?.map((b) => ({
                value: b.id,
                label: `${x.code} / ${b.name}`,
                disabled:
                  blockIds?.findIndex(
                    (id: any) => id != b.id && blockInfoMap?.[id]?.group_id == x.id,
                  ) >= 0,
              })),
            }))}
          />
        </Col>
      </Row>
    </ModalForm>
  );
};

export default GroupAssignmentForm;
