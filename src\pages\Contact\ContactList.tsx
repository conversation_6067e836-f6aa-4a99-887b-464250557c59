import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, message, Popconfirm, Space } from 'antd';
import React, { useState, useRef, useEffect } from 'react';
import { FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import UpdateForm from './components/UpdateForm';

import Util, { sn } from '@/util';
import CreateForm from './components/CreateForm';
import { getContactList, deleteContact } from '@/services/app/Sys/contact';
import SFooterToolbarExtra from '@/components/Table/SFooterToolbarExtra';
import BatchDeleteAction from '@/components/Table/BatchDeleteAction';
import { ContactType } from '@/constants';

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.Contact[]) => {
  const hide = message.loading('Deleting...', 0);
  if (!selectedRows) return true;

  try {
    await deleteContact({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully.');
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

/* const handleOrderOutContactByCustomerContact = async (
  address_type: API.AdressType,
  order_no?: number,
  by_address_id?: number,
) => {
  const hide = message.loading(`Updating ${address_type} address...`, 0);
  return updateOrderContact(order_no, {
    address_type,
    by_address_id,
    type: 'OrderOut',
  })
    .catch((e) => Util.error(e))
    .finally(() => hide());
}; */

/**
 * Don't pass both of customer_id and supplier_id
 */
type ContactListProps = {
  type: 'OrderIn' | 'OrderOut';
  contact_type: ContactType;

  supplier_id?: number;
  customer_id?: number;

  // tableTitle?: string;
  refreshTick?: number;

  // addressList?: API.Contact[];
  // setContactList?: Dispatch<SetStateAction<API.Contact[]>>;
  // loadOrderOut?: (no?: number) => void;
};

const ContactList: React.FC<ContactListProps> = ({
  type,
  contact_type,

  customer_id,
  supplier_id,

  //addressList,
  //setContactList,
  // loadOrderOut,
  ...rest
}) => {
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.Contact>();
  const [selectedRowsState, setSelectedRows] = useState<API.Contact[]>([]);

  const columns: ProColumns<API.Contact>[] = [
    /* {
      title: '',
      dataIndex: '',
      sorter: true,
      width: 50,
      valueType: 'option',
      render: (doms, record) => (
        <Space size={0}>
          {customer_id && (
            <Button
              size="small"
              type="link"
              title="Use as invoice address"
              onClick={() => {
                handleOrderOutContactByCustomerContact('invoice', order_no ?? 0, record.id).then(
                  (res) => loadOrderOut?.(order_no),
                );
              }}
            >
              Inv
            </Button>
          )}
          {customer_id && (
            <Button
              size="small"
              type="link"
              onClick={() => {
                handleOrderOutContactByCustomerContact('shipping', order_no ?? 0, record.id).then(
                  (res) => loadOrderOut?.(order_no),
                );
              }}
              title="Use as shipping address"
            >
              S
            </Button>
          )}
          {supplier_id && (
            <Button
              size="small"
              type="link"
              title="Use as loading address"
              onClick={() => {
                handleOrderOutContactByCustomerContact('loading', order_no ?? 0, record.id).then(
                  (res) => loadOrderOut?.(order_no),
                );
              }}
            >
              L
            </Button>
          )}
        </Space>
      ),
    }, */
    {
      title: 'Last Name',
      dataIndex: 'last_name',
      sorter: true,
      width: 100,
    },
    {
      title: 'First Name',
      dataIndex: 'first_name',
      sorter: true,
      width: 100,
      showSorterTooltip: false,
    },
    {
      title: 'Position',
      dataIndex: 'position',
      sorter: true,
      width: 100,
      showSorterTooltip: false,
    },
    {
      title: 'Phone',
      dataIndex: 'telephone',
      sorter: true,
      width: 100,
      showSorterTooltip: false,
    },
    {
      title: 'Email',
      dataIndex: 'email',
      sorter: true,
      showSorterTooltip: false,
      width: 100,
    },
    {
      title: '',
      dataIndex: 'option',
      valueType: 'option',
      showSorterTooltip: false,
      width: 50,
      render: (_, record) => (
        <Space size={0}>
          <Button
            key="edit"
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => {
              handleUpdateModalVisible(true);
              setCurrentRow(record);
            }}
          />
          <Popconfirm
            key="delete"
            title={<>Are you sure you want to delete?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ maxWidth: 300, minWidth: 300 }}
            onConfirm={async () => {
              handleRemove([record])
                .then((res) => actionRef.current?.reload())
                .catch((reason) => Util.error(reason));
            }}
          >
            <Button type="link" size="small" icon={<DeleteOutlined />} />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    if (sn(rest.refreshTick) > 0) {
      actionRef.current?.reload();
    }
  }, [rest.refreshTick]);

  const contactAvailable =
    (contact_type == ContactType.SUPPLIER_CONTACT && supplier_id) ||
    (contact_type == ContactType.CUSTOMER_CONTACT && customer_id);

  return (
    <>
      <ProTable<API.Contact, API.PageParams>
        headerTitle={'Contact list'}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true, reload: true, density: false, setting: false }}
        search={false}
        cardProps={{ style: { padding: 0 }, bodyStyle: { padding: 0 } }}
        toolBarRender={() =>
          contactAvailable
            ? [
                <Button
                  type="primary"
                  key="primary"
                  size="small"
                  onClick={() => {
                    handleModalVisible(true);
                  }}
                >
                  <PlusOutlined /> New
                </Button>,
              ]
            : []
        }
        locale={{ emptyText: <></> }}
        pagination={{
          showSizeChanger: true,
          pageSize: 10,
          hideOnSinglePage: true,
        }}
        //dataSource={addressList}
        //onDataSourceChange={setContactList}
        request={(params, sort, filter) => {
          if (contactAvailable) {
            return getContactList(
              { ...params, customer_id, supplier_id, contact_type },
              sort,
              filter,
            );
          }
          return Promise.resolve([]);
        }}
        columns={columns}
        rowSelection={false}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <SFooterToolbarExtra
              title="address"
              actionRef={actionRef}
              selectedRowsState={selectedRowsState}
            />
          }
        >
          <BatchDeleteAction
            title="address"
            onConfirm={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          />
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        contact_type={contact_type}
        customer_id={customer_id}
        supplier_id={supplier_id}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        contact_type={contact_type}
        customer_id={customer_id}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
        }}
      />
    </>
  );
};

export default ContactList;
