import { sn } from '@/util';
import * as ConstVar from '@/constants';
import { Tag } from 'antd';

export const TaskStatusComp: React.FC<{ status?: ConstVar.TaskStatus; style?: any }> = ({
  status,
  style,
}) => {
  let color = 'default';

  const statusText =
    ConstVar.TaskStatusOptions.find((x) => x.value == sn(status))?.label || 'Unknown';

  switch (sn(status)) {
    case ConstVar.TaskStatus.PROBLEM:
      color = 'red';
      break;
    case ConstVar.TaskStatus.WAITING:
      color = 'orange';
      break;
    case ConstVar.TaskStatus.IN_PROGRESS:
      color = 'blue';
      break;
    case ConstVar.TaskStatus.DONE:
      color = 'green';
      break;
  }

  return (
    <Tag color={color} style={{ ...style }}>
      {statusText}
    </Tag>
  );
};

export default TaskStatusComp;
