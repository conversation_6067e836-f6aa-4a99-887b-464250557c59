import { Draggable } from 'react-beautiful-dnd';
import type { onTaskClickType } from '..';
import { TaskClickAction } from '..';
import { useModel } from 'umi';
import { DictCode, TaskBlockLayout } from '@/constants';
import TaskLayoutC from './TaskLayout/TaskLayoutC';
import TaskLayoutT from './TaskLayout/TaskLayoutT';
import TaskLayoutB from './TaskLayout/TaskLayoutB';
import TaskLayoutA from './TaskLayout/TaskLayoutA';

import { sn, urlFull } from '@/util';
import TaskLayoutOrderA from './TaskLayoutOrder/TaskLayoutOrderA';

export type TaskProps = {
  index: number; // index no in list.
  task: API.Task;
  onTaskClick?: onTaskClickType;

  groupId?: number;
  droppableId: string; // block ID in DnD
  block?: API.TaskBlock;
  parentGroupId: number;
  parentBlockId: number;
};

export const Task: React.FC<TaskProps> = (props) => {
  const { task, index } = props;

  const { initialState } = useModel('@@initialState');
  const { getBlockLayout } = useModel('task-group');
  const { getCodeValue } = useModel('app-settings');
  const layout = getBlockLayout(task.block_id);

  return (
    <Draggable key={task.id} draggableId={`${task.id}`} index={index}>
      {(providedIn: any, snapshotIn: any) => {
        let innerDom = null;
        if (layout == TaskBlockLayout.LAYOUT_T_DATE) {
          innerDom = <TaskLayoutT {...props} />;
        } else if (layout == TaskBlockLayout.LAYOUT_A) {
          if (task.ref_type == 'OrderIn' || task.ref_type == 'OrderOut')
            innerDom = <TaskLayoutOrderA {...props} />;
          else innerDom = <TaskLayoutA {...props} />;
        } else if (layout == TaskBlockLayout.LAYOUT_B) {
          innerDom = <TaskLayoutB {...props} />;
        } else if (layout == TaskBlockLayout.LAYOUT_ORDER_C) {
          // innerDom = <TaskLayoutOrderC {...props} />;
          innerDom = <TaskLayoutOrderA {...props} />;
        } else {
          innerDom = <TaskLayoutC {...props} />;
        }
        const styles = { ...(providedIn.draggableProps.style || {}) };

        // We set background if task has any active sub group
        if (sn(task.sub_task_groups_count) > 0 && !task.sub_task_groups_count_hidden) {
          styles.backgroundColor = getCodeValue(DictCode.PARENT_TASK_BG) ?? undefined;
        }

        if (task.users?.length && task.users?.[0].initials == initialState?.currentUser?.initials) {
          styles.backgroundColor = '#fff7e6';
        }

        // If assigners exist, we override background as orange.
        if (task.settings?.bgColor) {
          styles.backgroundColor = task.settings?.bgColor;
        }

        return (
          <div
            className={`task ${snapshotIn.isDragging ? ` t-dragging` : ``}`}
            onClick={(e) => {
              e.stopPropagation();
              if (task.ref_type == 'OrderOut') {
                window.open(
                  urlFull('/order-out-detail/' + task.ref_id + '?task_id=' + task.id),
                  '_blank',
                );
              } else {
                props.onTaskClick?.(task, props.droppableId, index, e, TaskClickAction.update);
              }
            }}
            ref={providedIn.innerRef}
            {...providedIn.draggableProps}
            {...providedIn.dragHandleProps}
            style={styles}
          >
            {innerDom}
          </div>
        );
      }}
    </Draggable>
  );
};

export default Task;
