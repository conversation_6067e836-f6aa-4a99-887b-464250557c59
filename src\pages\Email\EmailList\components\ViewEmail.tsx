import Util from '@/util';
import { LinkOutlined, MailOutlined } from '@ant-design/icons';
import type { ProDescriptionsActionType } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import { message, Popconfirm, Space } from 'antd';
import { useRef } from 'react';
import { unlinkEmailToTask } from '@/services/app/Task/task';

export type ViewEmailProps = {
  email?: Partial<API.Email>;
  //   emailId?: number;
  task_id?: number;
  unlinkCallback?: () => void;
};

const ViewEmail: React.FC<ViewEmailProps> = (props) => {
  const { email, task_id, unlinkCallback } = props;
  const actionRef = useRef<ProDescriptionsActionType>();
  return (
    <>
      <div className="text-sm" style={{ right: 24, top: 48, position: 'absolute' }}>
        {Util.dtToDMYHHMM(email?.date)}
      </div>
      <ProDescriptions
        title={
          <div>
            <MailOutlined className="c-blue" />
            &nbsp;&nbsp;
            {email?.subject}
          </div>
        }
        bordered
        actionRef={actionRef}
        column={2}
        extra={
          <>
            <Space style={{ marginRight: 32, paddingLeft: 32 }}>
              {task_id && (
                <>
                  <Popconfirm
                    className="cursor-pointer c-red"
                    title={<>Are you sure you want to unlink?</>}
                    okText="Yes"
                    cancelText="No"
                    overlayStyle={{ width: 300 }}
                    onConfirm={async () => {
                      const hide = message.loading('Unlinking the Email to selected tasks...', 0);
                      unlinkEmailToTask(email?.id, { task_ids: [task_id] })
                        .then((newEmail) => {
                          message.success('Unlinked successfully.');
                          unlinkCallback?.();
                        })
                        .catch((e) => Util.error(e))
                        .finally(() => hide());
                    }}
                  >
                    <LinkOutlined title="Unlink" />
                  </Popconfirm>
                </>
              )}
            </Space>
          </>
        }
        request={async () => {
          //   return getEmail(emailId);
          return { data: email };
        }}
      >
        <ProDescriptions.Item label="From" dataIndex="sender" span={2} />
        <ProDescriptions.Item label="To" dataIndex="receiver" span={2} />
        <ProDescriptions.Item span={2}>
          <div
            dangerouslySetInnerHTML={{
              __html: email?.text_html ? email?.text_html : Util.nl2br(email?.text_plain),
            }}
          />
        </ProDescriptions.Item>
      </ProDescriptions>
    </>
  );
};

export default ViewEmail;
