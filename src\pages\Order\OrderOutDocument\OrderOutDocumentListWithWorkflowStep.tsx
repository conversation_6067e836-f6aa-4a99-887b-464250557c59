import { LinkOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, message, Drawer, Row, Col, Typo<PERSON>, Card } from 'antd';
import React, { useState, useRef, useEffect, useMemo } from 'react';
import { <PERSON><PERSON>ontainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType, ProColumnType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import UpdateForm from '../OrderOut/components/UpdateForm';

import Util, { sn, urlFull } from '@/util';
import CreateForm from '../OrderOut/components/CreateForm';
import { getOrderOutDocumentList } from '@/services/app/Order/order-out';
import { DEFAULT_PER_PAGE_PAGINATION, DictCode } from '@/constants';
import { useModel } from 'umi';
import { getTaskGroupACList } from '@/services/app/Task/task-group';
import EditableCell from '@/components/EditableCell';
import { updateTaskOrders } from '@/services/app/Task/task';
import type { ProFormInstance } from '@ant-design/pro-form';
import ProForm, {
  ProFormCheckbox,
  ProFormGroup,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-form';
import { handleRemove } from '../OrderOut';
import { FileCategorySuffixOptions } from '@/pages/FolderViewer/MiscFileBrowser';
import SFileIcon from '@/components/SFileIcon';
import { useSupplierOptions } from '@/hooks/useSupplierOptions';
import { useCustomerOptions } from '@/hooks/useCustomerOptions';

const OrderOutDocumentListWithWorkflowStep: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();

  const { getCodeValue } = useModel('app-settings');
  const { taskGroups, setTaskGroups } = useModel('task-group');

  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.OrderOut>();
  const [selectedRowsState, setSelectedRows] = useState<API.OrderOut[]>([]);

  const { supplierOptions, searchSupplierOptions } = useSupplierOptions({ existInOrderOut: true });
  const { customerOptions, searchCustomerOptions } = useCustomerOptions({ existInOrderOut: true });

  // Order Out's task group info
  const orderOutTaskGroup = useMemo<API.TaskGroup | undefined>(() => {
    return taskGroups.find((x) => x.code == 'OrderOut');
  }, [taskGroups]);

  // Order Out's task group ID.
  const taskGroupId = sn(orderOutTaskGroup?.id);
  /* const wfStepIds = useMemo(() => {
    return (
      orderOutTaskGroup?.settings?.wfStepIds?.split(',')?.map((x) => sn(x.trim().split(':')[0])) ??
      []
    );
  }, [orderOutTaskGroup?.settings?.wfStepIds]); */

  /* const wfStepIdsWidthMap = useMemo(() => {
    const map: any = {};
    const arr = orderOutTaskGroup?.settings?.wfStepIds?.split(',') || [];
    for (const x of arr) {
      const tmp = x.trim().split(':');
      const id = sn(tmp[0]);
      map[id] = sn(tmp[1] ?? 100);
    }
    return map;
  }, [orderOutTaskGroup?.settings?.wfStepIds]);

  const [steps, setSteps] = useState<API.SysWorkflowStep[]>([]); */

  /* useEffect(() => {
    if (wfStepIds?.length) {
      getSysWorkflowStepList({ pageSize: 500, ids: wfStepIds }).then((res) => {
        setSteps(res.data);
      });
    } else {
      setSteps([]);
    }
  }, [wfStepIds]); */

  const columnsBase: ProColumns<API.OrderOut>[] = [
    {
      title: 'Order No',
      dataIndex: 'order_no',
      sorter: true,
      width: 80,
      defaultSortOrder: 'descend',
      render: (dom, record) => {
        return (
          <a
            href={urlFull(`/order-out-detail/${record.order_no}?task_id=${record.task?.id}`)}
            target="_blank"
            rel="noreferrer"
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: 'Supplier',
      dataIndex: ['supplier_obj', 'name'],
      hideInForm: true,
      width: 170,
    },
    {
      title: 'Customer',
      dataIndex: 'customer',
      hideInForm: true,
      width: 170,
    },
    {
      title: 'Lotus Notes ID',
      dataIndex: 'lotus_notes_id',
      hideInForm: true,
      ellipsis: true,
      width: 40,
      render: (dom, record) =>
        record.lotus_notes_id ? (
          <Row wrap={false}>
            <Col flex={'auto'}>
              <Typography.Text ellipsis> {record.lotus_notes_id}</Typography.Text>
            </Col>
            <Col flex="0 0 40px">
              <a
                href={(getCodeValue(DictCode.LOTUS_PATH) ?? '').replace(
                  '{lotusNotesId}',
                  record.lotus_notes_id,
                )}
                title="Open Lotus link"
              >
                L
              </a>
              <a
                href={urlFull('/order-out-detail/lotus/' + record.lotus_notes_id)}
                target="_blank"
                rel="noreferrer"
                title="Open order out detail"
                style={{ marginLeft: 8 }}
              >
                <LinkOutlined />
              </a>
            </Col>
          </Row>
        ) : undefined,
    },
    {
      title: 'Description',
      dataIndex: 'desc',
      hideInForm: true,
      ellipsis: true,
      width: 250,
    },
    {
      title: 'Task',
      dataIndex: ['task', 'title'],
      hideInForm: true,
      ellipsis: true,
      width: 150,
    },
    {
      title: 'Block',
      dataIndex: ['task', 'task_blocks'],
      hideInForm: true,
      ellipsis: true,
      width: 120,
      tooltip: 'Click to edit',
      render(dom, record) {
        const blockId = record.task?.task_blocks?.[0]?.id || 0;
        return (
          <EditableCell
            dataType="select"
            defaultValue={blockId}
            convertValue={(value) => `${value ?? ''}`}
            request={async (params) => {
              return Promise.resolve(
                orderOutTaskGroup?.task_blocks?.map((x) => ({
                  value: x.id,
                  label: `${x.name}`,
                })) || [],
              );
            }}
            isTabSelectable
            triggerUpdate={function (
              value: any,
              cancelEdit?: (() => void) | undefined,
            ): Promise<void> {
              if (!blockId) {
                cancelEdit?.();
                return Promise.resolve();
              }
              if (!value) {
                message.error('Please select block');
                cancelEdit?.();
                return Promise.resolve();
              }

              const hide = message.loading('Moving a task...', 0);
              return updateTaskOrders(
                sn(record.task?.id),
                0,
                {
                  source: { droppableId: `${blockId}`, index: 0 },
                  destination: { droppableId: `${value}`, index: 0 },
                  reason: 'DROP',
                  combine: undefined,
                  mode: 'FLUID',
                  draggableId: '',
                  type: '',
                },
                taskGroupId,
              )
                .then((res) => {
                  cancelEdit?.();
                  actionRef.current?.reload();
                })
                .catch((reason) => {
                  Util.error(reason);
                })
                .finally(() => {
                  hide();
                });
            }}
          >
            {`${record.task?.task_blocks?.[0]?.name} (${record.task?.task_blocks?.[0]?.id})`}
          </EditableCell>
        );
      },
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 70,
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          Edit
        </a>,
      ],
    },
  ];

  const columns: ProColumns<API.OrderOut>[] = [
    ...columnsBase,
    ...FileCategorySuffixOptions.map((x, ind) => {
      return {
        title: x.label,
        dataIndex: `scan_file_${x.value}`,
        className: `${ind === 0 ? 'bl2' : ''}`,
        width: 70,
        render: (dom: any, record: API.OrderOut) => {
          const files = record.scan_files?.filter(
            (file) => file.category === 'order-out-' + x.value,
          );

          return (
            <Row gutter={4}>
              {files?.map((file) => {
                return (
                  <Col key={file.id}>
                    <a
                      href={file.url}
                      target="_blank"
                      rel="noreferrer"
                      title={file.clean_file_name}
                    >
                      <SFileIcon fileName={file.clean_file_name} />
                    </a>
                  </Col>
                );
              })}
            </Row>
          );
        },
      } as ProColumnType<API.OrderOut>;
    }),
    /* ...steps.map(
      (step) =>
        ({
          title: `${step.id} | ${step.desc}`,
          dataIndex: `wfsId_${step.id}`,
          width: wfStepIdsWidthMap[`${step.id}`] ?? 100,
          defaultSortOrder: 'descend',
          render: (dom: any, record: API.OrderOut) => {
            const wfSteps = record.order_out_workflow_steps?.filter((x) => x.step_id == step.id);

            return getWfStepData(wfSteps, step);
          },
        } as ProColumnType<API.OrderOut>),
    ), */
  ];

  useEffect(() => {
    getTaskGroupACList({ with: 'taskBlocks' })
      .then((res) => {
        setTaskGroups(res);
      })
      .catch((e) => Util.error('Failed to load task groups. Please try again'));
  }, [setTaskGroups]);

  useEffect(() => {
    actionRef.current?.reload();
  }, [orderOutTaskGroup]);

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }} bodyStyle={{ paddingBottom: 12 }}>
        <ProForm<API.OrderOut>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          initialValues={Util.getSfValues('sf_order_out_list', {})}
          submitter={{
            submitButtonProps: {
              htmlType: 'submit',
            },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => actionRef.current?.reload(),
            render: (form, dom) => {
              return [...dom];
            },
          }}
        >
          <ProFormText name={'order_no'} label="Order No" width={'xs'} placeholder={'Order No'} />
          <ProFormSelect
            showSearch
            placeholder="Select a supplier"
            options={supplierOptions}
            request={async (params) => {
              return searchSupplierOptions(params) as any;
            }}
            width="sm"
            name="supplier_id"
            label="Supplier"
            fieldProps={{
              dropdownMatchSelectWidth: false,
              onChange(value) {
                actionRef.current?.reload();
              },
            }}
          />
          <ProFormSelect
            showSearch
            placeholder="Select a customer"
            options={customerOptions}
            request={async (params) => {
              return searchCustomerOptions(params) as any;
            }}
            width="sm"
            name="customer_id"
            label="Customer"
            fieldProps={{
              dropdownMatchSelectWidth: false,
              onChange(value) {
                actionRef.current?.reload();
              },
            }}
          />

          <ProFormSelect
            name={'block_id'}
            options={
              orderOutTaskGroup?.task_blocks?.map((x) => ({
                value: x.id,
                label: `${x.name}`,
              })) || []
            }
            width={'sm'}
            label="Board block"
            placeholder="Board block"
            fieldProps={{
              onChange(value, option) {
                actionRef.current?.reload();
              },
            }}
          />
          <ProFormGroup style={{ marginTop: 8 }} size={'small'}>
            {FileCategorySuffixOptions.map((x, ind) => {
              return (
                <ProFormRadio.Group
                  key={x.value}
                  name={'exist_file_order-out-' + x.value}
                  label={x.label}
                  fieldProps={{
                    onChange: () => actionRef.current?.reload(),
                    buttonStyle: 'outline',
                    optionType: 'button',
                    size: 'small',
                  }}
                  initialValue={''}
                  options={[
                    { value: 'yes', label: 'Yes' },
                    { value: 'no', label: 'No' },
                    { value: '', label: 'Any' },
                  ]}
                />
              );
            })}
          </ProFormGroup>
        </ProForm>
      </Card>

      <ProTable<API.OrderOut, API.PageParams>
        headerTitle={'Order Out list'}
        actionRef={actionRef}
        rowKey="order_no"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        size="small"
        search={false}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(
            Util.getSfValues('sf_order_out_doc_list_p')?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION,
          ),
        }}
        params={{
          with: 'task,task.taskBlocks,wfStepColumns,supplier,customer,scanFiles',
        }}
        request={async (params, sort, filter) => {
          if (!taskGroupId) return Promise.resolve([]);

          const sfValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues('sf_order_out_doc_list', sfValues);
          Util.setSfValues('sf_order_out_doc_list_p', params);

          const newParams = { ...params, taskGroupId, ...sfValues };

          return getOrderOutDocumentList(newParams, sort, filter);
        }}
        onRequestError={(err) => Util.error(err)}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        scroll={{ x: 800 }}
        columnEmptyText=""
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              chosen{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{' '}
              OrderOut &nbsp;&nbsp;
            </div>
          }
        >
          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            Batch deletion
          </Button>
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.order_no && (
          <ProDescriptions<API.OrderOut>
            column={2}
            title={currentRow?.order_no}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.order_no,
            }}
            columns={columns as ProDescriptionsItemProps<API.OrderOut>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default OrderOutDocumentListWithWorkflowStep;
