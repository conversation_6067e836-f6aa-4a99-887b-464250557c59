import {
  BgColorsOutlined,
  BookOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  PlusOutlined,
  SelectOutlined,
  SwitcherOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { message } from 'antd';
import { Popconfirm } from 'antd';
import { Col } from 'antd';
import { Modal } from 'antd';
import { Dropdown, Menu } from 'antd';
import { useMemo, useState } from 'react';
import { useModel } from 'umi';
import { TaskClickAction } from '../..';
import CreateForm from '../CreateForm';
import type { TaskProps } from '../Task';
import _ from 'lodash';
import SubTaskList from '../SubTaskList';
import GroupAssignmentForm from '../GroupAssignmentForm';
import Util, { sn } from '@/util';
import OrderOutHeadAssignmentForm from '../OrderOutHeadAssignmentForm';
import TaskFlags from './TaskFlags';
import { GroupOutlined, MailOutlined, UngroupOutlined } from '@ant-design/icons/lib/icons';
import EmailLinkingForm from '../EmailLinkingForm';
import { unlinkEmailToTask } from '@/services/app/Task/task';
import MasterTaskSelectionModal from '../MasterTaskSelectionModal';

type TaskActionsProps = TaskProps;

const TaskActions: React.FC<TaskActionsProps> = (props) => {
  const { task, index, droppableId, onTaskClick, groupId } = props;
  const { getBlock, taskGroups, getTaskGroupInModel } = useModel('task-group');
  const blockInfo = getBlock(sn(droppableId));
  const isSubGroup = !!getTaskGroupInModel(groupId)?.task_id;

  // sub task
  const [subCreateModalVisible, setSubCreateModalVisible] = useState<boolean>(false);
  const [subTasksModalVisible, handleSubTasksModalVisible] = useState<boolean>(false);

  // group assignment of a task
  const [groupAssignModalVisible, setGroupAssignModalVisible] = useState<boolean>(false);
  const [headAssignModalVisible, setHeadAssignModalVisible] = useState<boolean>(false);

  // emailLinkingModal
  const [openEmailLinkingModal, setOpenEmailLinkingModal] = useState<boolean>(false);

  // Select task modal for moving into sub task
  const [openTaskSelectModal, setOpenTaskSelectModal] = useState<boolean>(false);

  const menu = useMemo(() => {
    const handleMenuClick: MenuProps['onClick'] = async (e) => {
      // console.log(e.key, e.keyPath);
      e.domEvent.stopPropagation();

      // 2 level menu items
      if (e.keyPath[1] == 'bgColor') {
        let color = 'white';
        if (e.keyPath[0] == 'bgColorGrey') {
          color = '#f5f5f5';
        } else if (e.keyPath[0] == 'bgColorOrange') {
          color = '#fff7e6';
        } else if (e.keyPath[0] == 'removeBg') {
          color = '';
        }

        props?.onTaskClick?.(
          props.task,
          props.droppableId,
          props.index,
          e,
          TaskClickAction.updatePartial,
          { settings: { bgColor: color } },
        );
        return;
      } else if (e.keyPath[1] == 'addUser') {
        props?.onTaskClick?.(
          props.task,
          props.droppableId,
          props.index,
          e,
          TaskClickAction.addUser,
          { userIds: e.keyPath[0] },
        );
        return;
      }

      if (e.key.includes('deleteUser')) {
        props?.onTaskClick?.(
          props.task,
          props.droppableId,
          props.index,
          e,
          TaskClickAction.deleteUser,
          { userIds: e.key.replace('deleteUser', '') },
        );
        return;
      }
      if (e.key.includes('moveTo')) {
        props?.onTaskClick?.(
          props.task,
          props.droppableId,
          props.index,
          e,
          TaskClickAction.moveTo,
          { destBlockId: e.key.replace('moveTo', '') },
        );
        return;
      }

      switch (e.key) {
        case 'delete':
          // ignore action here.
          break;
        case 'desable':
          // ignore action here.
          break;
        case 'open-sub':
          handleSubTasksModalVisible(true);
          break;
        case 'create-sub':
          setSubCreateModalVisible(true);
          break;
        case TaskClickAction.openSubGroup:
          props?.onTaskClick?.(
            props.task,
            props.droppableId,
            props.index,
            e,
            TaskClickAction.openSubGroup,
          );
          break;
        case TaskClickAction.reopenSubGroup:
          props?.onTaskClick?.(
            props.task,
            props.droppableId,
            props.index,
            e,
            TaskClickAction.reopenSubGroup,
          );
          break;
        case 'update':
          props?.onTaskClick?.(
            props.task,
            props.droppableId,
            props.index,
            e,
            TaskClickAction.update,
          );
          break;
        case 'linkToEmail':
          setOpenEmailLinkingModal(true);
          break;
        case 'unlinkingEmail':
          break;
        case TaskClickAction.sendToSub:
          setOpenTaskSelectModal(true);
          break;
        case 'create-sub-group':
          props?.onTaskClick?.(
            props.task,
            props.droppableId,
            props.index,
            e,
            TaskClickAction.createSubGroup,
          );
          break;
        case 'groupAssignment':
          setGroupAssignModalVisible(true);
          break;
        case 'assign-to-head':
          setHeadAssignModalVisible(true);
          break;
      }
    };

    const menuItems = [];
    const inOrderOutGroup = task.ref_type == 'OrderOut';
    const inOrderInGroup = task.ref_type == 'OrderIn';
    const inOrderGroup = inOrderInGroup || inOrderOutGroup;

    if (blockInfo && blockInfo.settings?.panelMenus?.length) {
      blockInfo.settings?.panelMenus.forEach((x, ind) => {
        if (x.destBlockId) {
          menuItems.push({
            label: <span>{`Move to ${x.label} #${x.destBlockId}`}</span>,
            key: `moveTo${x.destBlockId}`,
          });
        }
      });
    }

    if (inOrderOutGroup) {
      const groupInfo = (taskGroups || []).find((x) => x.id == blockInfo?.group_id);

      if (groupInfo && (groupInfo.code == 'OrderOut' || groupInfo.code == 'OrderOutHead')) {
        menuItems.push({
          label: (
            <span>
              <BookOutlined /> {task.order_out_head_id ? 'Change HeadOrder' : 'Assign to HeadOrder'}
            </span>
          ),
          key: 'assign-to-head',
        });
      }
    }

    if (!inOrderGroup) {
      // const groupInfo = (taskGroups || []).find((x) => x.id == blockInfo?.group_id);

      // if (groupInfo && !groupInfo.task_id) {
      if (props.task.sub_task_groups_count) {
        if (props.task.sub_task_groups_count_hidden) {
          menuItems.push({
            label: (
              <span className="c-lightgrey">
                <SwitcherOutlined /> Re-Open sub tasks group
              </span>
            ),
            key: TaskClickAction.reopenSubGroup,
          });
        } else {
          menuItems.push({
            label: (
              <span>
                <SwitcherOutlined /> Open sub tasks group
              </span>
            ),
            key: TaskClickAction.openSubGroup,
          });
        }
      } else {
        menuItems.push({
          label: (
            <span>
              <PlusOutlined /> Creat sub task group
            </span>
          ),
          key: 'create-sub-group',
        });
      }
      // }
    }

    if (!inOrderGroup) {
      menuItems.push({ type: 'divider' });

      if (props.task.children_count) {
        menuItems.push({
          label: (
            <span>
              <SwitcherOutlined /> Open sub tasks
            </span>
          ),
          key: 'open-sub',
        });
      }

      menuItems.push({
        label: (
          <span className={props.task.children_count ? 'c-green' : ''}>
            <PlusOutlined /> Creat sub task
          </span>
        ),
        key: 'create-sub',
      });
    }

    /* menuItems.push({ type: 'divider' });

    if (task.users?.length) {
      menuItems.push({
        label: (
          <span className="c-lightred">
            <UserOutlined /> Delete user - {task.users[0].initials}
          </span>
        ),
        key: `deleteUser${task.users[0].user_id}`,
      });
    } else {
      menuItems.push({
        label: (
          <span>
            <UserOutlined /> Add user
          </span>
        ),
        key: 'addUser',
        children: appSettings.users?.map((u) => ({
          key: u.user_id,
          label: u.initials ?? u.user_id,
        })),
      });
    } */
    /* menuItems.push({
      label: (
        <span className="c-lightred">
          <DeleteOutlined /> <DeleteTaskPopConfirm {...props} title="Delete" />
        </span>
      ),
      key: 'delete',
    }); */

    menuItems.push({ type: 'divider' });

    menuItems.push({
      label: (
        <span>
          <BgColorsOutlined /> Background
        </span>
      ),
      key: 'bgColor',
      children: [
        {
          key: 'bgColorWhite',
          label: 'White',
        },
        {
          key: 'bgColorGrey',
          label: 'Grey',
        },
        {
          key: 'bgColorOrange',
          label: 'Orange',
        },
        {
          type: 'divider',
        },
        {
          key: 'removeBg',
          label: <span className="c-lightred">Remove Color</span>,
        },
      ],
    });

    /* if (task.ref_type == 'OrderOut') {
      menuItems.push({ type: 'divider' });
      menuItems.push({
        label: 'Edit task',
        key: 'update',
        icon: <EditOutlined />,
      });
    } */

    // We can only assign group in the 1st level task.
    if (!isSubGroup && !task.parent_id) {
      menuItems.push({
        label: `Multiple Assignment${
          sn(task.task_blocks_count) > 1 ? ` (${task.task_blocks_count})` : ''
        }`,
        key: 'groupAssignment',
        icon: <SelectOutlined />,
      });
    }

    // Sending to sub / master
    if (!inOrderGroup) {
      if (!isSubGroup && !task.parent_id) {
        menuItems.push({ type: 'divider' });
        menuItems.push({
          key: TaskClickAction.sendToSub,
          label: 'Move to sub task...',
          icon: <GroupOutlined />,
        });
      } else if (isSubGroup && !task.parent_id) {
        const groupInfo = (taskGroups || []).find((x) => x.id == blockInfo?.group_id);
        if (groupInfo?.task_id) {
          const destTaskId = groupInfo?.task_id;
          menuItems.push({ type: 'divider' });
          menuItems.push({
            label: (
              <Popconfirm
                title={<>Are you sure you want to move to main board?</>}
                okText="Yes"
                cancelText="No"
                overlayStyle={{ width: 300 }}
                onConfirm={async (e) => {
                  onTaskClick?.(task, droppableId, index, e, TaskClickAction.sendToMaster, {
                    srcTaskId: task.id,
                    destTaskId: destTaskId,
                  });
                }}
              >
                <div className="">
                  <UngroupOutlined /> Move to main board
                </div>
              </Popconfirm>
            ),
            key: TaskClickAction.sendToMaster,
          });
        }
      }
    }

    if (!task.email_ref) {
      menuItems.push({
        label: `Link to Email`,
        key: 'linkToEmail',
        icon: <MailOutlined />,
      });
    } else {
      menuItems.push({
        label: (
          <Popconfirm
            className="cursor-pointer"
            title={<>Are you sure you want to unlink?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ width: 300 }}
            onConfirm={async () => {
              const hide = message.loading('Unlinking the Email to selected tasks...', 0);
              unlinkEmailToTask(task.email_ref?.id, { task_ids: [task?.id] })
                .then((newEmail) => {
                  message.success('Unlinked successfully.');
                  props?.onTaskClick?.(
                    props.task,
                    props.droppableId,
                    props.index,
                    null,
                    TaskClickAction.unlinkingEmail,
                  );
                })
                .catch((e) => Util.error(e))
                .finally(() => hide());
            }}
          >
            <span className="c-lightred">Unlink email</span>
          </Popconfirm>
        ),
        key: 'unlinkingEmail',
        icon: <MailOutlined className="c-lightred" />,
      });
    }

    menuItems.push({ type: 'divider' });
    if (task.visible == 0) {
      menuItems.push({
        label: (
          <Popconfirm
            title={<>Are you sure you want to show?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ width: 300 }}
            onConfirm={async (e) => {
              onTaskClick?.(task, droppableId, index, e, TaskClickAction.visible);
            }}
          >
            <div className="c-green">
              <EyeOutlined /> Show task
            </div>
          </Popconfirm>
        ),
        key: 'delete',
      });
    } else {
      menuItems.push({
        label: (
          <Popconfirm
            title={<>Are you sure you want to hide?</>}
            okText="Yes"
            cancelText="No"
            overlayStyle={{ width: 300 }}
            onConfirm={async (e) => {
              onTaskClick?.(task, droppableId, index, e, TaskClickAction.invisible);
            }}
          >
            <div className="c-lightred">
              <EyeInvisibleOutlined /> Hide task
            </div>
          </Popconfirm>
        ),
        key: 'delete',
      });
    }

    // Hide sub task group
    if (!inOrderGroup) {
      if (
        task.sub_task_groups?.[0]?.id &&
        task.sub_task_groups_count &&
        !task.sub_task_groups_count_hidden
      ) {
        menuItems.push({
          label: (
            <Popconfirm
              title={<>Are you sure you want to hide sub group?</>}
              okText="Yes"
              cancelText="No"
              overlayStyle={{ width: 300 }}
              onConfirm={async (e) => {
                onTaskClick?.(task, droppableId, index, e, TaskClickAction.hideSubGroup);
              }}
            >
              <div className="c-lightred">
                <EyeInvisibleOutlined /> Hide sub task group
              </div>
            </Popconfirm>
          ),
          key: TaskClickAction.hideSubGroup,
        });
      }
    }

    return <Menu onClick={handleMenuClick} items={menuItems as any} />;
  }, [task, blockInfo, isSubGroup, props, taskGroups, onTaskClick, droppableId, index]);

  return (
    <Col onClick={(e) => e.stopPropagation()} style={{ display: 'flex', alignItems: 'center' }}>
      <TaskFlags {...props} />
      {!task.ui_type && (
        <Dropdown.Button
          size="small"
          overlay={menu}
          buttonsRender={([leftButton, rightButton]) => [undefined, rightButton]}
          trigger={['click']}
        />
      )}
      {subTasksModalVisible && task.id && (
        <Modal
          title={
            <>
              <SwitcherOutlined className="c-blue4" /> Sub tasks in #{task.id}
            </>
          }
          bodyStyle={{ minHeight: 400, paddingTop: 0 }}
          open={subTasksModalVisible}
          footer={false}
          onCancel={() => handleSubTasksModalVisible(false)}
        >
          <SubTaskList
            initialValues={task}
            onTaskClick={onTaskClick}
            handleSubTasksModalVisible={handleSubTasksModalVisible}
            groupId={groupId}
            droppableId={droppableId}
            index={index}
          />
        </Modal>
      )}

      {subCreateModalVisible && (
        <CreateForm
          modalVisible={subCreateModalVisible}
          blockId={task?.block_id}
          parentId={task?.id}
          handleModalVisible={setSubCreateModalVisible}
          onSubmit={async (value) => {
            onTaskClick?.(
              task as any,
              droppableId,
              props.index,
              undefined,
              TaskClickAction.createSubCallback,
              { subTask: value },
            ).finally(() => {
              setSubCreateModalVisible(false);
              handleSubTasksModalVisible(true);
            });
          }}
        />
      )}

      {groupAssignModalVisible && (
        <GroupAssignmentForm
          modalVisible={groupAssignModalVisible}
          task={{
            id: task.id,
          }}
          handleModalVisible={setGroupAssignModalVisible}
          onSubmit={async (value) => {
            onTaskClick?.(
              task as any,
              droppableId,
              props.index,
              undefined,
              TaskClickAction.groupAssignmentCallback,
              { ...value, old_block_id: task.block_id }, // task.block_id is an old data!!!
            ).finally(() => {
              setGroupAssignModalVisible(false);
            });
          }}
        />
      )}

      {headAssignModalVisible && (
        <OrderOutHeadAssignmentForm
          modalVisible={headAssignModalVisible}
          task={task}
          handleModalVisible={setHeadAssignModalVisible}
          onSubmit={async (value) => {
            onTaskClick?.(
              task as any,
              droppableId,
              props.index,
              undefined,
              TaskClickAction.orderOutHeadAssignmentCallback,
              { ...value },
            ).finally(() => {
              setHeadAssignModalVisible(false);
            });
          }}
        />
      )}

      {openEmailLinkingModal && (
        <EmailLinkingForm
          modalVisible={openEmailLinkingModal}
          handleModalVisible={setOpenEmailLinkingModal}
          task={task}
          reloadParent={(newEmail?: API.Email) => {
            if (newEmail) {
              onTaskClick?.(
                task as any,
                droppableId,
                props.index,
                undefined,
                TaskClickAction.linkingEmailCallback,
                newEmail,
              ).finally(() => {});
            }
          }}
        />
      )}

      {openTaskSelectModal && (
        <MasterTaskSelectionModal
          modalVisible={openTaskSelectModal}
          handleModalVisible={setOpenTaskSelectModal}
          task={task}
          reloadParent={(newTask?: API.Task) => {
            if (newTask) {
              onTaskClick?.(
                task as any,
                droppableId,
                props.index,
                undefined,
                TaskClickAction.sendToSubCallback,
                newTask,
              ).finally(() => {});
            }
          }}
        />
      )}
    </Col>
  );
};

export default TaskActions;
