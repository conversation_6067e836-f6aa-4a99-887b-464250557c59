import type { Dispatch, SetStateAction } from 'react';
import { useEffect } from 'react';
import { useState } from 'react';
import React, { useRef } from 'react';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormRadio } from '@ant-design/pro-form';
import { ProFormTextArea } from '@ant-design/pro-form';
import { ModalForm } from '@ant-design/pro-form';
import { checkExistenceData, createLoSusaBulk } from '@/services/app/LexOffice/lo-susa';
import { Alert, message, Spin } from 'antd';
import Util from '@/util';
import SDatePicker from '@/components/SDatePicker';
import { TransactionType, TransactionTypeOptions } from '@/constants';

export type FormValueType = {
  data?: string;
  date?: string;
  category?: string;
};

const handleAdd = async (dataParam: FormValueType) => {
  const hide = message.loading('Importing...', 0);
  const data = { ...dataParam };
  try {
    await createLoSusaBulk(data);
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type CreateFormProps = {
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: FormValueType) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const { modalVisible, handleModalVisible, onSubmit } = props;
  const [loading, setLoading] = useState<boolean>(false);
  const [existStatus, setExistStatus] = useState<boolean | null>(null);

  const loadExistStatus = (values: FormValueType) => {
    setLoading(true);
    checkExistenceData(values)
      .then((res) => {
        setExistStatus(res);
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    if (modalVisible) {
      const cachedData = Util.getSfValues('f_lo_susa', { category: TransactionType.GL });
      if (!cachedData.category) {
        cachedData.category = TransactionType.GL;
      }
      formRef.current?.setFieldsValue(cachedData);
      loadExistStatus(cachedData);
    }
  }, [modalVisible]);

  return (
    <ModalForm<FormValueType>
      title={'Import SuSa data'}
      formRef={formRef}
      width="700px"
      visible={modalVisible}
      onVisibleChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ flex: '100px' }}
      onValuesChange={(changed, values) => {
        if ((changed.date || changed.category) && values.date && values.category) {
          Util.setSfValues('f_lo_susa', { date: values.date });
          loadExistStatus({ date: values.date, category: values.category });
        } else {
          setExistStatus(null);
        }
      }}
      onFinish={async (formValue) => {
        const success = await handleAdd(formValue);
        if (success) {
          if (formRef.current) formRef.current.setFieldValue('data', '');
          if (onSubmit) await onSubmit(formValue);
        }
      }}
    >
      <SDatePicker
        name="date"
        label="Date"
        fieldProps={{ picker: 'month' }}
        rules={[
          {
            required: true,
            message: 'Date is required',
          },
        ]}
        addonAfter={<Alert message="Old data on this date will be removed." type="warning" />}
      />
      <ProFormRadio.Group
        label="Import Type"
        name="category"
        rules={[
          {
            required: true,
            message: 'Import type is required',
          },
        ]}
        radioType="button"
        fieldProps={{ buttonStyle: 'solid' }}
        options={TransactionTypeOptions}
        addonAfter={
          <Spin spinning={loading}>
            {existStatus !== null ? (
              existStatus ? (
                <div className="c-red">Data exists!</div>
              ) : (
                <div className="c-green">Data does not exists!</div>
              )
            ) : null}
          </Spin>
        }
      />
      <ProFormTextArea
        rules={[
          {
            required: true,
            message: 'Data is required',
          },
        ]}
        fieldProps={{ rows: 10 }}
        name="data"
        label="Data"
      />
    </ModalForm>
  );
};

export default CreateForm;
