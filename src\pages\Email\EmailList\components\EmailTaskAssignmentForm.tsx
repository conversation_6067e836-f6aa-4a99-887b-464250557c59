import type { Dispatch, SetStateAction } from 'react';
import { useState } from 'react';
import React, { useEffect, useRef } from 'react';
import { Button, Card, Col, message, Modal, Row, Tabs } from 'antd';
import type { ProFormInstance } from '@ant-design/pro-form';
import { ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import { addTaskByEmail, getEmailTaskList, linkEmailToTask } from '@/services/app/Task/task';
import Util from '@/util';
import { useModel } from 'umi';
import _ from 'lodash';
import { LinkOutlined, PlusOutlined, SelectOutlined } from '@ant-design/icons';
import EmailTaskList from './EmailTaskList';
import { getTaskGroupACList } from '@/services/app/Task/task-group';

export type FormValueType = Partial<API.Task>;

export type EmailTaskAssignmentFormProps = {
  email?: Partial<API.Email>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  reloadParent?: () => void;
};

const EmailTaskAssignmentForm: React.FC<EmailTaskAssignmentFormProps> = (props) => {
  const { email, modalVisible, reloadParent } = props;
  const { taskGroups, blockInfoMap, setTaskGroups } = useModel('task-group');

  // form
  const formRef = useRef<ProFormInstance>();

  const [blockIds, setBlockIds] = useState<number[]>([]);
  const [refreshTick, setRefreshTick] = useState(0);

  useEffect(() => {
    getTaskGroupACList({ with: 'taskBlocks' })
      .then((res) => {
        setTaskGroups(res);
      })
      .catch(() => Util.error('Failed to load task groups. Please try again'));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setRefreshTick((prev) => prev + 1);
  }, [modalVisible]);

  useEffect(() => {
    if (!props.modalVisible) return;
    if (email) {
      formRef.current?.setFieldsValue({
        title: email.subject,
      });
    }
  }, [props.modalVisible, email]);

  return (
    <Modal
      title={
        <>
          <SelectOutlined className="c-blue" /> Link Tasks into email {`"${email?.subject}"`}
        </>
      }
      width="1200px"
      open={props.modalVisible}
      onCancel={() => props.handleModalVisible(false)}
      footer={false}
    >
      <Row gutter={32} wrap={false}>
        <Col flex={'0 0 800px'}>
          <Row gutter={32}>
            <Col span={24}>
              <ProForm
                formRef={formRef}
                layout="horizontal"
                submitter={false}
                labelCol={{ flex: '0 0 100px' }}
              >
                <Tabs
                  defaultActiveKey="1"
                  style={{ marginBottom: 32 }}
                  items={[
                    <>
                      <LinkOutlined />
                      Link to existing task
                    </>,
                    <>
                      <PlusOutlined />
                      Create new task
                    </>,
                  ].map((Icon, i) => {
                    const tabId = String(i + 1);
                    const children =
                      i == 0 ? (
                        // eslint-disable-next-line react/no-array-index-key
                        <Row key={i}>
                          <Col flex="auto">
                            <ProFormSelect
                              name="task_ids"
                              label="Exsiting Task"
                              required
                              showSearch
                              mode="multiple"
                              debounceTime={200}
                              rules={[
                                {
                                  required: true,
                                  message: 'Task is required',
                                },
                              ]}
                              formItemProps={{ style: { width: 400, marginBottom: 0 } }}
                              request={(params) => {
                                const newParams = {
                                  ...params,
                                  email_id_not: email?.id,
                                  pageSize: 200,
                                };
                                return getEmailTaskList(newParams).then((res) =>
                                  res.data.map((task) => ({
                                    value: task.id,
                                    label: `#${task.id} - ${task.title}`,
                                    data: task,
                                  })),
                                );
                              }}
                            />
                          </Col>
                          <Col flex="1 0 120px">
                            <Button
                              type="primary"
                              onClick={() => {
                                formRef.current?.validateFields([['task_ids']]).then((values) => {
                                  const hide = message.loading(
                                    'Linking the Email to selected tasks...',
                                    0,
                                  );
                                  linkEmailToTask(email?.id, values)
                                    .then((newEmail) => {
                                      message.success('Linked successfully.');
                                      formRef.current?.setFieldValue('task_ids', []);
                                      setRefreshTick((prev) => prev + 1);
                                      reloadParent?.();
                                    })
                                    .catch((e) => Util.error(e))
                                    .finally(() => hide());
                                });
                              }}
                              icon={<LinkOutlined />}
                            >
                              Link to task
                            </Button>
                          </Col>
                        </Row>
                      ) : (
                        // eslint-disable-next-line react/no-array-index-key
                        <Row key={i}>
                          <Col flex="auto">
                            <ProFormSelect
                              name="block_ids"
                              label="Blocks"
                              required
                              mode="multiple"
                              rules={[
                                {
                                  required: true,
                                  message: 'Block is required',
                                },
                              ]}
                              formItemProps={{ style: { width: 400 } }}
                              fieldProps={{
                                onChange: (ids) => {
                                  setBlockIds(ids || []);
                                },
                              }}
                              options={taskGroups?.map((x: API.TaskGroup) => ({
                                value: x.id,
                                label: `${x.name} (${x.code})`,
                                children: x.task_blocks?.map((b) => ({
                                  value: b.id,
                                  label: `${x.code} / ${b.name}`,
                                  disabled:
                                    blockIds?.findIndex(
                                      (id: any) =>
                                        id != b.id && blockInfoMap?.[id]?.group_id == x.id,
                                    ) >= 0,
                                })),
                              }))}
                            />
                            <ProFormText
                              name="title"
                              label="Task Title"
                              formItemProps={{ style: { width: 400 } }}
                              rules={[
                                {
                                  required: true,
                                  message: 'Task Title is required',
                                },
                              ]}
                            />
                            <ProFormTextArea
                              name="desc"
                              label="Description"
                              formItemProps={{ style: { width: 400 } }}
                            />
                          </Col>
                          <Col flex="1 0 120px">
                            <Button
                              type="primary"
                              onClick={() => {
                                formRef.current
                                  ?.validateFields([['title'], ['block_ids'], ['desc']])
                                  .then((values) => {
                                    const hide = message.loading(
                                      'Adding a new task with email info...',
                                      0,
                                    );
                                    addTaskByEmail({ ...values, email_id: email?.id })
                                      .then((newEmail) => {
                                        message.success('Added successfully.');
                                        formRef.current?.setFieldValue('block_ids', []);
                                        setRefreshTick((prev) => prev + 1);
                                        reloadParent?.();
                                      })
                                      .catch((e) => Util.error(e))
                                      .finally(() => hide());
                                  });
                              }}
                              icon={<PlusOutlined />}
                            >
                              Create
                            </Button>
                          </Col>
                        </Row>
                      );

                    return {
                      key: tabId,
                      label: Icon,
                      children: children,
                    };
                  })}
                />
              </ProForm>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <EmailTaskList id={email?.id} refreshTick={refreshTick} reloadParent={reloadParent} />
            </Col>
          </Row>
        </Col>
        <Col flex="1 1 auto">
          <Card style={{ width: '100%', minHeight: '100%' }} type="inner">
            <div
              dangerouslySetInnerHTML={{
                __html: email?.text_html
                  ? email?.text_html
                  : email?.text_plain?.replaceAll('\n', '<br/>') ?? '',
              }}
            />
          </Card>
        </Col>
      </Row>
    </Modal>
  );
};

export default EmailTaskAssignmentForm;
