/* eslint-disable */
import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/fin/detail';

/** GET /api/fin/detail/stat
 * 
 * Get fin details stata all list  
*/
export async function getFinDetailStatList(
    params: API.PageParams & Partial<API.FinDetail>,
    sort?: any,
    filter?: any,
): Promise<API.PaginatedResult<API.FinDetail>> {
    return request<API.BaseResult>(`${urlPrefix}/stat`, {
        method: 'GET',
        params: {
            ...params,
            perPage: params.pageSize,
            page: params.current,
            sort,
            filter,
        },
        paramsSerializer,
        withToken: true,
    }).then((res) => ({
        data: res.message.data,
        success: res.status == 'success',
        total: res.message.pagination.totalRows,
    }));
}