import type { ProFormInstance } from '@ant-design/pro-form';
import { Col, Row } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import type { onTaskClickType, onTaskPanelClickType } from '../..';
import TaskPanel from '../TaskPanel';

/**
 * base props of task group layout
 */
export type TaskGroupLayoutProps = {
  group: API.TaskGroup; // task group
  blocksMap: Record<number, API.TaskBlock[]>;
  tasks: Record<string, API.Task[]>;
  setTasks: Dispatch<SetStateAction<Record<string, API.Task[]>>>;
  onTaskClick: onTaskClickType;
  onTaskPanelClick: onTaskPanelClickType;
  searchFormRef: React.MutableRefObject<ProFormInstance<any> | undefined>;

  // For subgroup, we pass this ID.
  parentGroupId: number;
  parentBlockId: number;
};

export type TaskGroupLayoutDefaultPropsOptions = {
  visible6?: boolean;
};

/**
 * base props of task group layout-Default
 */
export type TaskGroupLayoutDefaultProps = TaskGroupLayoutProps & {
  options?: TaskGroupLayoutDefaultPropsOptions;
};

const TaskGroupLayoutDefault: React.FC<TaskGroupLayoutDefaultProps> = (props) => {
  const {
    blocksMap,
    group,
    tasks,
    setTasks,
    onTaskClick,
    onTaskPanelClick,
    searchFormRef,
    options,
    parentGroupId,
    parentBlockId,
  } = props;
  const { visible6 } = options ?? {};
  const groupId = group?.id;

  return (
    <>
      <Row className="wrap" gutter={[20, 40]}>
        <Col className="left-wrap" span={6}>
          {blocksMap[1] &&
            blocksMap[1].map((block: API.TaskBlock) => (
              <TaskPanel
                block={block}
                key={block.id}
                groupId={groupId}
                droppableId={`${block.id}`}
                tasks={tasks[block.id]}
                setTasks={setTasks}
                onTaskClick={onTaskClick}
                onTaskPanelClick={onTaskPanelClick}
                searchFormRef={searchFormRef}
                parentGroupId={parentGroupId}
                parentBlockId={parentBlockId}
              />
            ))}
          {blocksMap[2] &&
            blocksMap[2].map((block: API.TaskBlock) => (
              <TaskPanel
                block={block}
                key={block.id}
                groupId={groupId}
                droppableId={`${block.id}`}
                tasks={tasks[block.id]}
                setTasks={setTasks}
                onTaskClick={onTaskClick}
                onTaskPanelClick={onTaskPanelClick}
                searchFormRef={searchFormRef}
                parentGroupId={parentGroupId}
                parentBlockId={parentBlockId}
              />
            ))}
        </Col>
        <Col className="middle-wrap" span={visible6 ? 6 : 12}>
          {blocksMap[3] &&
            blocksMap[3].map((block: API.TaskBlock) => (
              <TaskPanel
                block={block}
                key={block.id}
                groupId={groupId}
                droppableId={`${block.id}`}
                tasks={tasks[block.id]}
                setTasks={setTasks}
                onTaskClick={onTaskClick}
                onTaskPanelClick={onTaskPanelClick}
                clsTaskList="middle"
                searchFormRef={searchFormRef}
                parentGroupId={parentGroupId}
                parentBlockId={parentBlockId}
              />
            ))}
        </Col>
        {visible6 && (
          <Col className="middle-wrap" span={6}>
            {blocksMap[6] &&
              blocksMap[6].map((block: API.TaskBlock) => (
                <TaskPanel
                  block={block}
                  key={block.id}
                  groupId={groupId}
                  droppableId={`${block.id}`}
                  tasks={tasks[block.id]}
                  setTasks={setTasks}
                  onTaskClick={onTaskClick}
                  onTaskPanelClick={onTaskPanelClick}
                  clsTaskList="middle"
                  searchFormRef={searchFormRef}
                  parentGroupId={parentGroupId}
                  parentBlockId={parentBlockId}
                />
              ))}
          </Col>
        )}
        <Col className="right-wrap" span={6}>
          {blocksMap[4] &&
            blocksMap[4].map((block: API.TaskBlock) => (
              <TaskPanel
                block={block}
                key={block.id}
                groupId={groupId}
                droppableId={`${block.id}`}
                tasks={tasks[block.id]}
                setTasks={setTasks}
                onTaskClick={onTaskClick}
                onTaskPanelClick={onTaskPanelClick}
                searchFormRef={searchFormRef}
                parentGroupId={parentGroupId}
                parentBlockId={parentBlockId}
              />
            ))}
          {blocksMap[5] &&
            blocksMap[5].map((block: API.TaskBlock) => (
              <TaskPanel
                block={block}
                key={block.id}
                groupId={groupId}
                droppableId={`${block.id}`}
                tasks={tasks[block.id]}
                setTasks={setTasks}
                onTaskClick={onTaskClick}
                onTaskPanelClick={onTaskPanelClick}
                searchFormRef={searchFormRef}
                parentGroupId={parentGroupId}
                parentBlockId={parentBlockId}
              />
            ))}
        </Col>
        {/* <div className='float-wrap'>
            <TaskPanel
            groupId={groupId}
              droppableId="6"
              tasks={tasks[6]}
              setTasks={setTasks}
              onTaskClick={onTaskClick}
              onTaskPanelClick={onTaskPanelClick}
            />
          </div> */}
      </Row>
    </>
  );
};

export default TaskGroupLayoutDefault;
