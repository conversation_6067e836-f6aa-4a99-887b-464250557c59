import { PlusOutlined } from '@ant-design/icons';
import { But<PERSON>, message, Drawer } from 'antd';
import React, { useState, useRef } from 'react';
import { PageContainer, FooterToolbar } from '@ant-design/pro-layout';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import ProDescriptions from '@ant-design/pro-descriptions';
import UpdateForm from './components/UpdateForm';

import Util from '@/util';
import CreateForm from './components/CreateForm';
import { getAddressList, deleteAddress } from '@/services/app/Sys/address';
import { DEFAULT_PER_PAGE_PAGINATION } from '@/constants';
import SFooterToolbarExtra from '@/components/Table/SFooterToolbarExtra';
import BatchDeleteAction from '@/components/Table/BatchDeleteAction';

/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.Address[]) => {
  const hide = message.loading('Deleting');
  if (!selectedRows) return true;

  try {
    await deleteAddress({
      id: selectedRows.map((row) => row.id),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    Util.error('Delete failed, please try again!', error);
    return false;
  }
};

const AddressList: React.FC = () => {
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.Address>();
  const [selectedRowsState, setSelectedRows] = useState<API.Address[]>([]);

  const columns: ProColumns<API.Address>[] = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 40,
      align: 'center',
      fixed: 'left',
      render: (item, record, index, action) => {
        return (
          ((action?.pageInfo?.current ?? 1) - 1) *
            (action?.pageInfo?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION) +
          index +
          1
        );
      },
    },
    {
      title: 'Company',
      dataIndex: 'company',
      sorter: true,
      width: 150,
    },
    {
      title: 'Name',
      dataIndex: 'name',
      sorter: true,
      width: 150,
      render: (dom, record) => (record.firstname ?? '') + ' ' + (record.lastname ?? ''),
    },
    {
      title: 'Street',
      dataIndex: 'street',
      sorter: true,
      width: 250,
      className: 'bl2 b-gray',
      render: (dom, record) => (record.street ?? '') + ' ' + (record.street2 ?? ''),
    },
    {
      title: 'City',
      dataIndex: 'city',
      sorter: true,
      width: 120,
    },
    {
      title: 'Region',
      dataIndex: 'region',
      sorter: true,
      width: 200,
    },
    {
      title: 'Zip',
      dataIndex: 'postcode',
      sorter: true,
      width: 100,
      ellipsis: true,
    },
    {
      title: 'Country',
      dataIndex: ['country', 'name'],
      sorter: true,
      width: 150,
      ellipsis: true,
    },
    {
      title: 'Created on',
      dataIndex: ['created_on'],
      sorter: true,
      hideInSearch: true,
      hideInForm: true,
      hideInTable: true,
      width: 150,
      ellipsis: true,
      defaultSortOrder: 'descend',
    },
    {
      title: 'ID',
      dataIndex: 'id',
      colSize: 1,
      search: false,
      className: 'c-grey',
    },
    {
      title: 'Option',
      dataIndex: 'option',
      valueType: 'option',
      width: 120,
      fixed: 'right',
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          Edit
        </a>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.Address, API.PageParams>
        headerTitle={'Address list'}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        scroll={{ x: 800 }}
        columnEmptyText=""
        search={{
          labelWidth: 'auto',
          searchText: 'Search',
          span: 6,
          filterType: 'query',
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        pagination={{
          showSizeChanger: true,
          pageSize: DEFAULT_PER_PAGE_PAGINATION,
        }}
        request={getAddressList}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <SFooterToolbarExtra
              title="address"
              actionRef={actionRef}
              selectedRowsState={selectedRowsState}
            />
          }
        >
          <BatchDeleteAction
            title="address"
            onConfirm={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          />
        </FooterToolbar>
      )}

      <CreateForm
        modalVisible={createModalVisible}
        handleModalVisible={handleModalVisible}
        onSubmit={async (value) => {
          handleModalVisible(false);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      <UpdateForm
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        initialValues={currentRow || {}}
        onSubmit={async (value) => {
          setCurrentRow(undefined);

          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);

          if (!showDetail) {
            setCurrentRow(undefined);
          }
        }}
      />

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions<API.Address>
            column={2}
            title={currentRow?.id}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<API.Address>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default AddressList;
